{"name": "ota-order-processing-system", "version": "1.0.0", "description": "OTA订单处理系统 - GoMyHire Integration", "main": "index.html", "scripts": {"build": "node deployment/validate-deployment.js && echo 'Build completed successfully - Static site ready for deployment'", "start": "echo 'Static site deployment'", "dev": "echo 'Development server - serve files directly'", "test": "echo 'Running tests...'", "deploy": "echo 'Deploying to production...'", "validate": "node deployment/validate-deployment.js", "prebuild": "echo 'Pre-build validation starting...'", "postbuild": "echo 'Post-build cleanup completed'"}, "repository": {"type": "git", "url": "local"}, "keywords": ["ota", "order", "processing", "gomyhire", "travel", "booking"], "author": "GoMyHire", "license": "MIT", "devDependencies": {}, "dependencies": {}}