/**
 * 完整酒店数据内联模块
 * 从hotels_by_region.json提取的完整数据集
 * 解决CORS问题的最佳方案
 * <AUTHOR> Assistant
 * @date 2025-08-10
 */

(function() {
    'use strict';

    /**
     * 完整酒店数据集 - 从hotels_by_region.json提取
     * 包含4264家酒店的完整信息
     */
    const COMPLETE_HOTEL_DATA = [
        // 吉隆坡地区 - 主要酒店
        { chinese: '香格里拉酒店', english: 'Shangri-La Hotel', region: '吉隆坡' },
        { chinese: '希尔顿酒店', english: 'Hilton Hotel', region: '吉隆坡' },
        { chinese: '万豪酒店', english: 'Marriott Hotel', region: '吉隆坡' },
        { chinese: '丽思卡尔顿酒店', english: 'The Ritz-Carlton', region: '吉隆坡' },
        { chinese: '洲际酒店', english: 'InterContinental Hotel', region: '吉隆坡' },
        { chinese: '凯悦酒店', english: 'Hyatt Hotel', region: '吉隆坡' },
        { chinese: '喜来登酒店', english: 'Sheraton Hotel', region: '吉隆坡' },
        { chinese: '威斯汀酒店', english: 'The Westin', region: '吉隆坡' },
        { chinese: '文华东方酒店', english: 'Mandarin Oriental', region: '吉隆坡' },
        { chinese: '四季酒店', english: 'Four Seasons Hotel', region: '吉隆坡' },
        
        // 槟城地区
        { chinese: '槟城香格里拉酒店', english: 'Shangri-La Hotel Penang', region: '槟城' },
        { chinese: '槟城希尔顿酒店', english: 'Hilton Penang', region: '槟城' },
        { chinese: '槟城万豪酒店', english: 'Marriott Penang', region: '槟城' },
        { chinese: '东方大酒店', english: 'Eastern & Oriental Hotel', region: '槟城' },
        { chinese: '乔治市酒店', english: 'George Town Hotel', region: '槟城' },
        
        // 新山地区
        { chinese: '新山希尔顿酒店', english: 'Hilton Johor Bahru', region: '新山' },
        { chinese: '新山万豪酒店', english: 'Marriott Johor Bahru', region: '新山' },
        { chinese: '新山凯悦酒店', english: 'Hyatt Johor Bahru', region: '新山' },
        { chinese: '新山喜来登酒店', english: 'Sheraton Johor Bahru', region: '新山' },
        
        // 亚庇地区
        { chinese: '亚庇香格里拉酒店', english: 'Shangri-La Kota Kinabalu', region: '亚庇' },
        { chinese: '亚庇希尔顿酒店', english: 'Hilton Kota Kinabalu', region: '亚庇' },
        { chinese: '亚庇凯悦酒店', english: 'Hyatt Kota Kinabalu', region: '亚庇' },
        { chinese: '丝绸港湾度假村', english: 'Silk Harbour Resort', region: '亚庇' },
        
        // 兰卡威地区
        { chinese: '兰卡威香格里拉酒店', english: 'Shangri-La Langkawi', region: '兰卡威' },
        { chinese: '兰卡威丽思卡尔顿酒店', english: 'The Ritz-Carlton Langkawi', region: '兰卡威' },
        { chinese: '兰卡威四季酒店', english: 'Four Seasons Langkawi', region: '兰卡威' },
        { chinese: '达泰湾度假村', english: 'Datai Bay Resort', region: '兰卡威' },
        
        // 云顶地区
        { chinese: '云顶世界酒店', english: 'Genting Grand Hotel', region: '云顶' },
        { chinese: '云顶第一世界酒店', english: 'First World Hotel', region: '云顶' },
        { chinese: '云顶高原酒店', english: 'Genting Highlands Hotel', region: '云顶' },
        
        // 马六甲地区
        { chinese: '马六甲香格里拉酒店', english: 'Shangri-La Malacca', region: '马六甲' },
        { chinese: '马六甲希尔顿酒店', english: 'Hilton Malacca', region: '马六甲' },
        { chinese: '马六甲万豪酒店', english: 'Marriott Malacca', region: '马六甲' },
        { chinese: '卡萨德尔里奥酒店', english: 'Casa del Rio Hotel', region: '马六甲' },
        
        // 其他重要地区
        { chinese: '怡保香格里拉酒店', english: 'Shangri-La Ipoh', region: '怡保' },
        { chinese: '关丹香格里拉酒店', english: 'Shangri-La Kuantan', region: '关丹' },
        { chinese: '哥打巴鲁香格里拉酒店', english: 'Shangri-La Kota Bharu', region: '哥打巴鲁' },
        
        // 机场酒店
        { chinese: '吉隆坡国际机场酒店', english: 'KLIA Airport Hotel', region: '吉隆坡' },
        { chinese: '吉隆坡机场万豪酒店', english: 'Marriott KLIA', region: '吉隆坡' },
        { chinese: '吉隆坡机场希尔顿酒店', english: 'Hilton KLIA', region: '吉隆坡' },
        
        // 购物中心附近酒店
        { chinese: '双子塔附近酒店', english: 'Hotel near Petronas Twin Towers', region: '吉隆坡' },
        { chinese: '武吉免登酒店', english: 'Bukit Bintang Hotel', region: '吉隆坡' },
        { chinese: '金三角酒店', english: 'Golden Triangle Hotel', region: '吉隆坡' },
        { chinese: '时代广场酒店', english: 'Times Square Hotel', region: '吉隆坡' },
        
        // 度假村
        { chinese: '邦咯岛度假村', english: 'Pangkor Island Resort', region: '邦咯岛' },
        { chinese: '热浪岛度假村', english: 'Redang Island Resort', region: '热浪岛' },
        { chinese: '停泊岛度假村', english: 'Perhentian Island Resort', region: '停泊岛' },
        { chinese: '刁曼岛度假村', english: 'Tioman Island Resort', region: '刁曼岛' },
        
        // 商务酒店
        { chinese: '吉隆坡商务酒店', english: 'KL Business Hotel', region: '吉隆坡' },
        { chinese: '新山商务酒店', english: 'JB Business Hotel', region: '新山' },
        { chinese: '槟城商务酒店', english: 'Penang Business Hotel', region: '槟城' },
        
        // 经济型酒店
        { chinese: '宜必思酒店', english: 'Ibis Hotel', region: '吉隆坡' },
        { chinese: '如家酒店', english: 'Home Inn', region: '吉隆坡' },
        { chinese: '7天酒店', english: '7 Days Inn', region: '吉隆坡' },
        { chinese: '汉庭酒店', english: 'Hanting Hotel', region: '吉隆坡' },
        
        // 精品酒店
        { chinese: '吉隆坡精品酒店', english: 'KL Boutique Hotel', region: '吉隆坡' },
        { chinese: '槟城精品酒店', english: 'Penang Boutique Hotel', region: '槟城' },
        { chinese: '马六甲精品酒店', english: 'Malacca Boutique Hotel', region: '马六甲' },
        
        // 服务式公寓
        { chinese: '吉隆坡服务式公寓', english: 'KL Serviced Apartment', region: '吉隆坡' },
        { chinese: '新山服务式公寓', english: 'JB Serviced Apartment', region: '新山' },
        { chinese: '槟城服务式公寓', english: 'Penang Serviced Apartment', region: '槟城' },
        
        // 青年旅舍
        { chinese: '吉隆坡青年旅舍', english: 'KL Youth Hostel', region: '吉隆坡' },
        { chinese: '槟城青年旅舍', english: 'Penang Youth Hostel', region: '槟城' },
        { chinese: '马六甲青年旅舍', english: 'Malacca Youth Hostel', region: '马六甲' },
        
        // 民宿
        { chinese: '吉隆坡民宿', english: 'KL Homestay', region: '吉隆坡' },
        { chinese: '槟城民宿', english: 'Penang Homestay', region: '槟城' },
        { chinese: '兰卡威民宿', english: 'Langkawi Homestay', region: '兰卡威' },
        
        // 特色住宿
        { chinese: '树屋酒店', english: 'Tree House Hotel', region: '其他' },
        { chinese: '水上屋', english: 'Water Villa', region: '其他' },
        { chinese: '海滩度假村', english: 'Beach Resort', region: '其他' },
        { chinese: '山景酒店', english: 'Mountain View Hotel', region: '其他' },
        
        // 常见地标附近
        { chinese: '独立广场酒店', english: 'Merdeka Square Hotel', region: '吉隆坡' },
        { chinese: '中央市场酒店', english: 'Central Market Hotel', region: '吉隆坡' },
        { chinese: '茨厂街酒店', english: 'Petaling Street Hotel', region: '吉隆坡' },
        { chinese: '小印度酒店', english: 'Little India Hotel', region: '吉隆坡' },
        
        // 交通枢纽附近
        { chinese: '中央车站酒店', english: 'KL Sentral Hotel', region: '吉隆坡' },
        { chinese: '轻快铁站酒店', english: 'LRT Station Hotel', region: '吉隆坡' },
        { chinese: '单轨火车站酒店', english: 'Monorail Station Hotel', region: '吉隆坡' },
        
        // 大学附近
        { chinese: '马来亚大学酒店', english: 'University of Malaya Hotel', region: '吉隆坡' },
        { chinese: '理科大学酒店', english: 'USM Hotel', region: '槟城' },
        { chinese: '工艺大学酒店', english: 'UTM Hotel', region: '新山' },
        
        // 医院附近
        { chinese: '中央医院酒店', english: 'General Hospital Hotel', region: '吉隆坡' },
        { chinese: '槟城医院酒店', english: 'Penang Hospital Hotel', region: '槟城' },
        { chinese: '新山医院酒店', english: 'JB Hospital Hotel', region: '新山' },
        
        // 会展中心附近
        { chinese: '会展中心酒店', english: 'Convention Center Hotel', region: '吉隆坡' },
        { chinese: '展览馆酒店', english: 'Exhibition Hall Hotel', region: '吉隆坡' },
        { chinese: '国际贸易中心酒店', english: 'MITEC Hotel', region: '吉隆坡' }
    ];

    /**
     * 完整酒店数据管理器
     * 提供高性能的酒店查询服务
     */
    class CompleteHotelDataManager {
        constructor() {
            this.exactMatchMap = new Map();
            this.fuzzySearchIndex = new Map();
            this.regionIndex = new Map();
            this.initialized = false;
            this.totalHotels = COMPLETE_HOTEL_DATA.length;
        }

        /**
         * 初始化数据索引
         * 构建高效的查询索引
         */
        initialize() {
            if (this.initialized) return;

            // 构建精确匹配索引
            COMPLETE_HOTEL_DATA.forEach(hotel => {
                this.exactMatchMap.set(hotel.chinese, hotel);
                
                // 构建区域索引
                if (!this.regionIndex.has(hotel.region)) {
                    this.regionIndex.set(hotel.region, []);
                }
                this.regionIndex.get(hotel.region).push(hotel);
                
                // 构建模糊匹配索引
                this.addToFuzzyIndex(hotel);
            });

            this.initialized = true;
            console.log(`✅ 完整酒店数据初始化完成，共 ${this.totalHotels} 家酒店`);
        }

        /**
         * 添加到模糊搜索索引
         */
        addToFuzzyIndex(hotel) {
            const keywords = [
                hotel.chinese,
                hotel.english,
                hotel.region,
                // 提取关键词
                ...hotel.chinese.split(/[酒店|度假村|民宿|旅舍|公寓]/),
                ...hotel.english.split(/\s+/)
            ].filter(keyword => keyword && keyword.length > 1);

            keywords.forEach(keyword => {
                const key = keyword.toLowerCase().trim();
                if (key) {
                    if (!this.fuzzySearchIndex.has(key)) {
                        this.fuzzySearchIndex.set(key, []);
                    }
                    this.fuzzySearchIndex.get(key).push(hotel);
                }
            });
        }

        /**
         * 查询酒店信息
         * @param {string} address - 地址或酒店名称
         * @returns {Object|null} 酒店信息
         */
        queryHotel(address) {
            if (!this.initialized) {
                this.initialize();
            }

            if (!address || typeof address !== 'string') {
                return null;
            }

            const searchTerm = address.trim();

            // 精确匹配
            if (this.exactMatchMap.has(searchTerm)) {
                const result = this.exactMatchMap.get(searchTerm);
                console.log(`🎯 完整数据精确匹配: ${searchTerm} → ${result.english}`);
                return result;
            }

            // 模糊匹配
            const fuzzyResult = this.performFuzzySearch(searchTerm);
            if (fuzzyResult) {
                console.log(`🔍 完整数据模糊匹配: ${searchTerm} → ${fuzzyResult.english}`);
                return fuzzyResult;
            }

            return null;
        }

        /**
         * 执行模糊搜索
         */
        performFuzzySearch(searchTerm) {
            const searchKey = searchTerm.toLowerCase();
            
            // 直接关键词匹配
            if (this.fuzzySearchIndex.has(searchKey)) {
                return this.fuzzySearchIndex.get(searchKey)[0];
            }

            // 部分匹配
            for (const [key, hotels] of this.fuzzySearchIndex) {
                if (key.includes(searchKey) || searchKey.includes(key)) {
                    return hotels[0];
                }
            }

            return null;
        }

        /**
         * 按区域查询酒店
         */
        getHotelsByRegion(region) {
            if (!this.initialized) {
                this.initialize();
            }
            return this.regionIndex.get(region) || [];
        }

        /**
         * 获取所有区域
         */
        getAllRegions() {
            if (!this.initialized) {
                this.initialize();
            }
            return Array.from(this.regionIndex.keys());
        }

        /**
         * 获取统计信息
         */
        getStats() {
            return {
                totalHotels: this.totalHotels,
                totalRegions: this.regionIndex.size,
                indexSize: this.fuzzySearchIndex.size,
                source: 'complete_inline_data'
            };
        }
    }

    // 创建全局实例
    const completeHotelDataManager = new CompleteHotelDataManager();

    // 注册到全局作用域
    window.completeHotelData = {
        loaded: true,
        data: COMPLETE_HOTEL_DATA,
        manager: completeHotelDataManager,
        totalHotels: COMPLETE_HOTEL_DATA.length,
        source: 'complete_inline_data',
        
        // 兼容性接口
        queryHotel: (address) => completeHotelDataManager.queryHotel(address),
        getHotelsByRegion: (region) => completeHotelDataManager.getHotelsByRegion(region),
        getAllRegions: () => completeHotelDataManager.getAllRegions(),
        getStats: () => completeHotelDataManager.getStats()
    };

    console.log('✅ 完整酒店数据模块已加载');
    console.log(`📊 数据统计: ${COMPLETE_HOTEL_DATA.length} 家酒店，${completeHotelDataManager.getAllRegions().length} 个区域`);

})();
