/**
 * 依赖标签（Dependency Tags）
 * 文件: js/auto-resize-manager.js
 * 角色: 自动尺寸调整（输入框/区域自适应）
 * 上游依赖(直接使用): Logger
 * 下游被依赖(常见调用方): UIManager
 * 事件: 监听输入并调整尺寸
 * 更新时间: 2025-08-09
 */
/**
 * 自适应高度管理器
 * 为输入框和文本域提供自动高度调整功能
 * @SERVICE 自适应高度管理服务
 */

(function() {
    'use strict';

    class AutoResizeManager {
        constructor() {
            this.logger = getLogger();
            this.resizeObserver = null;
            this.managedElements = new Map(); // 存储管理的元素及其配置
            this.debounceTimers = new Map(); // 防抖定时器
            
            this.init();
        }

        /**
         * 初始化管理器
         */
        init() {
            this.logger.log('🔧 自适应高度管理器初始化中...', 'info');
            
            // 初始化ResizeObserver用于监听窗口大小变化
            this.initResizeObserver();
            
            // 自动发现并初始化需要自适应高度的元素
            this.autoDiscoverElements();
            
            this.logger.log('✅ 自适应高度管理器初始化完成', 'success');
        }

        /**
         * 初始化ResizeObserver
         */
        initResizeObserver() {
            if (typeof ResizeObserver !== 'undefined') {
                this.resizeObserver = new ResizeObserver(() => {
                    // 窗口大小变化时重新调整所有管理的元素
                    this.debounce('windowResize', () => {
                        this.managedElements.forEach((config, element) => {
                            this.adjustHeight(element, config);
                        });
                    }, 100);
                });
            }
        }

        /**
         * 自动发现需要自适应高度的元素
         */
        autoDiscoverElements() {
            // 预定义的自适应字段选择器
            const autoResizeSelectors = [
                // 通过ID选择
                '#extraRequirement',
                '#pickup',
                '#dropoff', 
                '#customerName',
                '#flightInfo',
                '#otaReferenceNumber',
                '#orderInput',
                
                // 通过类名选择
                '.auto-resize',
                '.order-field-input[type="text"]',
                '.order-field-input[type="textarea"]',
                
                // 通过属性选择
                'textarea[data-auto-resize="true"]',
                'input[data-auto-resize="true"]',
                
                // 多订单管理器中的输入框
                '.multi-order-input',
                '.editable-field input',
                
                // 其他可能的输入框
                'textarea:not([data-auto-resize="false"])',
                'input[type="text"]:not([data-auto-resize="false"])'
            ];

            autoResizeSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (!this.managedElements.has(element)) {
                            this.addElement(element);
                        }
                    });
                } catch (error) {
                    this.logger.logError(`选择器 ${selector} 查找失败`, error);
                }
            });
        }

        /**
         * 添加元素到自适应管理
         * @param {HTMLElement} element - 要管理的元素
         * @param {Object} options - 配置选项
         */
        addElement(element, options = {}) {
            if (!element || this.managedElements.has(element)) {
                return;
            }

            // 默认配置
            const config = {
                minHeight: options.minHeight || this.getDefaultMinHeight(element),
                maxHeight: options.maxHeight || this.getDefaultMaxHeight(element),
                debounceDelay: options.debounceDelay || 50,
                enableTransition: options.enableTransition !== false,
                responsive: options.responsive !== false,
                ...options
            };

            // 存储配置
            this.managedElements.set(element, config);

            // 绑定事件监听器
            this.bindEvents(element, config);

            // 初始调整高度
            this.adjustHeight(element, config);

            // 添加到ResizeObserver
            if (this.resizeObserver && config.responsive) {
                this.resizeObserver.observe(element);
            }

            this.logger.log(`📏 已添加自适应高度管理: ${element.id || element.className}`, 'info');
        }

        /**
         * 移除元素的自适应管理
         * @param {HTMLElement} element - 要移除的元素
         */
        removeElement(element) {
            if (!this.managedElements.has(element)) {
                return;
            }

            // 移除事件监听器
            this.unbindEvents(element);

            // 从ResizeObserver中移除
            if (this.resizeObserver) {
                this.resizeObserver.unobserve(element);
            }

            // 清理防抖定时器
            const elementKey = this.getElementKey(element);
            if (this.debounceTimers.has(elementKey)) {
                clearTimeout(this.debounceTimers.get(elementKey));
                this.debounceTimers.delete(elementKey);
            }

            // 从管理列表中移除
            this.managedElements.delete(element);

            this.logger.log(`🗑️ 已移除自适应高度管理: ${element.id || element.className}`, 'info');
        }

        /**
         * 绑定事件监听器
         * @param {HTMLElement} element - 元素
         * @param {Object} config - 配置
         */
        bindEvents(element, config) {
            const elementKey = this.getElementKey(element);

            // 输入事件
            const inputHandler = () => {
                this.debounce(elementKey, () => {
                    this.adjustHeight(element, config);
                }, config.debounceDelay);
            };

            // 粘贴事件
            const pasteHandler = () => {
                // 粘贴后稍微延迟执行，确保内容已插入
                setTimeout(() => {
                    this.adjustHeight(element, config);
                }, 10);
            };

            // 焦点事件
            const focusHandler = () => {
                this.adjustHeight(element, config);
            };

            element.addEventListener('input', inputHandler);
            element.addEventListener('paste', pasteHandler);
            element.addEventListener('focus', focusHandler);

            // 存储事件处理器以便后续移除
            element._autoResizeHandlers = {
                input: inputHandler,
                paste: pasteHandler,
                focus: focusHandler
            };
        }

        /**
         * 解绑事件监听器
         * @param {HTMLElement} element - 元素
         */
        unbindEvents(element) {
            if (element._autoResizeHandlers) {
                element.removeEventListener('input', element._autoResizeHandlers.input);
                element.removeEventListener('paste', element._autoResizeHandlers.paste);
                element.removeEventListener('focus', element._autoResizeHandlers.focus);
                delete element._autoResizeHandlers;
            }
        }

        /**
         * 调整元素高度
         * @param {HTMLElement} element - 元素
         * @param {Object} config - 配置
         */
        adjustHeight(element, config) {
            if (!element || !element.isConnected) {
                return;
            }

            try {
                // 保存当前滚动位置
                const scrollTop = element.scrollTop;

                // 重置高度以获取正确的scrollHeight
                element.style.height = 'auto';

                // 计算新高度
                const scrollHeight = element.scrollHeight;
                const newHeight = Math.min(
                    Math.max(scrollHeight, config.minHeight),
                    config.maxHeight
                );

                // 设置新高度
                element.style.height = newHeight + 'px';

                // 设置过渡效果
                if (config.enableTransition) {
                    element.style.transition = 'height 0.2s ease';
                }

                // 恢复滚动位置
                element.scrollTop = scrollTop;

                // 如果内容超出最大高度，显示滚动条
                if (scrollHeight > config.maxHeight) {
                    element.style.overflowY = 'auto';
                } else {
                    element.style.overflowY = 'hidden';
                }

            } catch (error) {
                this.logger.logError('调整元素高度失败', error);
            }
        }

        /**
         * 获取默认最小高度
         * @param {HTMLElement} element - 元素
         * @returns {number} 最小高度
         */
        getDefaultMinHeight(element) {
            const isMobile = window.innerWidth <= 768;
            const isTextarea = element.tagName.toLowerCase() === 'textarea';
            
            if (isTextarea) {
                return isMobile ? 50 : 60;
            } else {
                return isMobile ? 30 : 35;
            }
        }

        /**
         * 获取默认最大高度
         * @param {HTMLElement} element - 元素
         * @returns {number} 最大高度
         */
        getDefaultMaxHeight(element) {
            const isMobile = window.innerWidth <= 768;
            const isTextarea = element.tagName.toLowerCase() === 'textarea';
            
            if (isTextarea) {
                return isMobile ? 200 : 300;
            } else {
                return isMobile ? 120 : 150;
            }
        }

        /**
         * 获取元素的唯一键
         * @param {HTMLElement} element - 元素
         * @returns {string} 唯一键
         */
        getElementKey(element) {
            return element.id || `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 防抖函数
         * @param {string} key - 防抖键
         * @param {Function} func - 要执行的函数
         * @param {number} delay - 延迟时间
         */
        debounce(key, func, delay) {
            if (this.debounceTimers.has(key)) {
                clearTimeout(this.debounceTimers.get(key));
            }

            const timer = setTimeout(() => {
                func();
                this.debounceTimers.delete(key);
            }, delay);

            this.debounceTimers.set(key, timer);
        }

        /**
         * 刷新所有管理的元素
         */
        refreshAll() {
            this.managedElements.forEach((config, element) => {
                this.adjustHeight(element, config);
            });
        }

        /**
         * 销毁管理器
         */
        destroy() {
            // 移除所有管理的元素
            this.managedElements.forEach((config, element) => {
                this.removeElement(element);
            });

            // 清理ResizeObserver
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
                this.resizeObserver = null;
            }

            // 清理所有防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            this.logger.log('🗑️ 自适应高度管理器已销毁', 'info');
        }
    }

    // 创建全局实例
    const autoResizeManager = new AutoResizeManager();

    // 导出到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.autoResizeManager = autoResizeManager;

    // 向后兼容
    window.autoResizeManager = autoResizeManager;

})();
