<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单修复测试</title>
</head>
<body>
    <h1>多订单修复测试</h1>
    
    <div>
        <h3>测试步骤：</h3>
        <ol>
            <li>打开开发者控制台</li>
            <li>点击下面的测试按钮</li>
            <li>查看控制台输出</li>
        </ol>
    </div>

    <button id="testBtn">测试多订单事件处理</button>
    
    <div id="result"></div>

    <!-- 最小化的必需依赖 -->
    <script>
        // 模拟基础环境
        window.OTA = window.OTA || {};
        
        // 模拟logger
        function getLogger() {
            return {
                log: (message, level, data) => {
                    console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
                }
            };
        }
        
        // 模拟多订单面板元素
        const mockPanel = document.createElement('div');
        mockPanel.id = 'multiOrderPanel';
        mockPanel.className = 'hidden';
        document.body.appendChild(mockPanel);
        
        const mockList = document.createElement('div');
        mockList.id = 'multiOrderList';
        mockPanel.appendChild(mockList);
    </script>

    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        document.getElementById('testBtn').addEventListener('click', function() {
            console.log('🧪 开始测试多订单事件处理...');
            
            // 模拟多订单检测结果
            const mockMultiOrderResult = {
                isMultiOrder: true,
                confidence: 0.9,
                orderCount: 2,
                orders: [
                    {
                        customer_name: "张三",
                        customer_contact: "12345678901",
                        pickup: "机场",
                        dropoff: "酒店A",
                        pickup_date: "2025-08-07",
                        pickup_time: "10:00",
                        ota_price: 150,
                        currency: "MYR"
                    },
                    {
                        customer_name: "李四", 
                        customer_contact: "09876543210",
                        pickup: "酒店B",
                        dropoff: "商场",
                        pickup_date: "2025-08-07",
                        pickup_time: "14:00",
                        ota_price: 200,
                        currency: "MYR"
                    }
                ]
            };

            // 创建并派发事件
            const event = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: mockMultiOrderResult,
                    orderText: "测试订单文本，包含两个订单..."
                }
            });

            console.log('🚀 派发多订单检测事件...');
            document.dispatchEvent(event);
            
            // 检查结果
            setTimeout(() => {
                const manager = window.OTA.multiOrderManagerV2;
                if (manager) {
                    console.log('📊 管理器状态:', {
                        isMultiOrderMode: manager.state.isMultiOrderMode,
                        parsedOrdersLength: manager.state.parsedOrders?.length,
                        selectedOrdersType: manager.state.selectedOrders?.constructor?.name,
                        selectedOrdersSize: manager.state.selectedOrders?.size
                    });
                    
                    const panel = document.getElementById('multiOrderPanel');
                    const list = document.getElementById('multiOrderList');
                    
                    document.getElementById('result').innerHTML = `
                        <h3>测试结果:</h3>
                        <p><strong>多订单模式:</strong> ${manager.state.isMultiOrderMode ? '✅ 已启用' : '❌ 未启用'}</p>
                        <p><strong>解析订单数量:</strong> ${manager.state.parsedOrders?.length || 0}</p>
                        <p><strong>selectedOrders类型:</strong> ${manager.state.selectedOrders?.constructor?.name || 'undefined'}</p>
                        <p><strong>面板显示状态:</strong> ${panel?.style.display !== 'none' ? '✅ 已显示' : '❌ 隐藏'}</p>
                        <p><strong>列表内容:</strong> ${list?.innerHTML ? '✅ 有内容' : '❌ 无内容'}</p>
                    `;
                } else {
                    document.getElementById('result').innerHTML = '<h3>❌ 管理器实例不存在</h3>';
                }
            }, 1000);
        });
    </script>
</body>
</html>
