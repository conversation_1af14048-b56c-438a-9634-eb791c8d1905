# 多订单API参考文档 v2.0

## 概述

本文档描述了多订单模式v2.0的核心API接口和数据结构。

## 核心API

### GeminiService.detectAndSplitMultiOrders()

一体化多订单检测、分割和完整解析方法。

#### 方法签名
```javascript
async detectAndSplitMultiOrders(orderText: string): Promise<MultiOrderResult>
```

#### 参数
- `orderText` (string): 原始订单文本，最小长度50字符

#### 返回值
```typescript
interface MultiOrderResult {
  isMultiOrder: boolean;           // 是否为多订单
  orderCount: number;              // 订单数量
  confidence: number;              // 置信度 (0.0-1.0)
  analysis: string;                // 分析说明
  orders: ParsedOrder[];           // 解析后的订单数组
}

interface ParsedOrder {
  rawText: string;                 // 原始订单文本片段
  customerName: string | null;     // 客户姓名
  customerContact: string | null;  // 联系电话
  customerEmail: string | null;    // 客户邮箱
  pickup: string | null;           // 上车地点
  dropoff: string | null;          // 目的地
  pickupDate: string | null;       // 接送日期 (YYYY-MM-DD)
  pickupTime: string | null;       // 接送时间 (HH:MM)
  passengerCount: number;          // 乘客人数
  luggageCount: number;            // 行李件数
  flightInfo: string | null;       // 航班信息
  otaReferenceNumber: string | null; // OTA订单号
  otaPrice: number | null;         // 价格
  currency: string;                // 货币 (MYR|USD|SGD|CNY)
  carTypeId: number;               // 车型ID
  subCategoryId: number;           // 子分类ID (2=接机,3=送机,4=包车)
  drivingRegionId: number;         // 行驶区域ID
  languagesIdArray: number[];      // 语言ID数组
  extraRequirement: string | null; // 额外要求
  babyChair: boolean;              // 儿童座椅
  tourGuide: boolean;              // 导游服务
  meetAndGreet: boolean;           // 迎接服务
}
```

#### 使用示例
```javascript
const geminiService = getGeminiService();
const result = await geminiService.detectAndSplitMultiOrders(orderText);

if (result.isMultiOrder) {
  console.log(`检测到${result.orderCount}个订单`);
  result.orders.forEach((order, index) => {
    console.log(`订单${index + 1}:`, order.customerName, order.pickup, order.dropoff);
  });
}
```

## MultiOrderManager API

### showMultiOrderPanel()

显示多订单管理面板。

#### 方法签名
```javascript
showMultiOrderPanel(orders: ParsedOrder[]): void
```

#### 参数
- `orders` (ParsedOrder[]): 完整解析的订单对象数组

### updateOrderField()

更新订单字段值。

#### 方法签名
```javascript
updateOrderField(index: number, fieldName: string, value: any): void
```

#### 参数
- `index` (number): 订单索引
- `fieldName` (string): 字段名称
- `value` (any): 新值

### generateOrderSummary()

生成订单摘要HTML。

#### 方法签名
```javascript
generateOrderSummary(order: ParsedOrder): string
```

#### 返回值
返回订单摘要的HTML字符串，包含：
- 客户信息
- 路线信息
- 时间信息
- 乘客人数
- 价格信息

### generateOrderFieldsHTML()

生成订单详细字段编辑器HTML。

#### 方法签名
```javascript
generateOrderFieldsHTML(order: ParsedOrder, index: number): string
```

#### 返回值
返回完整字段编辑器的HTML字符串。

## 字段验证API

### validateOrderFields()

验证和清理单个订单字段。

#### 方法签名
```javascript
validateOrderFields(order: any): ParsedOrder
```

#### 字段验证规则

##### 电话号码验证
```javascript
cleanPhoneNumber(phone: string): string | null
```
- 保留数字、+、-、空格
- 清除其他特殊字符

##### 邮箱验证
```javascript
validateEmail(email: string): string | null
```
- 使用正则表达式验证邮箱格式
- 格式：`/^[^\s@]+@[^\s@]+\.[^\s@]+$/`

##### 日期验证
```javascript
validateDate(date: string): string | null
```
- 验证YYYY-MM-DD格式
- 检查日期有效性

##### 时间验证
```javascript
validateTime(time: string): string | null
```
- 验证HH:MM格式（24小时制）
- 格式：`/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/`

##### 车型ID智能映射
```javascript
validateCarTypeId(carTypeId: number, passengerCount: number): number
```

映射规则：
- 1-3人 → 5 (5 Seater)
- 4人 → 37 (Extended 5)
- 5人 → 15 (7 Seater MPV)
- 6人 → 32 (Velfire/Alphard)
- 7人 → 20 (10 Seater MPV)
- 8-10人 → 23 (14 Seater Van)
- 11-12人 → 24 (18 Seater Van)
- 13+人 → 25 (30 Seat Mini Bus)

## 事件API

### multiOrderDetected

当检测到多订单时触发的事件。

#### 事件数据
```javascript
{
  detail: {
    orders: ParsedOrder[],     // 解析后的订单数组
    orderCount: number,        // 订单数量
    confidence: number,        // 置信度
    analysis: string          // 分析说明
  }
}
```

#### 监听示例
```javascript
document.addEventListener('multiOrderDetected', (event) => {
  const { orders, orderCount } = event.detail;
  console.log(`检测到${orderCount}个订单`, orders);
});
```

## 错误处理

### 错误类型

#### 解析错误
```javascript
{
  error: "parsing_failed",
  message: "Gemini解析失败",
  originalError: Error
}
```

#### 验证错误
```javascript
{
  error: "validation_failed", 
  field: "customerEmail",
  message: "邮箱格式不正确",
  value: "invalid-email"
}
```

#### 网络错误
```javascript
{
  error: "network_error",
  message: "网络连接失败", 
  status: 500
}
```

### 错误处理示例
```javascript
try {
  const result = await geminiService.detectAndSplitMultiOrders(text);
  // 处理成功结果
} catch (error) {
  if (error.error === 'parsing_failed') {
    console.error('AI解析失败:', error.message);
    // 显示用户友好的错误信息
  } else if (error.error === 'network_error') {
    console.error('网络错误:', error.message);
    // 提示用户检查网络连接
  }
}
```

## 性能优化

### 缓存策略
- 相似文本模式缓存
- 字段验证结果缓存
- ID映射表本地缓存

### 批量处理
- 支持批量订单验证
- 异步字段更新
- 进度跟踪和错误恢复

## 配置选项

### MultiOrderManager配置
```javascript
{
  minInputLength: 50,           // 最小输入长度
  debounceDelay: 1200,          // 防抖延迟(ms)
  maxOrdersPerBatch: 5,         // 每批次最大订单数
  batchDelay: 800,              // 批次间延迟(ms)
  confidenceThreshold: 0.7      // AI检测置信度阈值
}
```

### GeminiService配置
```javascript
{
  temperature: 0.1,             // AI创造性(低=更准确)
  topK: 1,                      // 候选token数量
  topP: 1,                      // 累积概率阈值
  maxOutputTokens: 2048,        // 最大输出token数
  timeout: 30000                // 请求超时(ms)
}
```

---

*本文档适用于多订单模式 v2.0及以上版本*