/**
 * ============================================================================
 * 🎬 动画组件样式 - 统一动画效果定义
 * ============================================================================
 *
 * @fileoverview 动画组件样式 - 为订单处理系统提供统一的动画效果
 * @description 包含实时分析、字段填充、状态反馈和按钮交互动画
 * 
 * @features 动画类型
 * - 实时分析动画和进度指示器
 * - 字段填充平滑过渡动画
 * - 状态反馈视觉动画
 * - 按钮交互增强动画
 * 
 * @compatibility 兼容性
 * - 支持动画开关控制
 * - 响应式设计适配
 * - 可访问性支持
 * - 性能优化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

/* =================================
   CSS变量定义
   ================================= */
:root {
  /* 动画持续时间 */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;
  --animation-duration-progress: 1000ms;
  
  /* 缓动函数 */
  --animation-easing-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --animation-easing-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --animation-easing-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 动画启用状态 */
  --animations-enabled: 1;
}

/* =================================
   动画禁用支持
   ================================= */
.animations-disabled *,
.animations-disabled *::before,
.animations-disabled *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* 系统偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* =================================
   实时分析动画
   ================================= */

/* 分析进度指示器 */
.realtime-progress {
  position: relative;
  margin: var(--spacing-3) 0;
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.realtime-progress.show {
  opacity: 1;
  transform: translateY(0);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
  transition: width var(--animation-duration-normal) var(--animation-easing-ease-out);
  position: relative;
}

/* 进度条光效动画 */
.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-text {
  margin-top: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  animation: pulse-text 2s infinite;
}

@keyframes pulse-text {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* 分析状态指示器 */
.analysis-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  transition: all var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.analysis-status.analyzing {
  background: var(--color-info-bg);
  color: var(--color-info);
  animation: analyzing-pulse 1.5s infinite;
}

@keyframes analyzing-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.02); opacity: 0.8; }
}

/* =================================
   字段填充动画
   ================================= */

/* 字段填充状态 */
.field-filling {
  position: relative;
  overflow: hidden;
}

.field-filling::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--color-primary-rgb), 0.1), transparent);
  animation: field-fill-sweep var(--animation-duration-normal) var(--animation-easing-ease-out);
}

@keyframes field-fill-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 字段高亮效果 */
.field-highlight {
  background: rgba(var(--color-success-rgb), 0.1) !important;
  border-color: var(--color-success) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  animation: field-highlight-fade var(--animation-duration-slow) var(--animation-easing-ease-out);
}

@keyframes field-highlight-fade {
  0% {
    background: rgba(var(--color-success-rgb), 0.2);
    box-shadow: 0 0 0 3px rgba(var(--color-success-rgb), 0.3);
  }
  100% {
    background: rgba(var(--color-success-rgb), 0.05);
    box-shadow: 0 0 0 1px rgba(var(--color-success-rgb), 0.1);
  }
}

/* 字段组动画 */
.form-group {
  transition: all var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.form-group.field-updated {
  animation: field-group-bounce var(--animation-duration-normal) var(--animation-easing-bounce);
}

@keyframes field-group-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* =================================
   状态反馈动画
   ================================= */

/* 状态动画基础类 */
.status-animating {
  animation: status-appear var(--animation-duration-normal) var(--animation-easing-bounce);
}

@keyframes status-appear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 成功状态 */
.status-success {
  background: var(--color-success-bg);
  color: var(--color-success);
  border-left: 4px solid var(--color-success);
}

.status-success.status-animating {
  animation: status-success var(--animation-duration-normal) var(--animation-easing-bounce);
}

@keyframes status-success {
  0% { background: var(--color-success-bg); }
  50% { background: rgba(var(--color-success-rgb), 0.2); }
  100% { background: var(--color-success-bg); }
}

/* 错误状态 */
.status-error {
  background: var(--color-error-bg);
  color: var(--color-error);
  border-left: 4px solid var(--color-error);
}

.status-error.status-animating {
  animation: status-error var(--animation-duration-normal) var(--animation-easing-bounce);
}

@keyframes status-error {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 警告状态 */
.status-warning {
  background: var(--color-warning-bg);
  color: var(--color-warning);
  border-left: 4px solid var(--color-warning);
}

.status-warning.status-animating {
  animation: status-warning var(--animation-duration-normal) var(--animation-easing-ease-out);
}

@keyframes status-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* =================================
   按钮交互动画增强
   ================================= */

/* 按钮点击动画 */
.btn-clicking {
  animation: btn-click var(--animation-duration-fast) var(--animation-easing-ease-out);
}

@keyframes btn-click {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 按钮悬停增强 */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  transition: all var(--animation-duration-fast) var(--animation-easing-ease-out);
}

.btn:active {
  transform: translateY(0);
  transition: all var(--animation-duration-fast) var(--animation-easing-ease-out);
}

/* 按钮加载状态增强 */
.btn.loading {
  position: relative;
  overflow: hidden;
}

.btn.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: btn-loading-sweep 1.5s infinite;
}

@keyframes btn-loading-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 按钮成功状态 */
.btn.success {
  background: var(--color-success) !important;
  animation: btn-success var(--animation-duration-normal) var(--animation-easing-bounce);
}

@keyframes btn-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* =================================
   通用动画工具类
   ================================= */

/* 淡入动画 */
.fade-in {
  animation: fade-in var(--animation-duration-normal) var(--animation-easing-ease-out);
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入动画 */
.slide-in-up {
  animation: slide-in-up var(--animation-duration-normal) var(--animation-easing-ease-out);
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹跳动画 */
.bounce-in {
  animation: bounce-in var(--animation-duration-slow) var(--animation-easing-bounce);
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* =================================
   响应式动画适配
   ================================= */

/* 移动端动画优化 */
@media (max-width: 768px) {
  :root {
    --animation-duration-fast: 100ms;
    --animation-duration-normal: 200ms;
    --animation-duration-slow: 300ms;
  }
  
  /* 减少移动端复杂动画 */
  .progress-fill::after,
  .btn.loading::before {
    display: none;
  }
}

/* 低性能设备优化 */
@media (max-width: 480px) {
  .field-filling::after,
  .progress-fill::after {
    display: none;
  }
  
  /* 简化动画效果 */
  .btn:hover {
    transform: none;
    box-shadow: none;
  }
}
