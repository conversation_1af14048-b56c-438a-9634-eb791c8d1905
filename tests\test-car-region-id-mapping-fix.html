<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型ID和区域ID映射修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #005a87; }
        .log-area { background: #f8f8f8; border: 1px solid #ddd; border-radius: 4px; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; margin-top: 10px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 车型ID和区域ID映射修复测试</h1>
        <p>测试Gemini响应中的车型ID和区域ID是否能正确保留到多订单显示中</p>

        <div class="test-section">
            <h3>📊 测试场景</h3>
            <button class="test-button" onclick="testGeminiCarTypeIdMapping()">测试车型ID映射</button>
            <button class="test-button" onclick="testGeminiRegionIdMapping()">测试区域ID映射</button>
            <button class="test-button" onclick="testPagingServicePreservation()">测试Paging服务车型保留</button>
            <button class="test-button" onclick="testCompleteDataFlow()">测试完整数据流</button>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>📝 测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 详细日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/logger-service.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/vehicle-configuration-manager.js"></script>
    <script src="js/multi-order/field-mapping-config.js"></script>
    <script src="js/multi-order/multi-order-transformer.js"></script>
    <script src="js/gemini-service.js"></script>

    <script>
        // 测试辅助函数
        function logToPage(message, level = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function addResult(message, type = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `result ${type}`;
            resultElement.textContent = message;
            resultsDiv.appendChild(resultElement);
        }

        function clearLogs() {
            document.getElementById('logArea').textContent = '';
            document.getElementById('testResults').innerHTML = '';
        }

        // 模拟Gemini响应数据
        function createMockGeminiResponse(carTypeId, drivingRegionId) {
            return {
                car_type_id: carTypeId,
                driving_region_id: drivingRegionId,
                customer_name: "测试客户",
                customer_contact: "+60123456789",
                pickup_location: "KLIA2机场",
                dropoff_location: "吉隆坡市中心",
                pickup_date: "2025-08-08",
                pickup_time: "10:00",
                passenger_count: 2,
                luggage_count: 2,
                ota_price: "150.00",
                sub_category_id: 2
            };
        }

        // 测试1: 车型ID映射
        async function testGeminiCarTypeIdMapping() {
            logToPage("开始测试车型ID映射...");
            
            try {
                const mockData = createMockGeminiResponse(15, 1); // 7座MPV, 吉隆坡
                logToPage(`输入数据: car_type_id=${mockData.car_type_id}`);

                // 获取服务实例
                const geminiService = window.getGeminiService ? window.getGeminiService() : new window.GeminiService();
                
                // 测试验证函数
                const validatedCarTypeId = geminiService.validateCarTypeId(mockData.car_type_id, mockData.passenger_count);
                logToPage(`验证后车型ID: ${validatedCarTypeId}`);

                if (validatedCarTypeId === mockData.car_type_id) {
                    addResult("✅ 车型ID验证测试通过 - 原始值被保留", 'success');
                } else {
                    addResult(`❌ 车型ID验证测试失败 - 期望: ${mockData.car_type_id}, 实际: ${validatedCarTypeId}`, 'error');
                }

                // 测试转换器
                if (window.OTA && window.OTA.MultiOrderTransformer) {
                    const transformer = new window.OTA.MultiOrderTransformer();
                    const transformedData = transformer.transformOrderData(mockData);
                    
                    logToPage(`转换后数据: carTypeId=${transformedData.carTypeId}, car_type_id=${transformedData.car_type_id}`);
                    
                    if (transformedData.carTypeId == mockData.car_type_id) {
                        addResult("✅ 转换器测试通过 - 车型ID正确映射", 'success');
                    } else {
                        addResult(`❌ 转换器测试失败 - carTypeId不匹配`, 'error');
                    }
                }

            } catch (error) {
                logToPage(`测试异常: ${error.message}`, 'error');
                addResult(`❌ 车型ID映射测试异常: ${error.message}`, 'error');
            }
        }

        // 测试2: 区域ID映射
        async function testGeminiRegionIdMapping() {
            logToPage("开始测试区域ID映射...");
            
            try {
                const mockData = createMockGeminiResponse(5, 3); // 5座车, 槟城
                logToPage(`输入数据: driving_region_id=${mockData.driving_region_id}`);

                const geminiService = window.getGeminiService ? window.getGeminiService() : new window.GeminiService();
                
                const validatedRegionId = geminiService.validateDrivingRegionId(mockData.driving_region_id);
                logToPage(`验证后区域ID: ${validatedRegionId}`);

                if (validatedRegionId === mockData.driving_region_id) {
                    addResult("✅ 区域ID验证测试通过 - 原始值被保留", 'success');
                } else {
                    addResult(`❌ 区域ID验证测试失败 - 期望: ${mockData.driving_region_id}, 实际: ${validatedRegionId}`, 'error');
                }

                if (window.OTA && window.OTA.MultiOrderTransformer) {
                    const transformer = new window.OTA.MultiOrderTransformer();
                    const transformedData = transformer.transformOrderData(mockData);
                    
                    logToPage(`转换后数据: drivingRegionId=${transformedData.drivingRegionId}`);
                    
                    if (transformedData.drivingRegionId == mockData.driving_region_id) {
                        addResult("✅ 转换器测试通过 - 区域ID正确映射", 'success');
                    } else {
                        addResult(`❌ 转换器测试失败 - drivingRegionId不匹配`, 'error');
                    }
                }

            } catch (error) {
                logToPage(`测试异常: ${error.message}`, 'error');
                addResult(`❌ 区域ID映射测试异常: ${error.message}`, 'error');
            }
        }

        // 测试3: Paging服务车型保留
        async function testPagingServicePreservation() {
            logToPage("开始测试Paging服务车型保留...");
            
            try {
                const mockPagingData = createMockGeminiResponse(15, 1); // 7座MPV
                mockPagingData.extra_requirement = "举牌接机服务"; // 触发Paging检测
                
                logToPage(`Paging订单输入: car_type_id=${mockPagingData.car_type_id}`);

                if (window.OTA && window.OTA.MultiOrderTransformer) {
                    const transformer = new window.OTA.MultiOrderTransformer();
                    const transformedData = transformer.transformOrderData(mockPagingData);
                    
                    logToPage(`Paging处理后: carTypeId=${transformedData.carTypeId}, is_paging_order=${transformedData.is_paging_order}`);
                    
                    if (transformedData.is_paging_order && transformedData.carTypeId == mockPagingData.car_type_id) {
                        addResult("✅ Paging服务测试通过 - 原始车型ID被保留", 'success');
                    } else if (transformedData.is_paging_order && transformedData.carTypeId === 34) {
                        addResult("⚠️ Paging服务使用默认车型ID (34) - 原始车型可能无效", 'warning');
                    } else {
                        addResult(`❌ Paging服务测试失败`, 'error');
                    }
                }

            } catch (error) {
                logToPage(`测试异常: ${error.message}`, 'error');
                addResult(`❌ Paging服务测试异常: ${error.message}`, 'error');
            }
        }

        // 测试4: 完整数据流
        async function testCompleteDataFlow() {
            logToPage("开始测试完整数据流...");
            
            try {
                const testCases = [
                    { car_type_id: 5, driving_region_id: 1, description: "5座车+吉隆坡" },
                    { car_type_id: 15, driving_region_id: 2, description: "7座MPV+新山" },
                    { car_type_id: 32, driving_region_id: 3, description: "Alphard+槟城" },
                ];

                for (const testCase of testCases) {
                    logToPage(`\n--- 测试案例: ${testCase.description} ---`);
                    
                    const mockData = createMockGeminiResponse(testCase.car_type_id, testCase.driving_region_id);
                    
                    if (window.OTA && window.OTA.MultiOrderTransformer) {
                        const transformer = new window.OTA.MultiOrderTransformer();
                        const result = transformer.transformOrderData(mockData);
                        
                        const carTypePreserved = result.carTypeId == mockData.car_type_id;
                        const regionPreserved = result.drivingRegionId == mockData.driving_region_id;
                        
                        logToPage(`结果: carTypeId=${result.carTypeId}(${carTypePreserved ? '✓' : '✗'}), drivingRegionId=${result.drivingRegionId}(${regionPreserved ? '✓' : '✗'})`);
                        
                        if (carTypePreserved && regionPreserved) {
                            addResult(`✅ ${testCase.description} - 数据完整保留`, 'success');
                        } else {
                            addResult(`❌ ${testCase.description} - 数据丢失`, 'error');
                        }
                    }
                }

            } catch (error) {
                logToPage(`测试异常: ${error.message}`, 'error');
                addResult(`❌ 完整数据流测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            logToPage("车型ID和区域ID映射修复测试页面已加载");
            logToPage("可用服务检查:");
            logToPage(`- GeminiService: ${window.GeminiService ? '✓' : '✗'}`);
            logToPage(`- MultiOrderTransformer: ${window.OTA?.MultiOrderTransformer ? '✓' : '✗'}`);
            logToPage(`- FieldMappingConfig: ${window.OTA?.FieldMappingConfig ? '✓' : '✗'}`);
        });
    </script>
</body>
</html>