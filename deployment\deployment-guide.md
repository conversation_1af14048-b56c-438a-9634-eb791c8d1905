# OTA 订单处理系统 - 部署配置指南

## 部署环境

### Netlify 静态站点部署

#### 1. 配置要求
- Node.js 版本：18.x
- 构建命令：无需构建（静态站点）
- 发布目录：当前目录 (.)

#### 2. 环境变量
如需配置环境变量，请在 Netlify 后台设置：
- `GOMYHIRE_API_URL`: GoMyHire API 地址
- `GEMINI_API_KEY`: Gemini API 密钥（如果使用）

#### 3. 重定向规则
```toml
# API 代理
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# SPA 路由支持
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# 404 页面
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 404
```

#### 4. 安全头配置
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Content-Security-Policy: 已配置支持 Gemini API 和外部资源

#### 5. 缓存策略
- JS/CSS 文件：1年缓存，immutable
- 图片文件：30天缓存
- HTML 文件：1小时缓存

## 部署步骤

### 自动部署（推荐）
1. 连接 Git 仓库到 Netlify
2. 配置构建设置：
   - Build command: `echo 'Static site deployment'`
   - Publish directory: `.`
3. 设置环境变量（如需要）
4. 触发部署

### 手动部署
1. 压缩项目文件（排除 node_modules、.git 等）
2. 在 Netlify 后台手动上传
3. 配置域名和 SSL

## 验证部署

运行验证脚本：
```bash
node deployment/validate-deployment.js
```

## 常见问题

### 1. CSP 策略过严
如果遇到内容安全策略问题，请检查 netlify.toml 中的 CSP 配置

### 2. 路由问题
确保 SPA 重定向规则正确配置，否则刷新页面会出现 404

### 3. API 请求失败
检查 CORS 配置和 API 端点设置

### 4. 静态资源加载失败
确保文件路径正确，特别是 JS 和 CSS 文件的引用

## 性能优化

1. 启用 Gzip 压缩
2. 配置适当的缓存策略
3. 优化图片大小和格式
4. 使用 CDN 加速

## 监控和日志

- 使用 Netlify Analytics 监控访问情况
- 配置错误日志收集
- 设置性能监控

## 回滚策略

Netlify 支持一键回滚到之前的部署版本：
1. 在 Netlify 后台查看部署历史
2. 选择要回滚的版本
3. 点击 "Publish deploy" 完成回滚
