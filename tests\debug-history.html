<!DOCTYPE html>
<html>
<head>
    <title>历史订单调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>历史订单调试工具</h1>
    
    <div class="section">
        <h2>1. 检查localStorage数据</h2>
        <button onclick="checkLocalStorage()">检查历史订单数据</button>
        <div id="storage-result"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试历史管理器</h2>
        <button onclick="testHistoryManager()">测试管理器初始化</button>
        <button onclick="addTestOrder()">添加测试订单</button>
        <button onclick="showHistoryPanel()">显示历史面板</button>
        <div id="manager-result"></div>
    </div>
    
    <div class="section">
        <h2>3. 检查DOM元素</h2>
        <button onclick="checkDOMElements()">检查DOM元素</button>
        <div id="dom-result"></div>
    </div>

    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/order-history-manager.js"></script>
    
    <script>
        function log(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function checkLocalStorage() {
            const result = document.getElementById('storage-result');
            result.innerHTML = '<h3>LocalStorage 检查结果:</h3>';
            
            // 检查历史订单数据
            const historyData = localStorage.getItem('ota_order_history');
            if (historyData) {
                try {
                    const parsed = JSON.parse(historyData);
                    result.innerHTML += '<p class="success">✅ 找到历史订单数据</p>';
                    result.innerHTML += '<pre>' + JSON.stringify(parsed, null, 2) + '</pre>';
                    
                    // 统计数据
                    const userCount = Object.keys(parsed).length;
                    let totalOrders = 0;
                    Object.values(parsed).forEach(userOrders => {
                        if (Array.isArray(userOrders)) {
                            totalOrders += userOrders.length;
                        }
                    });
                    result.innerHTML += `<p>用户数: ${userCount}, 总订单数: ${totalOrders}</p>`;
                } catch (e) {
                    result.innerHTML += '<p class="error">❌ 数据解析失败: ' + e.message + '</p>';
                    result.innerHTML += '<pre>' + historyData + '</pre>';
                }
            } else {
                result.innerHTML += '<p class="warning">⚠️ 未找到历史订单数据</p>';
            }
            
            // 列出所有localStorage键
            result.innerHTML += '<h4>所有localStorage键:</h4><ul>';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                result.innerHTML += `<li>${key}</li>`;
            }
            result.innerHTML += '</ul>';
        }
        
        function testHistoryManager() {
            const result = document.getElementById('manager-result');
            result.innerHTML = '<h3>历史管理器测试结果:</h3>';
            
            try {
                // 检查全局函数
                if (typeof getOrderHistoryManager === 'function') {
                    result.innerHTML += '<p class="success">✅ getOrderHistoryManager 函数存在</p>';
                    
                    const manager = getOrderHistoryManager();
                    if (manager) {
                        result.innerHTML += '<p class="success">✅ 历史管理器实例创建成功</p>';
                        
                        // 测试基本功能
                        const history = manager.getHistory();
                        result.innerHTML += `<p>当前用户: ${manager.getCurrentUser()}</p>`;
                        result.innerHTML += `<p>历史订单数: ${history.length}</p>`;
                        
                        if (history.length > 0) {
                            result.innerHTML += '<h4>最近的订单:</h4>';
                            result.innerHTML += '<pre>' + JSON.stringify(history.slice(0, 3), null, 2) + '</pre>';
                        }
                    } else {
                        result.innerHTML += '<p class="error">❌ 历史管理器实例创建失败</p>';
                    }
                } else {
                    result.innerHTML += '<p class="error">❌ getOrderHistoryManager 函数不存在</p>';
                }
                
                // 检查其他全局引用
                if (window.OTA && window.OTA.orderHistoryManager) {
                    result.innerHTML += '<p class="success">✅ OTA.orderHistoryManager 存在</p>';
                } else {
                    result.innerHTML += '<p class="warning">⚠️ OTA.orderHistoryManager 不存在</p>';
                }
                
            } catch (error) {
                result.innerHTML += '<p class="error">❌ 测试过程中发生错误: ' + error.message + '</p>';
            }
        }
        
        function addTestOrder() {
            try {
                const manager = getOrderHistoryManager();
                if (!manager) {
                    alert('历史管理器不可用');
                    return;
                }
                
                const testOrderData = {
                    customerName: '测试客户',
                    customerContact: '+60123456789',
                    customerEmail: '<EMAIL>',
                    pickup: '吉隆坡国际机场',
                    destination: '市中心酒店',
                    date: new Date().toISOString().split('T')[0],
                    time: '14:30',
                    passengerNumber: '2',
                    luggageNumber: '3',
                    otaReferenceNumber: 'TEST' + Date.now(),
                    otaChannel: 'Debug Test'
                };
                
                const orderId = 'DEBUG_' + Date.now();
                manager.addOrder(testOrderData, orderId, {
                    success: true,
                    message: '测试订单创建成功'
                });
                
                alert('测试订单已添加: ' + orderId);
                testHistoryManager(); // 刷新显示
            } catch (error) {
                alert('添加测试订单失败: ' + error.message);
            }
        }
        
        function showHistoryPanel() {
            try {
                const manager = getOrderHistoryManager();
                if (manager && typeof manager.showHistoryPanel === 'function') {
                    manager.showHistoryPanel();
                } else {
                    alert('历史管理器或showHistoryPanel方法不可用');
                }
            } catch (error) {
                alert('显示历史面板失败: ' + error.message);
            }
        }
        
        function checkDOMElements() {
            const result = document.getElementById('dom-result');
            result.innerHTML = '<h3>DOM元素检查结果:</h3>';
            
            const elements = [
                'historyPanel',
                'historyListContainer', 
                'historyBtn',
                'closeHistoryBtn',
                'listCount'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    result.innerHTML += `<p class="success">✅ ${id}: 存在</p>`;
                } else {
                    result.innerHTML += `<p class="error">❌ ${id}: 不存在</p>`;
                }
            });
        }
    </script>
</body>
</html>
