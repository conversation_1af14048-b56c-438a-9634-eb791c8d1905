/**
 * @NETLIFY_FUNCTION 航班信息查询代理函数
 * 🏷️ 标签: @FUNCTION @FLIGHT_INFO @PROXY
 * 📝 说明: 代理FlightAware AeroAPI调用，解决CORS问题，保护API密钥安全
 * <AUTHOR>
 * @version 1.0.0
 */

const https = require('https');

// FlightAware AeroAPI配置
const FLIGHTAWARE_BASE_URL = 'https://aeroapi.flightaware.com/aeroapi';
const API_TIMEOUT = 10000; // 10秒超时

/**
 * Netlify Function处理器
 * @param {Object} event - Netlify事件对象
 * @param {Object} context - Netlify上下文对象
 * @returns {Promise<Object>} HTTP响应
 */
exports.handler = async (event, context) => {
    // 设置CORS头
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // 处理预检请求
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // 只允许GET请求
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Method not allowed',
                message: '只支持GET请求'
            })
        };
    }

    try {
        // 获取API密钥
        const apiKey = process.env.FLIGHTAWARE_API_KEY;
        if (!apiKey) {
            console.error('❌ FlightAware API密钥未配置');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'API_KEY_MISSING',
                    message: 'FlightAware API密钥未配置'
                })
            };
        }

        // 从查询参数获取航班号
        const flightNumber = event.queryStringParameters?.flight;
        if (!flightNumber) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'MISSING_FLIGHT_NUMBER',
                    message: '缺少航班号参数'
                })
            };
        }

        console.log(`🔍 查询航班信息: ${flightNumber}`);

        // 调用FlightAware API
        const flightData = await callFlightAwareAPI(flightNumber, apiKey);
        
        console.log(`✅ 航班信息查询成功: ${flightNumber}`);
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                data: flightData,
                timestamp: new Date().toISOString()
            })
        };

    } catch (error) {
        console.error('❌ 航班信息查询失败:', error);
        
        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        let errorCode = 'INTERNAL_ERROR';
        
        if (error.message.includes('404')) {
            statusCode = 404;
            errorCode = 'FLIGHT_NOT_FOUND';
        } else if (error.message.includes('401') || error.message.includes('403')) {
            statusCode = 401;
            errorCode = 'API_AUTHENTICATION_FAILED';
        } else if (error.message.includes('timeout')) {
            statusCode = 408;
            errorCode = 'API_TIMEOUT';
        }

        return {
            statusCode,
            headers,
            body: JSON.stringify({
                success: false,
                error: errorCode,
                message: error.message,
                flightNumber: event.queryStringParameters?.flight || 'unknown'
            })
        };
    }
};

/**
 * 调用FlightAware API
 * @param {string} flightNumber - 航班号
 * @param {string} apiKey - API密钥
 * @returns {Promise<Object>} API响应数据
 */
async function callFlightAwareAPI(flightNumber, apiKey) {
    return new Promise((resolve, reject) => {
        const url = `${FLIGHTAWARE_BASE_URL}/flights/${encodeURIComponent(flightNumber)}`;
        
        const options = {
            method: 'GET',
            headers: {
                'x-apikey': apiKey,
                'Accept': 'application/json; charset=UTF-8',
                'User-Agent': 'OTA-Flight-Info-Service/1.0'
            },
            timeout: API_TIMEOUT
        };

        const req = https.request(url, options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    if (res.statusCode === 200) {
                        const jsonData = JSON.parse(data);
                        resolve(jsonData);
                    } else {
                        reject(new Error(`API请求失败: ${res.statusCode} ${res.statusMessage}`));
                    }
                } catch (parseError) {
                    reject(new Error(`API响应解析失败: ${parseError.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error(`API请求错误: ${error.message}`));
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error(`API请求超时: ${API_TIMEOUT}ms`));
        });

        req.setTimeout(API_TIMEOUT);
        req.end();
    });
}

/**
 * 验证航班号格式
 * @param {string} flightNumber - 航班号
 * @returns {boolean} 是否有效
 */
function validateFlightNumber(flightNumber) {
    if (!flightNumber || typeof flightNumber !== 'string') {
        return false;
    }
    
    // 航班号格式验证：2-3个字母 + 1-4个数字，可选的分段标识
    const flightPattern = /^[A-Z]{2,3}\d{1,4}(\/\d+)?$/i;
    return flightPattern.test(flightNumber.trim());
}
