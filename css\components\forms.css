/**
 * 表单组件样式
 * 包含输入框、选择器、复选框等表单元素
 */

/* =================================
   语言复选框样式 - 简化版本
   ================================= */

/* 语言复选框容器 */
.language-checkboxes {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-card);
}

/* 单个复选框项 */
.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    min-height: 10px; /* 确保触摸目标足够大 */
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease;
}

.checkbox-item:hover {
    background-color: var(--bg-secondary);
}

/* 复选框样式 */
.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--color-primary);
}

/* 复选框标签 */
.checkbox-item label {
    flex: 1;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 12.6px; /* 14px * 0.9 */
    font-family: inherit;
    user-select: none;
}

/* 焦点状态 */
.checkbox-item input[type="checkbox"]:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* =================================
   表单组 - 优化垂直间距和分组
   ================================= */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0; /* 进一步紧凑化间距 */
  margin-bottom: 0; /* 进一步紧凑化底部间距 */
}

/* 紧凑布局中的表单组 - 减少间距 */
.compact-inline-layout .form-group {
  margin-bottom: 0; /* 进一步紧凑化间距 */
}

.form-group label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: 0; /* 进一步减少标签与输入框之间的间距 */
}

/* 图标相关样式已移除 - 不再使用图标 */

/* 相关字段分组 - 减少组内间距 */
.form-group + .form-group.related {
  margin-top: calc(var(--spacing-2) * -1); /* 相关字段更紧密 */
}

/* 分组分隔 - 增加不同逻辑组之间的间距 */
.form-group.group-separator {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

/* =================================
   输入框样式
   ================================= */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="time"],
textarea,
select {
  width: 100%;
  padding: var(--spacing-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(159, 41, 159, 0.1);
}

input:hover,
textarea:hover,
select:hover {
  border-color: var(--border-hover);
}

input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

/* =================================
   文本域
   ================================= */
textarea {
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

/* 自适应高度文本框 */
.auto-resize,
#extraRequirement,
#pickup,
#dropoff,
#customerName,
#flightInfo,
#otaReferenceNumber,
#orderInput {
  resize: none; /* 禁用手动调整大小 */
  min-height: 35px; /* 最小高度 */
  max-height: 200px; /* 最大高度 */
  overflow-y: hidden; /* 默认隐藏滚动条，由JS控制 */
  transition: height 0.2s ease; /* 高度变化动画 */
  line-height: 1.4; /* 设置行高 */
  box-sizing: border-box; /* 确保padding和border包含在高度内 */
}

/* 文本域特殊样式 */
textarea.auto-resize,
textarea#extraRequirement {
  min-height: 60px; /* 文本域更大的最小高度 */
  max-height: 300px; /* 文本域更大的最大高度 */
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .auto-resize,
  #extraRequirement,
  #pickup,
  #dropoff,
  #customerName,
  #flightInfo,
  #otaReferenceNumber,
  #orderInput {
    min-height: 30px;
    max-height: 150px;
  }

  textarea.auto-resize,
  textarea#extraRequirement {
    min-height: 50px;
    max-height: 200px;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  #extraRequirement {
    min-height: 50px; /* 移动端减小最小高度 */
    max-height: 150px; /* 移动端减小最大高度 */
    font-size: var(--font-size-sm); /* 移动端使用较小字体 */
  }
}

/* =================================
   选择器
   ================================= */
select {
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: calc(var(--spacing-6) + 16px);
  appearance: none;
}

/* =================================
   复选框和单选框
   ================================= */
.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.checkbox-group-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

input[type="checkbox"],
input[type="radio"] {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}

/* =================================
   表单操作区 - 固定在页面底部
   ================================= */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  backdrop-filter: var(--blur-glass);
  border-top: 1px solid var(--glass-border);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0;
}

/* =================================
   特殊输入组件
   ================================= */
.compact-price-input {
  display: flex;
  align-items: center;
  gap: 1px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  min-height: 36px;
}

.compact-price-input input {
  flex: 1;
  border: none;
  background: transparent;
  padding: var(--spacing-2) var(--spacing-3);
  min-width: 0;
}

.compact-price-input select {
  width: 20% !important;
  min-width: 40px !important;
  border: none;
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  font-size: var(--font-size-xs);
}

/* =================================
   上传组件
   ================================= */
.compact-upload-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.btn-compact-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-compact-upload:hover {
  background: var(--color-primary-hover);
  transform: scale(1.05);
}

.upload-icon {
  font-size: 12.6px; /* 14px * 0.9 */
}

/* =================================
   验证状态
   ================================= */
.form-group.invalid input,
.form-group.invalid textarea,
.form-group.invalid select {
  border-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
}

.form-group.valid input,
.form-group.valid textarea,
.form-group.valid select {
  border-color: var(--color-success);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

/* 字段级错误提示样式 */
.field-error {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: 2px;
  padding: 2px 4px;
  background: var(--color-error-light);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-error);
  display: block;
  animation: fadeIn 0.2s ease-in;
}

/* 输入框错误状态 - 红色边框 */
input.error,
textarea.error,
select.error {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1) !important;
}

.error-message {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: var(--spacing-1);
}

.success-message {
  font-size: var(--font-size-xs);
  color: var(--color-success);
  margin-top: var(--spacing-1);
}

/* 错误提示淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =================================
   移动端优化
   ================================= */
@media (max-width: 768px) {
  .form-group label {
    font-size: var(--font-size-mobile-sm);
  }
  
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  input[type="number"],
  input[type="date"],
  input[type="time"],
  textarea,
  select {
    font-size: 10px;
    padding: var(--spacing-3) var(--spacing-4);
    min-height: 30px;
  }
  
  .form-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    flex-direction: row;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    justify-content: center;
    background: var(--bg-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    border-top: 1px solid var(--glass-border);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }
  
  .form-actions .btn {
    flex: 1;
    max-width: 200px;
    min-height: 44px;
  }
  
  .compact-price-input {
    flex-direction: row;
    height: 35px;
    min-width: 150px;
  }
  
  .compact-price-input input {
    width: 45%;
    flex: 1;
  }
}

@media (max-width: 480px) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  input[type="number"],
  input[type="date"],
  input[type="time"],
  textarea,
  select {
    font-size: var(--mobile-compact-sm);
    padding: var(--mobile-ultra-md);
  }
  
  .compact-price-input {
    min-width: 150px;
  }
}

/* =================================
   语言选择下拉菜单组件
   ================================= */
.language-selection-wrapper {
  position: relative;
  width: 100%;
}

.language-dropdown {
  position: relative;
  width: 100%;
}

.language-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  background: var(--bg-glass);
  cursor: pointer;
  min-height: 20px;
  box-sizing: border-box;
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);
}

.language-dropdown-header:hover {
  border-color: var(--color-primary);
  background-color: var(--brand-glass);
}

.dropdown-arrow {
  font-size: 9px; /* 10px * 0.9 */
  color: var(--color-primary);
  transition: transform var(--transition-fast) ease;
}

.language-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  backdrop-filter: var(--blur-glass);
  border: 1px solid var(--glass-border);
  border-top: none;
  border-radius: 0 0 var(--radius-sm) var(--radius-sm);
  z-index: var(--z-dropdown);
  max-height: 150px;
  overflow-y: auto;
  box-shadow: var(--shadow-card);
}

.language-checkbox-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  cursor: pointer;
  transition: background-color var(--transition-fast) ease;
  border-bottom: 1px solid var(--border-color);
}

.language-checkbox-item:last-child {
  border-bottom: none;
}

.language-checkbox-item:hover {
  background-color: var(--color-gray-50);
}

.language-checkbox-item input[type="checkbox"] {
  margin: 0;
  margin-right: var(--spacing-3);
}

.language-name {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.language-checkbox-item input:checked ~ .language-name {
  font-weight: 600;
  color: var(--color-primary);
}

/* =================================
   水平并列表单组样式
   ================================= */
.form-group-horizontal {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-1);
}

.form-group-horizontal .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* =================================
   响应式设计
   ================================= */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 0; /* 进一步紧凑化移动端间距 */
  }

  /* 移动端图标相关样式已移除 - 不再使用图标 */

  /* 移动端水平并列表单组调整为垂直布局 */
  .form-group-horizontal {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  /* 移动端图标文本样式已移除 */

  .checkbox-group,
  .radio-group {
    gap: var(--spacing-3);
  }

  /* 语言下拉菜单移动端适配 */
  .language-dropdown-content {
    max-height: 150px;
  }

  .language-checkbox-item {
    padding: var(--mobile-spacing-md);
  }

  .language-name {
    font-size: var(--font-size-base);
  }
}