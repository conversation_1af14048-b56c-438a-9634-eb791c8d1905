<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史订单测试工具</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6;
        }
        .section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { 
            background: #f5f5f5; 
            padding: 10px; 
            overflow-x: auto; 
            border-radius: 3px;
            font-size: 12px;
        }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            border: none;
            border-radius: 3px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .test-group {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: white;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔧 历史订单模块测试工具</h1>
    <p>此工具用于诊断和测试历史订单模块的显示和持久化问题。</p>
    
    <div class="test-group section">
        <h2>1. 环境检查</h2>
        <button onclick="checkEnvironment()">检查运行环境</button>
        <div id="env-result" class="result"></div>
    </div>
    
    <div class="test-group section">
        <h2>2. localStorage 数据检查</h2>
        <button onclick="checkLocalStorage()">检查存储数据</button>
        <button onclick="clearLocalStorage()">清空存储数据</button>
        <div id="storage-result" class="result"></div>
    </div>
    
    <div class="test-group section">
        <h2>3. 历史管理器测试</h2>
        <button onclick="testHistoryManager()">测试管理器</button>
        <button onclick="addTestOrders()">添加测试数据</button>
        <button onclick="testGetHistory()">测试获取历史</button>
        <div id="manager-result" class="result"></div>
    </div>
    
    <div class="test-group section">
        <h2>4. UI 渲染测试</h2>
        <button onclick="testRenderHistory()">测试渲染</button>
        <button onclick="testShowPanel()">测试显示面板</button>
        <button onclick="testStatistics()">测试统计更新</button>
        <div id="ui-result" class="result"></div>
    </div>

    <!-- 模拟的DOM元素 -->
    <div style="display: none;">
        <div id="historyPanel" class="hidden">
            <div id="historyListContainer"></div>
            <div id="listCount">0</div>
            <div id="statTotal">0</div>
            <div id="statToday">0</div>
            <div id="statWeek">0</div>
            <div id="statMonth">0</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/order-history-manager.js"></script>
    
    <script>
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResult(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkEnvironment() {
            clearResult('env-result');
            
            // 检查必要的全局函数和对象
            const checks = [
                { name: 'getLogger', obj: window.getLogger },
                { name: 'getAppState', obj: window.getAppState },
                { name: 'getOrderHistoryManager', obj: window.getOrderHistoryManager },
                { name: 'OrderHistoryManager', obj: window.OrderHistoryManager },
                { name: 'localStorage', obj: window.localStorage }
            ];

            checks.forEach(check => {
                if (typeof check.obj !== 'undefined') {
                    logResult('env-result', `✅ ${check.name} 可用`, 'success');
                } else {
                    logResult('env-result', `❌ ${check.name} 不可用`, 'error');
                }
            });

            // 检查DOM元素
            const domElements = ['historyPanel', 'historyListContainer', 'listCount'];
            domElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    logResult('env-result', `✅ DOM元素 ${id} 存在`, 'success');
                } else {
                    logResult('env-result', `❌ DOM元素 ${id} 不存在`, 'error');
                }
            });
        }

        function checkLocalStorage() {
            clearResult('storage-result');
            
            try {
                const storageKey = 'ota_order_history';
                const data = localStorage.getItem(storageKey);
                
                if (data) {
                    const parsed = JSON.parse(data);
                    logResult('storage-result', `✅ 找到历史数据`, 'success');
                    logResult('storage-result', `数据大小: ${Math.round(data.length / 1024)}KB`, 'info');
                    
                    const userCount = Object.keys(parsed).length;
                    let totalOrders = 0;
                    Object.values(parsed).forEach(userOrders => {
                        if (Array.isArray(userOrders)) {
                            totalOrders += userOrders.length;
                        }
                    });
                    
                    logResult('storage-result', `用户数: ${userCount}, 总订单数: ${totalOrders}`, 'info');
                    
                    // 显示部分数据
                    const preview = JSON.stringify(parsed, null, 2).substring(0, 500) + '...';
                    document.getElementById('storage-result').innerHTML += `<pre>${preview}</pre>`;
                } else {
                    logResult('storage-result', `⚠️ 未找到历史数据`, 'warning');
                }
            } catch (error) {
                logResult('storage-result', `❌ 检查失败: ${error.message}`, 'error');
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.removeItem('ota_order_history');
                logResult('storage-result', `✅ 存储数据已清空`, 'success');
            } catch (error) {
                logResult('storage-result', `❌ 清空失败: ${error.message}`, 'error');
            }
        }

        function testHistoryManager() {
            clearResult('manager-result');
            
            try {
                if (typeof getOrderHistoryManager !== 'function') {
                    logResult('manager-result', `❌ getOrderHistoryManager 函数不存在`, 'error');
                    return;
                }

                const manager = getOrderHistoryManager();
                if (!manager) {
                    logResult('manager-result', `❌ 管理器实例为null`, 'error');
                    return;
                }

                logResult('manager-result', `✅ 管理器实例创建成功`, 'success');
                
                // 测试基本方法
                const currentUser = manager.getCurrentUser();
                logResult('manager-result', `当前用户: ${currentUser}`, 'info');
                
                const history = manager.getHistory();
                logResult('manager-result', `历史订单数: ${history.length}`, 'info');
                
                const stats = manager.getStatistics();
                logResult('manager-result', `统计信息: 总计${stats.total}, 今日${stats.today}, 本周${stats.week}, 本月${stats.month}`, 'info');
                
            } catch (error) {
                logResult('manager-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function addTestOrders() {
            try {
                const manager = getOrderHistoryManager();
                if (!manager) {
                    logResult('manager-result', `❌ 管理器不可用`, 'error');
                    return;
                }

                // 添加成功订单
                const successOrder = {
                    customerName: '张三',
                    customerContact: '+60123456789',
                    customerEmail: '<EMAIL>',
                    pickup: '吉隆坡国际机场',
                    destination: '双子塔',
                    date: '2025-08-06',
                    time: '14:30',
                    passengerNumber: '2',
                    luggageNumber: '3',
                    otaReferenceNumber: 'SUCCESS' + Date.now(),
                    otaChannel: 'Test Channel'
                };

                manager.addOrder(successOrder, 'ORDER_' + Date.now(), {
                    success: true,
                    message: '订单创建成功'
                });

                // 添加失败订单
                const failedOrder = {
                    customerName: '李四',
                    customerContact: '+60123456788',
                    customerEmail: '<EMAIL>',
                    pickup: '槟城机场',
                    destination: '乔治市',
                    date: '2025-08-06',
                    time: '16:00',
                    passengerNumber: '1',
                    luggageNumber: '2',
                    otaReferenceNumber: 'FAILED' + Date.now(),
                    otaChannel: 'Test Channel'
                };

                manager.addOrder(failedOrder, 'FAILED_' + Date.now(), {
                    success: false,
                    message: '验证失败：缺少必要信息'
                });

                logResult('manager-result', `✅ 测试订单已添加`, 'success');
                
                // 重新检查数据
                const history = manager.getHistory();
                logResult('manager-result', `更新后订单数: ${history.length}`, 'info');
                
            } catch (error) {
                logResult('manager-result', `❌ 添加测试订单失败: ${error.message}`, 'error');
            }
        }

        function testGetHistory() {
            try {
                const manager = getOrderHistoryManager();
                const history = manager.getHistory();
                
                logResult('manager-result', `获取到 ${history.length} 条历史订单`, 'info');
                
                if (history.length > 0) {
                    const latest = history[0];
                    logResult('manager-result', `最新订单: ${latest.orderId} - ${latest.orderData?.customerName}`, 'info');
                    
                    // 显示详细信息
                    const details = JSON.stringify(latest, null, 2);
                    document.getElementById('manager-result').innerHTML += `<pre>${details.substring(0, 300)}...</pre>`;
                }
                
            } catch (error) {
                logResult('manager-result', `❌ 获取历史失败: ${error.message}`, 'error');
            }
        }

        function testRenderHistory() {
            clearResult('ui-result');
            
            try {
                const manager = getOrderHistoryManager();
                const history = manager.getHistory();
                
                logResult('ui-result', `开始渲染 ${history.length} 条订单`, 'info');
                
                manager.renderHistory(history);
                
                const container = document.getElementById('historyListContainer');
                if (container && container.innerHTML.trim()) {
                    logResult('ui-result', `✅ 渲染成功，内容长度: ${container.innerHTML.length}`, 'success');
                    
                    // 显示渲染的HTML片段
                    const preview = container.innerHTML.substring(0, 200) + '...';
                    document.getElementById('ui-result').innerHTML += `<pre>${preview}</pre>`;
                } else {
                    logResult('ui-result', `⚠️ 容器为空或未找到`, 'warning');
                }
                
            } catch (error) {
                logResult('ui-result', `❌ 渲染失败: ${error.message}`, 'error');
            }
        }

        function testShowPanel() {
            try {
                const manager = getOrderHistoryManager();
                manager.showHistoryPanel();
                
                const panel = document.getElementById('historyPanel');
                if (panel && !panel.classList.contains('hidden')) {
                    logResult('ui-result', `✅ 面板显示成功`, 'success');
                } else {
                    logResult('ui-result', `⚠️ 面板可能未正确显示`, 'warning');
                }
                
            } catch (error) {
                logResult('ui-result', `❌ 显示面板失败: ${error.message}`, 'error');
            }
        }

        function testStatistics() {
            try {
                const manager = getOrderHistoryManager();
                const history = manager.getHistory();
                
                manager.updateStatistics(history);
                manager.updateRecordCount(history.length);
                
                const stats = {
                    total: document.getElementById('statTotal')?.textContent || '未更新',
                    today: document.getElementById('statToday')?.textContent || '未更新',
                    week: document.getElementById('statWeek')?.textContent || '未更新',
                    month: document.getElementById('statMonth')?.textContent || '未更新',
                    count: document.getElementById('listCount')?.textContent || '未更新'
                };
                
                logResult('ui-result', `统计更新结果: 总计${stats.total}, 今日${stats.today}, 记录数${stats.count}`, 'info');
                
            } catch (error) {
                logResult('ui-result', `❌ 统计更新失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动检查环境
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkEnvironment();
            }, 1000);
        });
    </script>
</body>
</html>
