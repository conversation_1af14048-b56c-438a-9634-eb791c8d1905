<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单OTA渠道同步测试</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-title { 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 10px; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        button { 
            margin: 5px; 
            padding: 8px 16px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        button:hover { background: #0056b3; }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            background: #f8f9fa; 
            border-left: 4px solid #007bff; 
        }
        .mock-panel {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        select { 
            padding: 5px 10px; 
            margin: 5px; 
            min-width: 200px; 
        }
    </style>
</head>
<body>
    <h1>🔧 多订单OTA渠道同步功能测试</h1>
    <p>本页面用于测试多订单模组的OTA渠道批量设置和API发送功能</p>

    <!-- 模拟用户状态 -->
    <div class="test-section">
        <div class="test-title">1. 用户状态模拟</div>
        <label>选择测试用户:</label>
        <select id="userSelect" onchange="changeUser()">
            <option value="">请选择用户</option>
            <option value="2666">JR Coach (ID: 2666) - 专属渠道</option>
            <option value="2446">UCSI - Cheras (ID: 2446) - 教育机构</option>
            <option value="common">通用用户 - 使用通用渠道</option>
        </select>
        <div id="userStatus" class="test-result"></div>
    </div>

    <!-- 批量OTA选择器测试 -->
    <div class="test-section">
        <div class="test-title">2. 批量OTA选择器测试</div>
        <div class="mock-panel">
            <label>批量OTA渠道选择:</label>
            <select id="batchOtaSelect">
                <option value="">选择OTA渠道</option>
            </select>
            <span id="otaChannelInfo" class="info"></span>
        </div>
        <button onclick="testOtaChannelSync()">测试OTA渠道同步</button>
        <div id="otaSyncResult" class="test-result"></div>
    </div>

    <!-- 订单数据映射测试 -->
    <div class="test-section">
        <div class="test-title">3. 订单字段映射测试</div>
        <button onclick="testFieldMapping()">测试字段映射</button>
        <div id="fieldMappingResult" class="test-result"></div>
    </div>

    <!-- 批量API提交测试 -->
    <div class="test-section">
        <div class="test-title">4. 批量API提交功能测试</div>
        <button onclick="testBatchSubmissionSetup()">测试批量提交初始化</button>
        <button onclick="testBatchValidation()">测试订单验证</button>
        <div id="batchSubmissionResult" class="test-result"></div>
    </div>

    <!-- 系统完整性检查 -->
    <div class="test-section">
        <div class="test-title">5. 系统完整性检查</div>
        <button onclick="runSystemIntegrityCheck()">运行完整性检查</button>
        <div id="systemCheckResult" class="test-result"></div>
    </div>

    <!-- 加载基础模块 -->
    <script src="../js/logger.js"></script>
    <script src="../js/ota-channel-mapping.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/multi-order-manager-v2.js"></script>

    <script>
        // 测试脚本
        let testManager = null;

        // 模拟用户切换
        function changeUser() {
            const userSelect = document.getElementById('userSelect');
            const userStatus = document.getElementById('userStatus');
            const userId = userSelect.value;

            let userInfo = null;
            if (userId === '2666') {
                userInfo = { id: 2666, email: '<EMAIL>', name: 'JR Coach' };
            } else if (userId === '2446') {
                userInfo = { id: 2446, email: '<EMAIL>', name: 'UCSI User' };
            } else if (userId === 'common') {
                userInfo = { id: 9999, email: '<EMAIL>', name: 'Common User' };
            }

            // 模拟设置当前用户
            if (window.OTA && window.OTA.AppState) {
                window.OTA.AppState.setCurrentUser(userInfo);
                userStatus.innerHTML = `✅ 已设置用户: ${userInfo ? userInfo.name : '无'} (ID: ${userInfo ? userInfo.id : 'N/A'})`;
                userStatus.className = 'test-result success';
                
                // 重新填充OTA选项
                populateOtaOptions();
            } else {
                userStatus.innerHTML = '❌ AppState未初始化';
                userStatus.className = 'test-result error';
            }
        }

        // 填充OTA选项（模拟多订单管理器的逻辑）
        function populateOtaOptions() {
            const batchOtaSelect = document.getElementById('batchOtaSelect');
            const otaChannelInfo = document.getElementById('otaChannelInfo');

            if (!window.OTA?.otaChannelMapping) {
                otaChannelInfo.textContent = '❌ OTA渠道映射未加载';
                return;
            }

            // 获取当前用户
            const user = window.OTA.AppState?.getCurrentUser();
            let otaConfig = null;

            if (user) {
                if (user.id) {
                    otaConfig = window.OTA.otaChannelMapping.getConfig(user.id);
                }
                if (!otaConfig && user.email) {
                    otaConfig = window.OTA.otaChannelMapping.getConfig(user.email);
                }
            }

            // 清空选项
            batchOtaSelect.innerHTML = '<option value="">选择OTA渠道</option>';

            // 确定渠道列表
            let channelsToUse;
            let configType;
            
            if (otaConfig && otaConfig.options) {
                channelsToUse = otaConfig.options;
                configType = '用户专属';
            } else {
                channelsToUse = window.OTA.otaChannelMapping.commonChannels || [];
                configType = '通用';
            }

            // 填充选项
            channelsToUse.forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.value;
                option.textContent = channel.text;
                batchOtaSelect.appendChild(option);
            });

            // 设置默认值
            if (otaConfig && otaConfig.default) {
                batchOtaSelect.value = otaConfig.default;
            }

            otaChannelInfo.textContent = `${configType}配置 - ${channelsToUse.length} 个渠道${otaConfig?.default ? ', 默认: ' + otaConfig.default : ''}`;
        }

        // 测试OTA渠道同步
        function testOtaChannelSync() {
            const resultDiv = document.getElementById('otaSyncResult');
            const batchOtaSelect = document.getElementById('batchOtaSelect');
            
            try {
                // 检查多订单管理器
                if (!window.OTA?.multiOrderManagerV2) {
                    throw new Error('MultiOrderManagerV2 未初始化');
                }

                const manager = window.OTA.multiOrderManagerV2;
                
                // 模拟订单数据
                const mockOrders = [
                    { id: 1, pickup: '测试地点1', destination: '测试目的地1', date: '2025-08-07', time: '10:00' },
                    { id: 2, pickup: '测试地点2', destination: '测试目的地2', date: '2025-08-07', time: '14:00' }
                ];
                
                manager.state.parsedOrders = mockOrders;
                manager.state.selectedOrders = new Set([0, 1]); // 选中所有订单

                // 测试OTA渠道应用
                const selectedOta = batchOtaSelect.value;
                if (!selectedOta) {
                    throw new Error('请先选择OTA渠道');
                }

                manager.applyBatchOtaToSelected(selectedOta);

                // 检查结果
                const updatedOrders = manager.state.parsedOrders;
                const hasCorrectMapping = updatedOrders.every(order => 
                    order.ota === selectedOta && order._otaChannel === selectedOta
                );

                if (hasCorrectMapping) {
                    resultDiv.innerHTML = `✅ OTA渠道同步成功！<br>
                        - 选择的OTA: ${selectedOta}<br>
                        - 应用到 ${updatedOrders.length} 个订单<br>
                        - API字段 (ota): ✓<br>
                        - 内部字段 (_otaChannel): ✓`;
                    resultDiv.className = 'test-result success';
                } else {
                    throw new Error('字段映射不正确');
                }

            } catch (error) {
                resultDiv.innerHTML = `❌ 测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 测试字段映射
        function testFieldMapping() {
            const resultDiv = document.getElementById('fieldMappingResult');
            
            try {
                const manager = window.OTA.multiOrderManagerV2;
                if (!manager) {
                    throw new Error('多订单管理器未初始化');
                }

                // 创建测试订单
                const testOrder = {
                    id: 'test-001',
                    pickup: 'KLIA Terminal 1',
                    destination: 'Kuala Lumpur City Center',
                    date: '2025-08-07',
                    time: '15:30',
                    ota: 'Klook West Malaysia'
                };

                // 验证字段
                const validation = manager.validateOrdersForSubmission([testOrder]);
                
                if (validation.isValid) {
                    resultDiv.innerHTML = `✅ 字段映射测试通过！<br>
                        - 必填字段检查: ✓<br>
                        - OTA字段 (ota): "${testOrder.ota}"<br>
                        - 接送地点: "${testOrder.pickup}" → "${testOrder.destination}"<br>
                        - 日期时间: ${testOrder.date} ${testOrder.time}`;
                    resultDiv.className = 'test-result success';
                } else {
                    throw new Error(`验证失败: ${validation.errors.join(', ')}`);
                }

            } catch (error) {
                resultDiv.innerHTML = `❌ 测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 测试批量提交初始化
        function testBatchSubmissionSetup() {
            const resultDiv = document.getElementById('batchSubmissionResult');
            
            try {
                const manager = window.OTA.multiOrderManagerV2;
                if (!manager) {
                    throw new Error('多订单管理器未初始化');
                }

                // 检查批量提交按钮是否存在（模拟）
                let buttonExists = false;
                try {
                    const btn = document.getElementById('createSelectedOrdersBtn');
                    buttonExists = !!btn;
                } catch (e) {
                    // 按钮不存在是正常的，这里是测试页面
                }

                // 检查方法是否存在
                const methodsExist = {
                    handleBatchSubmission: typeof manager.handleBatchSubmission === 'function',
                    validateOrdersForSubmission: typeof manager.validateOrdersForSubmission === 'function',
                    submitOrdersBatch: typeof manager.submitOrdersBatch === 'function',
                    showBatchSubmissionProgress: typeof manager.showBatchSubmissionProgress === 'function'
                };

                const allMethodsExist = Object.values(methodsExist).every(Boolean);

                if (allMethodsExist) {
                    resultDiv.innerHTML = `✅ 批量提交功能初始化完成！<br>
                        - handleBatchSubmission: ✓<br>
                        - validateOrdersForSubmission: ✓<br>
                        - submitOrdersBatch: ✓<br>
                        - showBatchSubmissionProgress: ✓<br>
                        - 按钮绑定: ${buttonExists ? '✓ (在主页面)' : '⚠️ (测试页面无按钮)'}`;
                    resultDiv.className = 'test-result success';
                } else {
                    const missingMethods = Object.keys(methodsExist).filter(method => !methodsExist[method]);
                    throw new Error(`缺少方法: ${missingMethods.join(', ')}`);
                }

            } catch (error) {
                resultDiv.innerHTML = `❌ 测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 测试订单验证
        function testBatchValidation() {
            const resultDiv = document.getElementById('batchSubmissionResult');
            
            try {
                const manager = window.OTA.multiOrderManagerV2;
                
                // 测试有效订单
                const validOrders = [{
                    ota: 'Klook West Malaysia',
                    pickup: 'KLIA',
                    destination: 'KL Sentral',
                    date: '2025-08-07',
                    time: '10:00'
                }];

                // 测试无效订单
                const invalidOrders = [{
                    // 缺少必填字段
                    pickup: 'KLIA'
                }];

                const validResult = manager.validateOrdersForSubmission(validOrders);
                const invalidResult = manager.validateOrdersForSubmission(invalidOrders);

                if (validResult.isValid && !invalidResult.isValid) {
                    resultDiv.innerHTML = `✅ 订单验证功能正常！<br>
                        - 有效订单验证: ✓ 通过<br>
                        - 无效订单验证: ✓ 拒绝<br>
                        - 错误信息: "${invalidResult.errors[0]}"`;
                    resultDiv.className = 'test-result success';
                } else {
                    throw new Error('验证逻辑异常');
                }

            } catch (error) {
                resultDiv.innerHTML = `❌ 测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 系统完整性检查
        function runSystemIntegrityCheck() {
            const resultDiv = document.getElementById('systemCheckResult');
            
            try {
                const checks = {
                    'OTA渠道映射': !!window.OTA?.otaChannelMapping,
                    'AppState服务': !!window.OTA?.AppState,
                    'API服务': !!getApiService,
                    '多订单管理器V2': !!window.OTA?.multiOrderManagerV2,
                    '批量OTA填充': !!window.OTA?.multiOrderManagerV2?.populateOtaOptions,
                    '用户配置获取': !!window.OTA?.otaChannelMapping?.getConfig,
                    '批量提交功能': !!window.OTA?.multiOrderManagerV2?.handleBatchSubmission
                };

                const passedChecks = Object.keys(checks).filter(check => checks[check]);
                const failedChecks = Object.keys(checks).filter(check => !checks[check]);

                let html = `<strong>系统完整性检查结果:</strong><br>`;
                html += `✅ 通过: ${passedChecks.length}/${Object.keys(checks).length}<br>`;
                
                if (passedChecks.length > 0) {
                    html += `<br><strong>通过的检查:</strong><br>`;
                    passedChecks.forEach(check => {
                        html += `  ✓ ${check}<br>`;
                    });
                }

                if (failedChecks.length > 0) {
                    html += `<br><strong>失败的检查:</strong><br>`;
                    failedChecks.forEach(check => {
                        html += `  ❌ ${check}<br>`;
                    });
                }

                resultDiv.innerHTML = html;
                resultDiv.className = failedChecks.length === 0 ? 'test-result success' : 'test-result warning';

            } catch (error) {
                resultDiv.innerHTML = `❌ 检查失败: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                runSystemIntegrityCheck();
                console.log('🧪 多订单OTA渠道同步测试页面已加载');
            }, 1000);
        });
    </script>
</body>
</html>