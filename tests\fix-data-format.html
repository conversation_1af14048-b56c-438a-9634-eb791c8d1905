<!DOCTYPE html>
<html>
<head>
    <title>修复历史订单数据格式</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 300px; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a8b; }
        .fix-button { background: #28a745; }
        .fix-button:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <h1>历史订单数据格式修复工具</h1>
    
    <div class="section">
        <h2>1. 检查当前数据格式</h2>
        <button onclick="analyzeData()">分析数据结构</button>
        <div id="analysis-result"></div>
    </div>
    
    <div class="section">
        <h2>2. 数据修复选项</h2>
        <button onclick="fixDataFormat()" class="fix-button">自动修复数据格式</button>
        <button onclick="createSampleData()" class="fix-button">创建样本数据</button>
        <button onclick="clearAllData()" style="background: #dc3545;">清空所有数据</button>
        <div id="fix-result"></div>
    </div>
    
    <div class="section">
        <h2>3. 测试渲染</h2>
        <button onclick="testRendering()">测试订单渲染</button>
        <div id="render-result"></div>
    </div>

    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/order-history-manager.js"></script>
    
    <script>
        function log(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function analyzeData() {
            const result = document.getElementById('analysis-result');
            result.innerHTML = '<h3>数据分析结果:</h3>';
            
            try {
                const historyData = localStorage.getItem('ota_order_history');
                if (!historyData) {
                    result.innerHTML += '<p class="warning">⚠️ 没有找到历史订单数据</p>';
                    return;
                }
                
                const parsed = JSON.parse(historyData);
                result.innerHTML += '<p class="success">✅ 数据解析成功</p>';
                
                // 分析数据结构
                result.innerHTML += '<h4>数据结构分析:</h4>';
                result.innerHTML += `<p>数据类型: ${Array.isArray(parsed) ? '数组' : '对象'}</p>`;
                
                if (Array.isArray(parsed)) {
                    result.innerHTML += '<p class="warning">⚠️ 检测到旧的数组格式，需要迁移</p>';
                    result.innerHTML += `<p>订单数量: ${parsed.length}</p>`;
                    
                    if (parsed.length > 0) {
                        result.innerHTML += '<h4>第一个订单结构:</h4>';
                        result.innerHTML += '<pre>' + JSON.stringify(parsed[0], null, 2) + '</pre>';
                    }
                } else {
                    result.innerHTML += '<p class="success">✅ 检测到新的用户分组格式</p>';
                    const userCount = Object.keys(parsed).length;
                    result.innerHTML += `<p>用户数量: ${userCount}</p>`;
                    
                    let totalOrders = 0;
                    let hasValidData = false;
                    
                    Object.keys(parsed).forEach(user => {
                        const userOrders = parsed[user];
                        if (Array.isArray(userOrders)) {
                            totalOrders += userOrders.length;
                            result.innerHTML += `<p>用户 ${user}: ${userOrders.length} 个订单</p>`;
                            
                            if (userOrders.length > 0) {
                                hasValidData = true;
                                result.innerHTML += '<h4>样本订单结构:</h4>';
                                result.innerHTML += '<pre>' + JSON.stringify(userOrders[0], null, 2) + '</pre>';
                            }
                        }
                    });
                    
                    result.innerHTML += `<p>总订单数: ${totalOrders}</p>`;
                    
                    if (!hasValidData) {
                        result.innerHTML += '<p class="warning">⚠️ 没有找到有效的订单数据</p>';
                    }
                }
                
                // 检查当前用户
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    const currentUser = manager.getCurrentUser();
                    result.innerHTML += `<p class="info">当前用户: ${currentUser}</p>`;
                    
                    const userHistory = manager.getHistory();
                    result.innerHTML += `<p class="info">当前用户订单数: ${userHistory.length}</p>`;
                }
                
            } catch (error) {
                result.innerHTML += '<p class="error">❌ 数据分析失败: ' + error.message + '</p>';
            }
        }
        
        function fixDataFormat() {
            const result = document.getElementById('fix-result');
            result.innerHTML = '<h3>修复结果:</h3>';
            
            try {
                const historyData = localStorage.getItem('ota_order_history');
                if (!historyData) {
                    result.innerHTML += '<p class="warning">⚠️ 没有数据需要修复</p>';
                    return;
                }
                
                const parsed = JSON.parse(historyData);
                
                // 如果是数组格式，需要迁移
                if (Array.isArray(parsed)) {
                    result.innerHTML += '<p class="info">🔄 开始迁移数组格式到用户分组格式...</p>';
                    
                    const newFormat = {};
                    const defaultUser = 'default_user';
                    
                    // 迁移每个订单
                    parsed.forEach((order, index) => {
                        try {
                            // 尝试从订单中获取用户信息
                            let userKey = defaultUser;
                            if (order.metadata && order.metadata.userEmail) {
                                userKey = order.metadata.userEmail;
                            } else if (order.orderData && order.orderData.customerEmail) {
                                userKey = order.orderData.customerEmail;
                            }
                            
                            if (!newFormat[userKey]) {
                                newFormat[userKey] = [];
                            }
                            
                            // 确保订单有正确的结构
                            const fixedOrder = {
                                id: order.id || `hist_${Date.now()}_${index}`,
                                orderId: order.orderId || `ORDER_${Date.now()}_${index}`,
                                timestamp: order.timestamp || Date.now(),
                                orderData: {
                                    customerName: order.orderData?.customerName || order.customerName || '',
                                    customerContact: order.orderData?.customerContact || order.customerContact || '',
                                    customerEmail: order.orderData?.customerEmail || order.customerEmail || '',
                                    pickup: order.orderData?.pickup || order.pickup || '',
                                    destination: order.orderData?.destination || order.destination || '',
                                    date: order.orderData?.date || order.date || '',
                                    time: order.orderData?.time || order.time || '',
                                    passengerNumber: order.orderData?.passengerNumber || order.passengerNumber || '',
                                    luggageNumber: order.orderData?.luggageNumber || order.luggageNumber || '',
                                    otaReferenceNumber: order.orderData?.otaReferenceNumber || order.otaReferenceNumber || '',
                                    otaChannel: order.orderData?.otaChannel || order.otaChannel || '',
                                    subCategoryId: order.orderData?.subCategoryId || order.subCategoryId || '',
                                    flightInfo: order.orderData?.flightInfo || order.flightInfo || '',
                                    specialRequests: order.orderData?.specialRequests || order.specialRequests || ''
                                },
                                metadata: order.metadata || {
                                    userEmail: userKey,
                                    createdBy: 'Migration Tool',
                                    apiResponse: {
                                        success: true,
                                        message: '数据迁移'
                                    }
                                }
                            };
                            
                            newFormat[userKey].push(fixedOrder);
                        } catch (orderError) {
                            result.innerHTML += `<p class="warning">⚠️ 订单 ${index} 迁移失败: ${orderError.message}</p>`;
                        }
                    });
                    
                    // 保存新格式
                    localStorage.setItem('ota_order_history', JSON.stringify(newFormat));
                    result.innerHTML += '<p class="success">✅ 数据迁移完成</p>';
                    result.innerHTML += `<p>迁移了 ${Object.keys(newFormat).length} 个用户的数据</p>`;
                    
                } else {
                    // 修复现有的对象格式数据
                    result.innerHTML += '<p class="info">🔄 修复现有用户分组格式数据...</p>';
                    
                    let fixed = false;
                    Object.keys(parsed).forEach(user => {
                        const userOrders = parsed[user];
                        if (Array.isArray(userOrders)) {
                            userOrders.forEach((order, index) => {
                                // 确保每个订单都有正确的数据结构
                                if (!order.orderData) {
                                    order.orderData = {};
                                    fixed = true;
                                }
                                
                                // 修复缺失的字段
                                const requiredFields = ['customerName', 'pickup', 'destination', 'otaReferenceNumber'];
                                requiredFields.forEach(field => {
                                    if (!order.orderData[field] && order[field]) {
                                        order.orderData[field] = order[field];
                                        fixed = true;
                                    }
                                });
                                
                                // 确保有 metadata
                                if (!order.metadata) {
                                    order.metadata = {
                                        userEmail: user,
                                        createdBy: 'Auto Fix',
                                        apiResponse: {
                                            success: true,
                                            message: '数据修复'
                                        }
                                    };
                                    fixed = true;
                                }
                            });
                        }
                    });
                    
                    if (fixed) {
                        localStorage.setItem('ota_order_history', JSON.stringify(parsed));
                        result.innerHTML += '<p class="success">✅ 数据结构修复完成</p>';
                    } else {
                        result.innerHTML += '<p class="success">✅ 数据格式已经正确，无需修复</p>';
                    }
                }
                
                // 验证修复结果
                analyzeData();
                
            } catch (error) {
                result.innerHTML += '<p class="error">❌ 修复失败: ' + error.message + '</p>';
            }
        }
        
        function createSampleData() {
            const result = document.getElementById('fix-result');
            result.innerHTML = '<h3>创建样本数据:</h3>';
            
            try {
                const manager = getOrderHistoryManager();
                const currentUser = manager.getCurrentUser();
                
                const sampleOrders = [
                    {
                        customerName: '张三',
                        customerContact: '+60123456789',
                        customerEmail: '<EMAIL>',
                        pickup: '吉隆坡国际机场',
                        destination: '双子塔',
                        date: '2025-08-06',
                        time: '14:30',
                        passengerNumber: '2',
                        luggageNumber: '3',
                        otaReferenceNumber: 'SAMPLE001',
                        otaChannel: 'Booking.com',
                        subCategoryId: 'airport_transfer'
                    },
                    {
                        customerName: '李四',
                        customerContact: '+60198765432',
                        customerEmail: '<EMAIL>',
                        pickup: '市中心酒店',
                        destination: '云顶高原',
                        date: '2025-08-06',
                        time: '09:00',
                        passengerNumber: '4',
                        luggageNumber: '2',
                        otaReferenceNumber: 'SAMPLE002',
                        otaChannel: 'Agoda',
                        subCategoryId: 'day_trip'
                    },
                    {
                        customerName: '王五',
                        customerContact: '+60187654321',
                        customerEmail: '<EMAIL>',
                        pickup: '吉隆坡中央车站',
                        destination: '马六甲古城',
                        date: '2025-08-07',
                        time: '08:00',
                        passengerNumber: '3',
                        luggageNumber: '4',
                        otaReferenceNumber: 'SAMPLE003',
                        otaChannel: 'Expedia',
                        subCategoryId: 'city_tour'
                    }
                ];
                
                sampleOrders.forEach((orderData, index) => {
                    const orderId = `SAMPLE_${Date.now()}_${index}`;
                    manager.addOrder(orderData, orderId, {
                        success: true,
                        message: '样本订单创建成功'
                    });
                });
                
                result.innerHTML += '<p class="success">✅ 已创建 3 个样本订单</p>';
                result.innerHTML += `<p>当前用户: ${currentUser}</p>`;
                
                // 显示创建的数据
                const userHistory = manager.getHistory();
                result.innerHTML += `<p>用户订单总数: ${userHistory.length}</p>`;
                
            } catch (error) {
                result.innerHTML += '<p class="error">❌ 创建样本数据失败: ' + error.message + '</p>';
            }
        }
        
        function clearAllData() {
            if (confirm('确定要清空所有历史订单数据吗？此操作不可恢复！')) {
                localStorage.removeItem('ota_order_history');
                const result = document.getElementById('fix-result');
                result.innerHTML = '<h3>清空结果:</h3><p class="success">✅ 所有数据已清空</p>';
            }
        }
        
        function testRendering() {
            const result = document.getElementById('render-result');
            result.innerHTML = '<h3>渲染测试结果:</h3>';
            
            try {
                const manager = getOrderHistoryManager();
                const history = manager.getHistory();
                
                result.innerHTML += `<p class="info">获取到 ${history.length} 个订单</p>`;
                
                if (history.length === 0) {
                    result.innerHTML += '<p class="warning">⚠️ 没有订单数据可以渲染</p>';
                    return;
                }
                
                // 模拟渲染第一个订单
                const firstOrder = history[0];
                result.innerHTML += '<h4>第一个订单的渲染数据:</h4>';
                
                const renderData = {
                    orderId: firstOrder.orderId || 'N/A',
                    customerName: firstOrder.orderData?.customerName || 'N/A',
                    subCategoryId: firstOrder.orderData?.subCategoryId || 'N/A',
                    otaReferenceNumber: firstOrder.orderData?.otaReferenceNumber || 'N/A',
                    pickup: firstOrder.orderData?.pickup || 'N/A',
                    destination: firstOrder.orderData?.destination || 'N/A'
                };
                
                result.innerHTML += '<pre>' + JSON.stringify(renderData, null, 2) + '</pre>';
                
                // 检查是否有 N/A 值
                const hasNAValues = Object.values(renderData).some(value => value === 'N/A');
                if (hasNAValues) {
                    result.innerHTML += '<p class="warning">⚠️ 检测到 N/A 值，说明数据结构有问题</p>';
                    result.innerHTML += '<h4>原始订单数据:</h4>';
                    result.innerHTML += '<pre>' + JSON.stringify(firstOrder, null, 2) + '</pre>';
                } else {
                    result.innerHTML += '<p class="success">✅ 渲染数据正常，没有 N/A 值</p>';
                }
                
            } catch (error) {
                result.innerHTML += '<p class="error">❌ 渲染测试失败: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
