/**
 * ============================================================================
 * 🚀 核心业务流程 - API调用器 (子层实现)
 * ============================================================================
 *
 * @fileoverview API调用器 - 子层实现
 * @description 负责GoMyHire API调用的具体实现，从multi-order-manager-v2.js拆分而来
 * 
 * @businessFlow GoMyHire API调用
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理 → 订单管理
 *     ↓
 * B1. 单订单 → 映射到单订单表单 → 【当前文件职责】发送GoMyHire API - 远程处理
 * B2. 多订单 → 映射到多订单表单 → 【当前文件职责】批量发送API - 远程处理
 *     ↓
 * 保存到本地历史订单
 *
 * @architecture Child Layer (子层) - 远程处理实现
 * - 职责：GoMyHire API调用的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供API调用服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/order-management-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责
 * - 🟢 API请求参数构建和验证
 * - 🟢 错误处理和重试机制
 * - 🟢 响应数据解析和格式化
 * - 🟢 批量处理进度管理
 *
 * @remoteProcessing 远程处理职责（核心功能）
 * - 🔴 调用GoMyHire API创建单个订单
 * - 🔴 批量调用GoMyHire API创建多个订单
 * - 🔴 处理API响应和错误
 * - 🔴 管理API调用频率和限制
 *
 * @compatibility 兼容性保证
 * - 保持现有API调用格式
 * - 兼容现有的错误处理机制
 * - 保持响应数据格式一致
 *
 * @refactoringConstraints 重构约束
 * - ✅ 专注于API调用，不处理UI逻辑
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持API调用的稳定性
 * - ✅ 保持现有的重试和错误处理机制
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * API调用器 - 子层实现
     */
    class APICaller {
        constructor() {
            this.logger = this.getLogger();
            
            // API配置
            this.config = {
                baseURL: 'https://api.gomyhire.com', // 示例API地址
                timeout: 30000,
                maxRetries: 3,
                retryDelay: 1000,
                batchSize: 3, // 批量处理时的并发数
                rateLimitDelay: 500 // 请求间隔
            };

            // API状态
            this.state = {
                requestCount: 0,
                successCount: 0,
                errorCount: 0,
                lastRequestTime: 0
            };
            
            this.logger.log('API调用器已初始化', 'info');
        }

        /**
         * 创建单个订单
         * @param {object} order - 订单数据
         * @param {object} options - 调用选项
         * @returns {Promise<object>} API响应结果
         */
        async createSingleOrder(order, options = {}) {
            try {
                this.logger.log('开始创建单个订单', 'info', { 
                    customerName: order.customer_name 
                });

                // 验证订单数据
                const validation = this.validateOrderForAPI(order);
                if (!validation.valid) {
                    throw new Error(`订单数据验证失败: ${validation.errors.join(', ')}`);
                }

                // 构建API请求
                const requestData = this.buildOrderRequest(order);
                
                // 调用API
                const response = await this.makeAPIRequest('/orders', 'POST', requestData, options);
                
                // 更新统计
                this.state.successCount++;
                
                this.logger.log('单个订单创建成功', 'success', { 
                    orderId: response.id || 'unknown' 
                });

                return {
                    success: true,
                    orderId: response.id,
                    response: response,
                    order: order
                };

            } catch (error) {
                this.state.errorCount++;
                this.logger.log('单个订单创建失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 创建多个订单（批量处理）
         * @param {array} orders - 订单数组
         * @param {object} options - 调用选项
         * @returns {Promise<object>} 批量处理结果
         */
        async createMultipleOrders(orders, options = {}) {
            try {
                this.logger.log('开始批量创建订单', 'info', { 
                    orderCount: orders.length 
                });

                if (!Array.isArray(orders) || orders.length === 0) {
                    throw new Error('无效的订单数组');
                }

                const results = [];
                const errors = [];
                
                // 分批处理
                const batches = this.createBatches(orders, this.config.batchSize);
                
                for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                    const batch = batches[batchIndex];
                    
                    this.logger.log(`处理批次 ${batchIndex + 1}/${batches.length}`, 'info', { 
                        batchSize: batch.length 
                    });

                    // 并行处理批次内的订单
                    const batchPromises = batch.map(async (order, orderIndex) => {
                        try {
                            // 添加延迟以避免API限制
                            if (orderIndex > 0) {
                                await this.delay(this.config.rateLimitDelay);
                            }
                            
                            const result = await this.createSingleOrder(order, options);
                            return { success: true, result, order };
                        } catch (error) {
                            return { success: false, error: error.message, order };
                        }
                    });

                    const batchResults = await Promise.allSettled(batchPromises);
                    
                    // 处理批次结果
                    batchResults.forEach((promiseResult, index) => {
                        if (promiseResult.status === 'fulfilled') {
                            const { success, result, error, order } = promiseResult.value;
                            if (success) {
                                results.push(result);
                            } else {
                                errors.push({ order, error });
                            }
                        } else {
                            errors.push({ 
                                order: batch[index], 
                                error: promiseResult.reason?.message || '未知错误' 
                            });
                        }
                    });

                    // 批次间延迟
                    if (batchIndex < batches.length - 1) {
                        await this.delay(this.config.rateLimitDelay * 2);
                    }
                }

                this.logger.log('批量订单创建完成', 'success', { 
                    totalOrders: orders.length,
                    successCount: results.length,
                    errorCount: errors.length 
                });

                return {
                    success: errors.length === 0,
                    totalOrders: orders.length,
                    successCount: results.length,
                    errorCount: errors.length,
                    results: results,
                    errors: errors
                };

            } catch (error) {
                this.logger.log('批量订单创建失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 验证订单数据用于API调用
         * @param {object} order - 订单数据
         * @returns {object} 验证结果
         */
        validateOrderForAPI(order) {
            const validation = {
                valid: true,
                errors: [],
                warnings: []
            };

            // 检查必需字段
            const requiredFields = ['customer_name', 'pickup_location'];
            for (const field of requiredFields) {
                if (!order[field] || order[field] === null) {
                    validation.errors.push(`缺少必需字段: ${field}`);
                    validation.valid = false;
                }
            }

            // 检查数据格式
            if (order.ota_price && (typeof order.ota_price !== 'number' || order.ota_price <= 0)) {
                validation.warnings.push('价格格式不正确');
            }

            if (order.passenger_count && (typeof order.passenger_count !== 'number' || order.passenger_count < 1)) {
                validation.warnings.push('乘客数量不正确');
            }

            return validation;
        }

        /**
         * 构建订单API请求数据
         * @param {object} order - 订单数据
         * @returns {object} API请求数据
         */
        buildOrderRequest(order) {
            return {
                customer_name: order.customer_name,
                customer_contact: order.customer_contact || '',
                pickup_location: order.pickup_location,
                dropoff_location: order.dropoff_location || '',
                pickup_time: order.pickup_time || '',
                ota: order.ota || '',
                ota_reference_number: order.ota_reference_number || '',
                ota_price: order.ota_price || 0,
                car_type_id: order.car_type_id || 1,
                passenger_count: order.passenger_count || 1,
                luggage_count: order.luggage_count || 0,
                languages_id_array: order.languages_id_array || [2, 4],
                sub_category_id: order.sub_category_id || null,
                created_at: new Date().toISOString()
            };
        }

        /**
         * 执行API请求
         * @param {string} endpoint - API端点
         * @param {string} method - HTTP方法
         * @param {object} data - 请求数据
         * @param {object} options - 请求选项
         * @returns {Promise<object>} API响应
         */
        async makeAPIRequest(endpoint, method = 'GET', data = null, options = {}) {
            let lastError;
            
            for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
                try {
                    this.state.requestCount++;
                    this.state.lastRequestTime = Date.now();

                    const requestOptions = {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        signal: AbortSignal.timeout(this.config.timeout)
                    };

                    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                        requestOptions.body = JSON.stringify(data);
                    }

                    const response = await fetch(`${this.config.baseURL}${endpoint}`, requestOptions);

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
                    }

                    const responseData = await response.json();
                    return responseData;

                } catch (error) {
                    lastError = error;
                    this.logger.log(`API请求失败 (尝试 ${attempt}/${this.config.maxRetries})`, 'warning', { 
                        endpoint,
                        method,
                        error: error.message 
                    });

                    if (attempt < this.config.maxRetries) {
                        await this.delay(this.config.retryDelay * attempt);
                    }
                }
            }

            throw new Error(`API请求最终失败: ${lastError.message}`);
        }

        /**
         * 创建批次
         * @param {array} items - 项目数组
         * @param {number} batchSize - 批次大小
         * @returns {array} 批次数组
         */
        createBatches(items, batchSize) {
            const batches = [];
            for (let i = 0; i < items.length; i += batchSize) {
                batches.push(items.slice(i, i + batchSize));
            }
            return batches;
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取API统计信息
         * @returns {object} 统计信息
         */
        getAPIStats() {
            return {
                requestCount: this.state.requestCount,
                successCount: this.state.successCount,
                errorCount: this.state.errorCount,
                successRate: this.state.requestCount > 0 ? 
                    (this.state.successCount / this.state.requestCount * 100).toFixed(2) + '%' : '0%',
                lastRequestTime: this.state.lastRequestTime,
                config: this.config
            };
        }

        /**
         * 重置API统计
         */
        resetAPIStats() {
            this.state.requestCount = 0;
            this.state.successCount = 0;
            this.state.errorCount = 0;
            this.state.lastRequestTime = 0;
            
            this.logger.log('API统计已重置', 'info');
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const apiCaller = new APICaller();

    // 导出到全局作用域
    window.APICaller = APICaller;
    window.OTA.APICaller = APICaller;
    window.OTA.apiCaller = apiCaller;

    console.log('✅ APICaller (子层实现) 已加载');

})();
