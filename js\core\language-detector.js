/**
 * 统一语言检测模块
 * 负责所有语言检测相关的功能，提供统一的API接口
 * 
 * 功能特点：
 * - 简单的二元检测逻辑（英文/中文）
 * - 统一的事件绑定和管理
 * - 可靠的初始化时机控制
 * - 与现有稳定化器集成
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 统一语言检测器类
     */
    class UnifiedLanguageDetector {
        constructor() {
            this.initialized = false;
            this.boundFields = new Set();
            this.eventListeners = new Map();

            // 语言检测配置
            this.config = {
                // 中文字符检测正则表达式
                chineseRegex: /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/,
                // 默认语言ID
                defaultLanguageId: 2, // 英文
                // 中文语言ID
                chineseLanguageId: 4, // 中文
                // 需要监听的字段选择器（不包含orderInput，避免与Gemini自动触发冲突）
                targetFields: [
                    '#customerName',     // 客户姓名
                    '#pickup',          // 上车地点
                    '#dropoff',         // 目的地
                    '#extraRequirement', // 额外要求
                    '#remark',          // 备注
                    '#flightInfo'       // 航班信息
                ],
                // 防抖延迟（毫秒）
                debounceDelay: 300
            };

            // 渠道检测配置
            this.channelDetectionConfig = {
                enabled: false,           // 默认关闭，只对通用用户启用
                availableChannels: [],    // 用户可选渠道列表
                autoAnalysisEnabled: true, // 启用自动分析
                // 渠道检测规则（复用flow/channel-detector.js的逻辑）
                detectionRules: {
                    fliggy: {
                        patterns: [/订单编号[：:\s]*\d{19}/],  // 🚀 修复：移除全局标志，避免lastIndex状态问题
                        confidence: 0.95,
                        channel: 'Fliggy'
                    },
                    jingge: {
                        patterns: [/商铺/],  // 🚀 修复：移除全局标志
                        confidence: 0.85,
                        channel: 'JingGe'
                    },
                    // 参考号模式检测
                    referencePatterns: {
                        'CD': { channel: 'Chong Dealer', confidence: 0.95 },
                        'CT': { channel: 'Ctrip West Malaysia', confidence: 0.9 },
                        'KL': { channel: 'Klook West Malaysia', confidence: 0.9 },
                        'KK': { channel: 'Kkday', confidence: 0.9 }
                    },
                    // 关键词检测
                    keywords: {
                        'chong dealer': { channel: 'Chong Dealer', confidence: 0.9 },
                        '携程': { channel: 'Ctrip West Malaysia', confidence: 0.85 },
                        'ctrip': { channel: 'Ctrip West Malaysia', confidence: 0.85 },
                        'klook': { channel: 'Klook West Malaysia', confidence: 0.85 },
                        'kkday': { channel: 'Kkday', confidence: 0.85 },
                        'traveloka': { channel: 'Traveloka', confidence: 0.8 }
                    }
                },
                // 渠道到策略的映射
                channelStrategyMapping: {
                    'Fliggy': 'fliggy',
                    'JingGe': 'jingge',
                    'Klook West Malaysia': 'klook',
                    'Kkday': 'kkday',
                    'Ctrip West Malaysia': 'ctrip',
                    'Traveloka': 'traveloka',
                    'Chong Dealer': 'chong'
                }
            };

            // 防抖定时器
            this.debounceTimers = new Map();
        }

        /**
         * 安全的日志记录方法
         */
        log(message, level = 'info', data = null) {
            try {
                if (typeof getLogger === 'function') {
                    if (level === 'error') {
                        getLogger().logError(message, data);
                    } else {
                        getLogger().log(message, level, data);
                    }
                } else {
                    // 降级到console
                    const prefix = level === 'error' ? '❌' : level === 'success' ? '✅' : 'ℹ️';
                    console.log(`${prefix} [UnifiedLanguageDetector] ${message}`, data || '');
                }
            } catch (error) {
                console.log(`[UnifiedLanguageDetector] ${message}`, data || '');
            }
        }

        /**
         * 初始化语言检测器
         */
        async init() {
            if (this.initialized) {
                this.log('语言检测器已经初始化', 'info');
                return true;
            }

            try {
                this.log('🌐 初始化统一语言检测器...', 'info');

                // 等待DOM就绪
                await this.waitForDOM();

                // 绑定所有目标字段的事件
                await this.bindAllFieldEvents();

                // 设置默认语言
                await this.setDefaultLanguage();

                this.initialized = true;
                this.log('✅ 统一语言检测器初始化完成', 'success');

                // 注册到全局OTA命名空间
                window.OTA.unifiedLanguageDetector = this;

                return true;
            } catch (error) {
                this.log('语言检测器初始化失败', 'error', error);
                return false;
            }
        }

        /**
         * 等待DOM就绪
         */
        async waitForDOM() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    const handler = () => {
                        document.removeEventListener('DOMContentLoaded', handler);
                        window.removeEventListener('load', handler);
                        resolve();
                    };
                    document.addEventListener('DOMContentLoaded', handler);
                    window.addEventListener('load', handler);
                }
            });
        }

        /**
         * 绑定所有目标字段的事件
         * 减法修复：减少不必要的警告输出，只在调试模式下显示
         */
        async bindAllFieldEvents() {
            let boundCount = 0;

            for (const selector of this.config.targetFields) {
                const element = document.querySelector(selector);
                if (element) {
                    this.bindFieldEvents(element, selector);
                    boundCount++;
                } else {
                    // 减法修复：只在调试模式下输出字段不存在的警告
                    if (window.location?.hostname === 'localhost' || window.OTA?.config?.debugMode) {
                        this.log(`⚠️ 字段不存在，跳过绑定: ${selector}`, 'warning');
                    }
                }
            }

            this.log(`📝 已绑定 ${boundCount} 个字段的语言检测事件`, 'info');
        }

        /**
         * 为单个字段绑定事件
         */
        bindFieldEvents(element, selector) {
            if (this.boundFields.has(selector)) {
                this.log(`字段 ${selector} 已经绑定过事件，跳过`, 'info');
                return;
            }

            // 创建事件处理器
            const inputHandler = (event) => this.handleInput(event, selector);
            const pasteHandler = (event) => this.handlePaste(event, selector);
            const blurHandler = (event) => this.handleBlur(event, selector);

            // 绑定事件
            element.addEventListener('input', inputHandler);
            element.addEventListener('paste', pasteHandler);
            element.addEventListener('blur', blurHandler);

            // 保存事件监听器引用，以便后续清理
            this.eventListeners.set(selector, {
                element,
                handlers: { inputHandler, pasteHandler, blurHandler }
            });

            this.boundFields.add(selector);
            this.log(`✅ 已绑定字段事件: ${selector}`, 'info');
        }

        /**
         * 处理输入事件
         */
        handleInput(event, selector) {
            const text = event.target.value;
            this.detectLanguageWithDebounce(text, selector);
        }

        /**
         * 处理粘贴事件
         */
        handlePaste(event, selector) {
            // 粘贴后稍微延迟处理，确保内容已更新
            setTimeout(() => {
                const text = event.target.value;
                this.detectLanguageWithDebounce(text, selector);
            }, 50);
        }

        /**
         * 处理失焦事件
         */
        handleBlur(event, selector) {
            const text = event.target.value;
            // 失焦时立即检测，不使用防抖
            this.detectAndApply(text, selector);
        }

        /**
         * 带防抖的语言检测
         */
        detectLanguageWithDebounce(text, sourceField) {
            // 清除之前的定时器
            if (this.debounceTimers.has(sourceField)) {
                clearTimeout(this.debounceTimers.get(sourceField));
            }

            // 设置新的定时器
            const timer = setTimeout(() => {
                this.detectAndApply(text, sourceField);
                this.debounceTimers.delete(sourceField);
            }, this.config.debounceDelay);

            this.debounceTimers.set(sourceField, timer);
        }

        /**
         * 检测语言并应用设置
         * @param {string} text - 输入文本
         * @param {string} sourceField - 来源字段
         */
        async detectAndApply(text, sourceField = 'unknown') {
            try {
                if (!text || text.trim().length === 0) {
                    // 空文本时设置默认语言
                    await this.applyLanguageSelection([this.config.defaultLanguageId], sourceField);
                    return true;
                }

                // 检测是否包含中文字符
                const hasChinese = this.config.chineseRegex.test(text);
                const languageIds = hasChinese ? [this.config.chineseLanguageId] : [this.config.defaultLanguageId];
                const languageName = hasChinese ? '中文' : '英文';

                // 应用语言选择
                const success = await this.applyLanguageSelection(languageIds, sourceField);

                if (success) {
                    this.log(
                        `🌐 检测到${languageName}内容，已自动设置${languageName}语言要求`,
                        'info',
                        {
                            sourceField,
                            textLength: text.length,
                            hasChinese,
                            languageIds
                        }
                    );
                }

                // 🚀 性能修复：只做渠道检测，不触发API分析（避免与实时分析冲突）
                if (this.channelDetectionConfig.enabled) {
                    await this.detectAndApplyChannelOnly(text, sourceField);
                }

                return success;
            } catch (error) {
                this.log('语言检测和应用失败', 'error', { error, sourceField, textLength: text?.length });
                return false;
            }
        }

        /**
         * 应用语言选择 - 简化为只使用直接DOM操作
         */
        async applyLanguageSelection(languageIds, sourceField) {
            try {
                // 直接操作DOM - 最可靠的方法
                return this.setLanguageSelectionDirectly(languageIds);

            } catch (error) {
                this.log('应用语言选择失败', 'error', { error, languageIds, sourceField });
                return false;
            }
        }

        /**
         * 直接操作DOM设置语言选择
         */
        setLanguageSelectionDirectly(languageIds) {
            try {
                const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]');
                if (checkboxes.length === 0) {
                    this.log('未找到语言复选框，无法设置语言选择', 'warning');
                    return false;
                }

                // 清除所有选择
                checkboxes.forEach(checkbox => checkbox.checked = false);

                // 设置指定的语言
                languageIds.forEach(id => {
                    const checkbox = document.getElementById(`lang_${id}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                // 触发change事件
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                return true;
            } catch (error) {
                this.log('直接设置语言选择失败', 'error', error);
                return false;
            }
        }

        /**
         * 设置默认语言
         */
        async setDefaultLanguage() {
            try {
                await this.applyLanguageSelection([this.config.defaultLanguageId], 'initialization');
                this.log('✅ 已设置默认语言（英文）', 'info');
            } catch (error) {
                this.log('设置默认语言失败', 'error', error);
            }
        }

        /**
         * 清理所有事件监听器
         */
        cleanup() {
            // 清理防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            // 清理事件监听器
            this.eventListeners.forEach(({ element, handlers }, selector) => {
                element.removeEventListener('input', handlers.inputHandler);
                element.removeEventListener('paste', handlers.pasteHandler);
                element.removeEventListener('blur', handlers.blurHandler);
            });

            this.eventListeners.clear();
            this.boundFields.clear();
            this.initialized = false;

            this.log('🧹 语言检测器已清理', 'info');
        }

        /**
         * 初始化渠道检测功能
         * @param {Array} availableChannels - 用户可选的渠道列表
         */
        initChannelDetection(availableChannels) {
            this.channelDetectionConfig.enabled = true;
            this.channelDetectionConfig.availableChannels = availableChannels || [];
            this.log('渠道智能检测已启用', 'info', {
                channelCount: this.channelDetectionConfig.availableChannels.length
            });
        }

        /**
         * 启用或禁用自动分析
         * @param {boolean} enabled - 是否启用自动分析
         */
        enableAutoAnalysis(enabled = true) {
            this.channelDetectionConfig.autoAnalysisEnabled = enabled;
            this.log(`自动分析已${enabled ? '启用' : '禁用'}`, 'info');
        }

        /**
         * 渠道检测和自动分析联动
         * @param {string} text - 输入文本
         * @param {string} sourceField - 来源字段
         */
        async detectAndApplyChannelWithAnalysis(text, sourceField) {
            try {
                // 1. 渠道检测
                const detectionResult = await this.detectChannelFromText(text);

                if (detectionResult.channel && detectionResult.confidence > 0.8) {
                    const mappedChannel = this.mapDetectedChannelToOption(detectionResult.channel);

                    // 2. 检查权限并设置UI
                    if (this.isChannelAvailable(mappedChannel)) {
                        this.setOTAChannelSelection(mappedChannel);

                        // 3. 自动触发策略切换和分析
                        if (this.channelDetectionConfig.autoAnalysisEnabled) {
                            await this.triggerAutoAnalysis(text, detectionResult, sourceField);
                        }
                    }
                }
            } catch (error) {
                this.log('渠道检测和自动分析失败', 'error', error);
            }
        }

        /**
         * 🚀 架构修复：渠道检测并存储结果，供后续API调用使用
         * @param {string} text - 输入文本
         * @param {string} sourceField - 来源字段
         * @returns {Promise<object>} 渠道检测结果
         */
        async detectAndApplyChannelOnly(text, sourceField) {
            try {
                // 1. 渠道检测（纯本地逻辑）
                const detectionResult = await this.detectChannelFromText(text);

                if (detectionResult.channel && detectionResult.confidence > 0.8) {
                    const mappedChannel = this.mapDetectedChannelToOption(detectionResult.channel);

                    // 2. 检查权限并设置UI
                    if (this.isChannelAvailable(mappedChannel)) {
                        this.setOTAChannelSelection(mappedChannel);

                        // 🚀 架构修复：存储渠道检测结果到AppState
                        this.storeChannelDetectionResult(detectionResult, sourceField);

                        this.log('🚀 渠道检测完成并已存储结果', 'info', {
                            channel: detectionResult.channel,
                            confidence: detectionResult.confidence,
                            sourceField,
                            stored: true
                        });

                        return detectionResult;
                    }
                }

                // 未检测到渠道时也要存储结果
                const noChannelResult = { channel: null, confidence: 0, method: 'no_match' };
                this.storeChannelDetectionResult(noChannelResult, sourceField);
                return noChannelResult;

            } catch (error) {
                this.log('渠道检测失败', 'error', error);
                const errorResult = { channel: null, confidence: 0, method: 'error', error: error.message };
                this.storeChannelDetectionResult(errorResult, sourceField);
                return errorResult;
            }
        }

        /**
         * 存储渠道检测结果到AppState
         * 🚀 架构修复：为后续API调用提供渠道信息，防止覆盖优质结果
         * @param {object} detectionResult - 检测结果
         * @param {string} sourceField - 来源字段
         */
        storeChannelDetectionResult(detectionResult, sourceField) {
            try {
                const appState = this.getAppState();
                if (appState) {
                    // 🚀 防止覆盖更好的检测结果
                    const existingResult = appState.get('lastChannelDetection');

                    // 如果新结果是no_match，但已有更好的结果，则不覆盖
                    if (detectionResult.method === 'no_match' &&
                        existingResult &&
                        existingResult.channel &&
                        existingResult.confidence > 0.8 &&
                        (Date.now() - existingResult.timestamp) < 10000) { // 10秒内的结果

                        this.log('🚀 保护现有的优质渠道检测结果，跳过覆盖', 'info', {
                            existing: existingResult.channel,
                            existingConfidence: existingResult.confidence,
                            newResult: detectionResult.method
                        });
                        return;
                    }

                    const channelInfo = {
                        ...detectionResult,
                        sourceField,
                        timestamp: Date.now(),
                        detectedBy: 'language-detector'
                    };

                    appState.set('lastChannelDetection', channelInfo);

                    this.log('渠道检测结果已存储到AppState', 'info', channelInfo);
                } else {
                    this.log('AppState不可用，无法存储渠道检测结果', 'warning');
                }
            } catch (error) {
                this.log('存储渠道检测结果失败', 'error', error);
            }
        }

        /**
         * 从文本中检测渠道
         * @param {string} text - 输入文本
         * @returns {Promise<object>} 检测结果
         */
        async detectChannelFromText(text) {
            try {
                const rules = this.channelDetectionConfig.detectionRules;

                // 1. Fliggy渠道检测（最高优先级）
                for (const pattern of rules.fliggy.patterns) {
                    if (pattern.test(text)) {
                        return {
                            channel: rules.fliggy.channel,
                            confidence: rules.fliggy.confidence,
                            method: 'fliggy_pattern',
                            matchedPattern: pattern.source
                        };
                    }
                }

                // 2. JingGe渠道检测
                for (const pattern of rules.jingge.patterns) {
                    if (pattern.test(text)) {
                        return {
                            channel: rules.jingge.channel,
                            confidence: rules.jingge.confidence,
                            method: 'jingge_pattern',
                            matchedPattern: pattern.source
                        };
                    }
                }

                // 3. 参考号模式检测
                const referencePattern = /\b([A-Z]{2})[A-Z0-9]{6,12}\b/g;
                const matches = text.match(referencePattern);
                if (matches && matches.length > 0) {
                    for (const match of matches) {
                        const prefix = match.substring(0, 2);
                        const rule = rules.referencePatterns[prefix];
                        if (rule) {
                            return {
                                channel: rule.channel,
                                confidence: rule.confidence,
                                method: 'reference_pattern',
                                matchedReference: match
                            };
                        }
                    }
                }

                // 4. 关键词检测
                const lowerText = text.toLowerCase();
                for (const [keyword, rule] of Object.entries(rules.keywords)) {
                    if (lowerText.includes(keyword.toLowerCase())) {
                        return {
                            channel: rule.channel,
                            confidence: rule.confidence,
                            method: 'keyword_match',
                            matchedKeyword: keyword
                        };
                    }
                }

                // 未检测到任何渠道
                return { channel: null, confidence: 0, method: 'no_match' };

            } catch (error) {
                this.log('渠道检测失败', 'error', error);
                return { channel: null, confidence: 0, method: 'error', error: error.message };
            }
        }

        /**
         * 将检测到的渠道映射到下拉选项值
         * @param {string} detectedChannel - 检测到的渠道名称
         * @returns {string} 映射后的渠道选项值
         */
        mapDetectedChannelToOption(detectedChannel) {
            // 直接映射，大多数情况下检测结果就是选项值
            return detectedChannel;
        }

        /**
         * 检查渠道是否在用户可选范围内
         * @param {string} channelValue - 渠道选项值
         * @returns {boolean} 是否可选
         */
        isChannelAvailable(channelValue) {
            return this.channelDetectionConfig.availableChannels.some(
                channel => channel.value === channelValue
            );
        }

        /**
         * 设置OTA渠道选择
         * 🚀 修复：增强选择器查找和错误处理
         * @param {string} channelValue - 渠道选项值
         */
        setOTAChannelSelection(channelValue) {
            try {
                // 🚀 修复：尝试多种选择器查找OTA渠道下拉框
                let otaChannelSelect = document.getElementById('otaChannel');

                if (!otaChannelSelect) {
                    // 尝试其他可能的选择器
                    otaChannelSelect = document.querySelector('select[name="otaChannel"]') ||
                                     document.querySelector('select[data-field="otaChannel"]') ||
                                     document.querySelector('#ota-channel') ||
                                     document.querySelector('.ota-channel-select');
                }

                if (otaChannelSelect) {
                    // 检查选项是否存在
                    const targetOption = Array.from(otaChannelSelect.options).find(option =>
                        option.value === channelValue || option.textContent.includes(channelValue)
                    );

                    if (targetOption) {
                        otaChannelSelect.value = targetOption.value;

                        // 触发change事件
                        const changeEvent = new Event('change', { bubbles: true });
                        otaChannelSelect.dispatchEvent(changeEvent);

                        this.log(`🎯 自动选择OTA渠道: ${channelValue}`, 'success');
                    } else {
                        this.log(`OTA渠道选项不存在: ${channelValue}`, 'warning', {
                            availableOptions: Array.from(otaChannelSelect.options).map(opt => opt.value)
                        });
                    }
                } else {
                    this.log('OTA渠道下拉框未找到', 'warning', {
                        searchedSelectors: ['#otaChannel', 'select[name="otaChannel"]', 'select[data-field="otaChannel"]', '#ota-channel', '.ota-channel-select']
                    });
                }
            } catch (error) {
                this.log('设置OTA渠道选择失败', 'error', error);
            }
        }

        /**
         * 触发自动分析
         * @param {string} originalText - 原始输入文本
         * @param {object} detectionResult - 渠道检测结果
         * @param {string} sourceField - 来源字段
         */
        async triggerAutoAnalysis(originalText, detectionResult, sourceField) {
            try {
                this.log('🚀 触发自动渠道分析', 'info', {
                    channel: detectionResult.channel,
                    confidence: detectionResult.confidence,
                    sourceField
                });

                // 1. 获取业务流程控制器
                const businessController = this.getBusinessFlowController();
                if (!businessController) {
                    this.log('业务流程控制器不可用', 'warning');
                    return;
                }

                // 2. 构建分析选项
                const analysisOptions = {
                    autoTriggered: true,
                    sourceField,
                    detectedChannel: detectionResult.channel,
                    confidence: detectionResult.confidence,
                    isRealtime: true
                };

                // 3. 调用完整的业务流程
                const result = await businessController.processInput(
                    originalText,
                    'text',
                    analysisOptions
                );

                // 4. 处理分析结果
                await this.handleAutoAnalysisResult(result, detectionResult);

            } catch (error) {
                this.log('自动分析执行失败', 'error', error);
            }
        }

        /**
         * 获取业务流程控制器
         * @returns {object|null} 业务流程控制器实例
         */
        getBusinessFlowController() {
            return window.OTA?.businessFlowController ||
                   window.OTA?.BusinessFlowController ||
                   window.businessFlowController ||
                   null;
        }

        /**
         * 获取AppState实例
         * 🚀 架构修复：用于存储和获取渠道检测结果
         * @returns {object|null} AppState实例
         */
        getAppState() {
            return window.OTA?.appState ||
                   window.appState ||
                   window.getAppState?.() ||
                   null;
        }

        /**
         * 处理自动分析结果
         * @param {object} analysisResult - 分析结果
         * @param {object} detectionResult - 检测结果
         */
        async handleAutoAnalysisResult(analysisResult, detectionResult) {
            try {
                this.log('🎯 自动分析完成', 'success', {
                    channel: detectionResult.channel,
                    resultType: analysisResult.type,
                    hasOrder: !!analysisResult.order
                });

                // 1. 更新UI状态
                this.updateAnalysisStatus('completed', {
                    channel: detectionResult.channel,
                    confidence: detectionResult.confidence
                });

                // 2. 如果有表单管理器，自动填充结果
                const formManager = this.getFormManager();
                if (formManager && analysisResult.order) {
                    await this.autoFillAnalysisResult(formManager, analysisResult.order);
                }

                // 3. 触发自定义事件
                this.dispatchAutoAnalysisEvent(analysisResult, detectionResult);

            } catch (error) {
                this.log('自动分析结果处理失败', 'error', error);
            }
        }

        /**
         * 更新分析状态显示
         * @param {string} status - 状态
         * @param {object} data - 状态数据
         */
        updateAnalysisStatus(status, data = {}) {
            try {
                // 可以在这里更新UI状态指示器
                this.log(`分析状态更新: ${status}`, 'info', data);
            } catch (error) {
                this.log('更新分析状态失败', 'error', error);
            }
        }

        /**
         * 自动填充分析结果到表单
         * @param {object} formManager - 表单管理器
         * @param {object} orderData - 订单数据
         */
        async autoFillAnalysisResult(formManager, orderData) {
            try {
                // 自动填充表单字段
                if (orderData.customerName && formManager.setFieldValue) {
                    formManager.setFieldValue('customerName', orderData.customerName);
                }
                if (orderData.pickup && formManager.setFieldValue) {
                    formManager.setFieldValue('pickup', orderData.pickup);
                }
                if (orderData.dropoff && formManager.setFieldValue) {
                    formManager.setFieldValue('dropoff', orderData.dropoff);
                }
                if (orderData.contactPhone && formManager.setFieldValue) {
                    formManager.setFieldValue('contactPhone', orderData.contactPhone);
                }
                if (orderData.extraRequirement && formManager.setFieldValue) {
                    formManager.setFieldValue('extraRequirement', orderData.extraRequirement);
                }

                this.log('自动填充表单完成', 'success');
            } catch (error) {
                this.log('自动填充表单失败', 'error', error);
            }
        }

        /**
         * 获取表单管理器
         * @returns {object|null} 表单管理器实例
         */
        getFormManager() {
            return window.OTA?.formManager ||
                   window.formManager ||
                   null;
        }

        /**
         * 触发自定义事件
         * @param {object} analysisResult - 分析结果
         * @param {object} detectionResult - 检测结果
         */
        dispatchAutoAnalysisEvent(analysisResult, detectionResult) {
            try {
                const event = new CustomEvent('ota-auto-analysis-completed', {
                    detail: {
                        analysisResult,
                        detectionResult,
                        timestamp: Date.now()
                    }
                });
                document.dispatchEvent(event);
                this.log('自动分析完成事件已触发', 'info');
            } catch (error) {
                this.log('触发自定义事件失败', 'error', error);
            }
        }

        /**
         * 重新初始化
         */
        async reinit() {
            this.cleanup();
            return await this.init();
        }
    }

    // 创建全局实例
    window.OTA.UnifiedLanguageDetector = UnifiedLanguageDetector;

    // 自动初始化（在DOM就绪后）- 🔧 修复：添加重试机制和状态检查
    async function initializeUnifiedLanguageDetector() {
        try {
            console.log('🌐 开始初始化统一语言检测器...');
            const detector = new UnifiedLanguageDetector();
            const success = await detector.init();

            if (success) {
                console.log('✅ 统一语言检测器初始化成功');
            } else {
                console.warn('⚠️ 统一语言检测器初始化失败，将重试');
                // 重试一次
                setTimeout(async () => {
                    const retryDetector = new UnifiedLanguageDetector();
                    await retryDetector.init();
                }, 2000);
            }
        } catch (error) {
            console.error('❌ 统一语言检测器初始化错误:', error);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeUnifiedLanguageDetector);
    } else {
        // DOM已经就绪，立即初始化
        setTimeout(initializeUnifiedLanguageDetector, 100);
    }

})();
