/**
 * ============================================================================
 * 🔧🎬 字段标准化与动画系统集成测试
 * ============================================================================
 *
 * @fileoverview 综合测试字段标准化修复和动画系统的协调工作
 * @description 验证API标准字段映射和动画效果的完整集成
 * 
 * @features 测试功能
 * - API标准字段列表同步验证
 * - 字段标准化映射完整性检查
 * - 动画系统与字段映射协调测试
 * - 实际订单数据的端到端测试
 * 
 * @usage 使用方法
 * 在浏览器控制台中运行：testFieldAnimationIntegration()
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    /**
     * 字段标准化与动画系统集成测试套件
     */
    class FieldAnimationIntegrationTest {
        constructor() {
            this.testResults = [];
            
            // GoMyHire API标准字段列表（基于用户提供的最新文档）
            this.API_STANDARD_FIELDS = [
                'sub_category_id', 'ota', 'ota_reference_number', 'ota_price',
                'customer_name', 'customer_contact', 'customer_email', 'flight_info',
                'pickup', 'pickup_lat', 'pickup_long', 'date', 'time',
                'destination', 'destination_lat', 'destination_long', 'car_type_id',
                'passenger_number', 'luggage_number', 'driver_fee', 'driver_collect',
                'tour_guide', 'baby_chair', 'meet_and_greet', 'extra_requirement',
                'incharge_by_backend_user_id', 'driving_region_id', 'languages_id_array'
            ];
            
            // 关键字段映射测试数据
            this.CRITICAL_FIELD_MAPPINGS = {
                'pickup': '新加坡升达酒店-武吉士',
                'destination': '樟宜机场T3',
                'date': '2025-08-13',
                'time': '09:00',
                'passenger_number': 3,
                'customer_name': '叶伶睿',
                'customer_contact': '13857841893',
                'ota_reference_number': '4670922206817960132'
            };
        }

        /**
         * 运行完整的集成测试
         */
        async runIntegrationTest() {
            console.log('🔧🎬 开始字段标准化与动画系统集成测试...');
            console.log('='.repeat(60));

            try {
                // 1. 字段标准化验证
                await this.testFieldStandardizationSync();
                await this.testFieldMappingCompleteness();
                
                // 2. 动画系统验证
                await this.testAnimationSystemIntegration();
                
                // 3. 端到端集成测试
                await this.testEndToEndIntegration();
                
                // 4. 实际订单数据测试
                await this.testRealOrderDataProcessing();
                
                // 输出测试结果
                this.outputIntegrationResults();
                
            } catch (error) {
                console.error('❌ 集成测试失败:', error);
                this.addTestResult('集成测试执行', false, `测试执行失败: ${error.message}`);
            }
        }

        /**
         * 测试字段标准化同步
         */
        async testFieldStandardizationSync() {
            console.log('🔧 测试字段标准化同步...');
            
            try {
                const fieldStandardization = window.OTA?.GlobalFieldStandardizationLayer;
                if (!fieldStandardization) {
                    this.addTestResult('字段标准化同步', false, '字段标准化层不可用');
                    return;
                }
                
                // 检查API标准字段定义
                const systemApiFields = Object.keys(fieldStandardization.API_STANDARD_FIELDS || {});
                const missingFields = this.API_STANDARD_FIELDS.filter(field => 
                    !systemApiFields.includes(field)
                );
                
                const extraFields = systemApiFields.filter(field => 
                    !this.API_STANDARD_FIELDS.includes(field) && 
                    !['currency', 'raw_text'].includes(field) // 允许系统内部字段
                );
                
                if (missingFields.length === 0 && extraFields.length === 0) {
                    this.addTestResult('字段标准化同步', true, 
                        `API标准字段完全同步 (${systemApiFields.length} 个字段)`);
                } else {
                    this.addTestResult('字段标准化同步', false, 
                        `字段不同步 - 缺失: ${missingFields.join(', ')} | 多余: ${extraFields.join(', ')}`);
                }
                
            } catch (error) {
                this.addTestResult('字段标准化同步', false, error.message);
            }
        }

        /**
         * 测试字段映射完整性
         */
        async testFieldMappingCompleteness() {
            console.log('🗺️ 测试字段映射完整性...');
            
            try {
                const fieldStandardization = window.OTA?.GlobalFieldStandardizationLayer;
                if (!fieldStandardization) {
                    this.addTestResult('字段映射完整性', false, '字段标准化层不可用');
                    return;
                }
                
                // 测试关键字段映射
                let mappingSuccessCount = 0;
                const mappingTests = [];
                
                Object.entries(this.CRITICAL_FIELD_MAPPINGS).forEach(([apiField, testValue]) => {
                    // 测试API字段到前端字段的映射
                    const frontendField = fieldStandardization.API_TO_FRONTEND_MAPPING?.[apiField];
                    if (frontendField) {
                        mappingSuccessCount++;
                        mappingTests.push(`${apiField} → ${frontendField} ✅`);
                    } else {
                        mappingTests.push(`${apiField} → 未映射 ❌`);
                    }
                });
                
                const successRate = Math.round((mappingSuccessCount / Object.keys(this.CRITICAL_FIELD_MAPPINGS).length) * 100);
                
                if (successRate >= 90) {
                    this.addTestResult('字段映射完整性', true, 
                        `关键字段映射成功率: ${successRate}% (${mappingSuccessCount}/${Object.keys(this.CRITICAL_FIELD_MAPPINGS).length})`);
                } else {
                    this.addTestResult('字段映射完整性', false, 
                        `关键字段映射成功率过低: ${successRate}%`);
                }
                
                console.log('字段映射详情:', mappingTests);
                
            } catch (error) {
                this.addTestResult('字段映射完整性', false, error.message);
            }
        }

        /**
         * 测试动画系统集成
         */
        async testAnimationSystemIntegration() {
            console.log('🎬 测试动画系统集成...');
            
            try {
                const animationManager = window.OTA?.animationManager || window.animationManager;
                if (!animationManager) {
                    this.addTestResult('动画系统集成', false, '动画管理器不可用');
                    return;
                }
                
                // 检查动画管理器是否已集成到关键管理器中
                const integrationChecks = [
                    { name: '实时分析管理器', manager: window.OTA?.managers?.RealtimeAnalysisManager },
                    { name: '表单管理器', manager: window.OTA?.uiManager?.getManager('form') },
                    { name: 'UI状态管理器', manager: window.OTA?.uiManager?.getManager('state') }
                ];
                
                let integratedCount = 0;
                integrationChecks.forEach(check => {
                    if (check.manager && check.manager.animationManager) {
                        integratedCount++;
                        console.log(`✅ ${check.name}已集成动画支持`);
                    } else {
                        console.log(`❌ ${check.name}未集成动画支持`);
                    }
                });
                
                if (integratedCount === integrationChecks.length) {
                    this.addTestResult('动画系统集成', true, '所有关键管理器已集成动画支持');
                } else {
                    this.addTestResult('动画系统集成', false, 
                        `${integrationChecks.length - integratedCount} 个管理器未集成动画支持`);
                }
                
            } catch (error) {
                this.addTestResult('动画系统集成', false, error.message);
            }
        }

        /**
         * 测试端到端集成
         */
        async testEndToEndIntegration() {
            console.log('🔄 测试端到端集成...');
            
            try {
                // 创建测试表单字段
                const testFields = {};
                Object.keys(this.CRITICAL_FIELD_MAPPINGS).forEach(apiField => {
                    const fieldStandardization = window.OTA?.GlobalFieldStandardizationLayer;
                    const frontendField = fieldStandardization?.API_TO_FRONTEND_MAPPING?.[apiField];
                    if (frontendField) {
                        const element = document.getElementById(this.getFrontendFieldId(frontendField));
                        if (element) {
                            testFields[apiField] = { element, frontendField };
                        }
                    }
                });
                
                console.log(`找到 ${Object.keys(testFields).length} 个可测试的表单字段`);
                
                // 测试字段填充和动画
                const animationManager = window.OTA?.animationManager || window.animationManager;
                let animationTestCount = 0;
                
                for (const [apiField, { element }] of Object.entries(testFields)) {
                    const testValue = this.CRITICAL_FIELD_MAPPINGS[apiField];
                    
                    if (animationManager && animationManager.isAnimationEnabled()) {
                        try {
                            await animationManager.animateFieldFill(element, testValue, { fieldName: apiField });
                            animationTestCount++;
                        } catch (error) {
                            console.warn(`字段 ${apiField} 动画测试失败:`, error);
                        }
                    } else {
                        // 降级测试：直接设置值
                        element.value = testValue;
                        animationTestCount++;
                    }
                }
                
                if (animationTestCount > 0) {
                    this.addTestResult('端到端集成', true, 
                        `成功测试 ${animationTestCount} 个字段的填充和动画`);
                } else {
                    this.addTestResult('端到端集成', false, '没有字段通过端到端测试');
                }
                
            } catch (error) {
                this.addTestResult('端到端集成', false, error.message);
            }
        }

        /**
         * 测试实际订单数据处理
         */
        async testRealOrderDataProcessing() {
            console.log('📋 测试实际订单数据处理...');
            
            const testOrderText = `订单编号：4670922206817960132
买家：叶伶睿
支付时间：2025-08-07 09:22:31

豪华7座

【送机】

新加坡-新加坡

[出发]新加坡升达酒店-武吉士
[抵达]樟宜机场T3

约23.7公里

2025-08-13 09:00:00

叶伶睿
真实号：13857841893

---
3成人0儿童`;

            try {
                const geminiService = window.getGeminiService?.();
                if (!geminiService) {
                    this.addTestResult('实际订单数据处理', false, 'Gemini服务不可用');
                    return;
                }
                
                // 解析订单数据
                const parseResult = await geminiService.parseOrder(testOrderText, true);
                
                if (parseResult && Array.isArray(parseResult) && parseResult.length > 0) {
                    const order = parseResult[0];
                    
                    // 检查关键字段是否正确解析
                    let parsedFieldCount = 0;
                    const fieldChecks = [];
                    
                    Object.keys(this.CRITICAL_FIELD_MAPPINGS).forEach(field => {
                        const value = order[field];
                        if (value !== null && value !== undefined && value !== '') {
                            parsedFieldCount++;
                            fieldChecks.push(`${field}: ${value} ✅`);
                        } else {
                            fieldChecks.push(`${field}: null/empty ❌`);
                        }
                    });
                    
                    console.log('字段解析详情:', fieldChecks);
                    
                    const parseSuccessRate = Math.round((parsedFieldCount / Object.keys(this.CRITICAL_FIELD_MAPPINGS).length) * 100);
                    
                    if (parseSuccessRate >= 70) {
                        this.addTestResult('实际订单数据处理', true, 
                            `订单解析成功率: ${parseSuccessRate}% (${parsedFieldCount}/${Object.keys(this.CRITICAL_FIELD_MAPPINGS).length})`);
                    } else {
                        this.addTestResult('实际订单数据处理', false, 
                            `订单解析成功率过低: ${parseSuccessRate}%`);
                    }
                } else {
                    this.addTestResult('实际订单数据处理', false, '订单解析失败或返回空结果');
                }
                
            } catch (error) {
                this.addTestResult('实际订单数据处理', false, error.message);
            }
        }

        /**
         * 获取前端字段对应的DOM元素ID
         */
        getFrontendFieldId(frontendField) {
            const fieldIdMapping = {
                'customerName': 'customerName',
                'customerContact': 'customerContact',
                'pickupLocation': 'pickup',
                'dropoffLocation': 'dropoff',
                'pickupDate': 'pickupDate',
                'pickupTime': 'pickupTime',
                'passengerCount': 'passengerCount',
                'otaReferenceNumber': 'otaReferenceNumber'
            };
            
            return fieldIdMapping[frontendField] || frontendField;
        }

        /**
         * 添加测试结果
         */
        addTestResult(testName, passed, message) {
            this.testResults.push({
                name: testName,
                passed,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 输出集成测试结果
         */
        outputIntegrationResults() {
            console.log('\n' + '='.repeat(60));
            console.log('🔧🎬 字段标准化与动画系统集成测试结果');
            console.log('='.repeat(60));
            
            const passedTests = this.testResults.filter(result => result.passed);
            const failedTests = this.testResults.filter(result => !result.passed);
            
            console.log(`✅ 通过: ${passedTests.length} 项`);
            console.log(`❌ 失败: ${failedTests.length} 项`);
            console.log(`📊 总计: ${this.testResults.length} 项`);
            
            if (failedTests.length > 0) {
                console.log('\n❌ 失败的测试:');
                failedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            if (passedTests.length > 0) {
                console.log('\n✅ 通过的测试:');
                passedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            const successRate = Math.round((passedTests.length / this.testResults.length) * 100);
            console.log(`\n🎯 集成成功率: ${successRate}%`);
            
            if (successRate >= 85) {
                console.log('🎉 字段标准化与动画系统集成测试通过！');
                console.log('✨ 系统已准备好处理实际订单数据');
            } else if (successRate >= 70) {
                console.log('⚠️ 集成测试基本通过，但需要进一步优化');
            } else {
                console.log('❌ 集成测试失败，需要修复关键问题');
            }
        }
    }

    // 创建全局测试函数
    window.testFieldAnimationIntegration = async function() {
        const tester = new FieldAnimationIntegrationTest();
        await tester.runIntegrationTest();
        return tester.testResults;
    };

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.FieldAnimationIntegrationTest = FieldAnimationIntegrationTest;

    console.log('🔧🎬 字段标准化与动画系统集成测试脚本已加载');
    console.log('💡 使用 testFieldAnimationIntegration() 运行完整集成测试');

})();
