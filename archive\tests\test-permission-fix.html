<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限控制修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .user-info { background: #f0f8ff; padding: 10px; margin: 10px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #28a745; background: #f8fff8; }
        .test-error { border-left-color: #dc3545; background: #fff8f8; }
        .hidden-element { display: none !important; }
        button { margin: 5px; padding: 8px 15px; }
    </style>
</head>
<body>
    <h1>🔐 权限控制修复测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>此页面用于测试价格字段和paging选项的权限控制修复效果。</p>
        <p><strong>受限用户列表:</strong></p>
        <ul>
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>模拟用户状态</h2>
        <div class="user-info" id="userInfo">
            当前用户: 未登录
        </div>
        <button onclick="simulateRestrictedUser()">模拟受限用户登录</button>
        <button onclick="simulateNormalUser()">模拟普通用户登录</button>
        <button onclick="simulateLogout()">模拟登出</button>
    </div>

    <div class="test-section">
        <h2>价格字段测试</h2>
        <!-- 模拟完整的价格信息面板结构 -->
        <section class="panel compact-card" data-panel="price-info" style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
            <div class="section-header">
                <h3>💰 价格信息</h3>
            </div>
            <div class="panel-content">
                <div class="form-group" id="otaPriceGroup">
                    <label>OTA价格:</label>
                    <input type="number" id="otaPrice" placeholder="OTA价格">
                </div>
                <div class="form-group" id="driverFeeGroup">
                    <label>司机费用:</label>
                    <input type="number" id="driverFee" placeholder="司机费用">
                </div>
            </div>
        </section>
    </div>

    <div class="test-section">
        <h2>语言选项测试</h2>
        <div class="checkbox-item">
            <input type="checkbox" id="lang_5" name="languagesIdArray" value="5" style="display: none;">
            <label for="lang_5" style="display: none;">Paging (PG)</label>
        </div>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
        <button onclick="runPermissionTest()">执行权限测试</button>
    </div>

    <script>
        // 模拟应用状态
        let mockAppState = {
            auth: {
                user: null
            }
        };

        // 模拟getAppState函数
        window.getAppState = function() {
            return {
                get: function(path) {
                    if (path === 'auth.user') {
                        return mockAppState.auth.user;
                    }
                    return null;
                }
            };
        };

        // 模拟logger
        window.getLogger = function() {
            return {
                log: function(message, level, data) {
                    console.log(`[${level}] ${message}`, data);
                },
                logError: function(message, error) {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        };

        // 模拟权限检查函数
        function checkPriceFieldPermissions() {
            const currentUser = mockAppState.auth.user;
            let permissions = {
                canViewOtaPrice: true,
                canViewDriverFee: true
            };

            if (!currentUser || !currentUser.email) {
                return permissions;
            }

            const email = currentUser.email.toLowerCase();
            const restrictedUsers = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];

            if (restrictedUsers.includes(email)) {
                permissions.canViewOtaPrice = false;
                permissions.canViewDriverFee = false;
            }

            return permissions;
        }

        function checkLanguagePermissions() {
            const currentUser = mockAppState.auth.user;
            let permissions = {
                canUsePaging: true
            };

            if (!currentUser || !currentUser.email) {
                return permissions;
            }

            const email = currentUser.email.toLowerCase();
            const restrictedUsers = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];

            if (restrictedUsers.includes(email)) {
                permissions.canUsePaging = false;
            }

            return permissions;
        }

        // 应用权限控制
        function applyPermissions() {
            // 应用价格字段权限
            const pricePermissions = checkPriceFieldPermissions();
            const priceInfoPanel = document.querySelector('[data-panel="price-info"]');
            const otaPriceGroup = document.getElementById('otaPriceGroup');
            const driverFeeGroup = document.getElementById('driverFeeGroup');

            // 判断用户是否有任何价格权限
            const hasAnyPricePermission = pricePermissions.canViewOtaPrice || pricePermissions.canViewDriverFee;

            // 控制整个价格信息面板
            if (priceInfoPanel) {
                priceInfoPanel.style.display = hasAnyPricePermission ? 'block' : 'none';
            }

            // 控制具体字段（仅在面板显示时有意义）
            if (hasAnyPricePermission) {
                if (otaPriceGroup) {
                    otaPriceGroup.style.display = pricePermissions.canViewOtaPrice ? 'block' : 'none';
                }
                if (driverFeeGroup) {
                    driverFeeGroup.style.display = pricePermissions.canViewDriverFee ? 'block' : 'none';
                }
            }

            // 应用语言选项权限
            const languagePermissions = checkLanguagePermissions();
            const pagingCheckbox = document.getElementById('lang_5');
            const pagingLabel = document.querySelector('label[for="lang_5"]');

            if (pagingCheckbox && pagingLabel) {
                if (languagePermissions.canUsePaging) {
                    pagingCheckbox.style.display = '';
                    pagingLabel.style.display = '';
                } else {
                    pagingCheckbox.style.display = 'none';
                    pagingLabel.style.display = 'none';
                    pagingCheckbox.checked = false;
                }
            }
        }

        // 用户状态模拟函数
        function simulateRestrictedUser() {
            mockAppState.auth.user = {
                email: '<EMAIL>',
                name: 'demo'
            };
            updateUserInfo();
            applyPermissions();
        }

        function simulateNormalUser() {
            mockAppState.auth.user = {
                email: '<EMAIL>',
                name: 'admin'
            };
            updateUserInfo();
            applyPermissions();
        }

        function simulateLogout() {
            mockAppState.auth.user = null;
            updateUserInfo();
            applyPermissions();
        }

        function updateUserInfo() {
            const userInfo = document.getElementById('userInfo');
            if (mockAppState.auth.user) {
                userInfo.textContent = `当前用户: ${mockAppState.auth.user.email}`;
            } else {
                userInfo.textContent = '当前用户: 未登录';
            }
        }

        function runPermissionTest() {
            const results = document.getElementById('testResults');
            let html = '<h3>测试结果:</h3>';

            // 测试价格权限
            const priceInfoPanel = document.querySelector('[data-panel="price-info"]');
            const otaPriceGroup = document.getElementById('otaPriceGroup');
            const driverFeeGroup = document.getElementById('driverFeeGroup');
            const panelVisible = priceInfoPanel.style.display !== 'none';
            const priceVisible = otaPriceGroup.style.display !== 'none';
            
            // 测试paging选项权限
            const pagingCheckbox = document.getElementById('lang_5');
            const pagingVisible = pagingCheckbox.style.display !== 'none';

            const currentUser = mockAppState.auth.user;
            const isRestrictedUser = currentUser && [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ].includes(currentUser.email.toLowerCase());

            if (isRestrictedUser) {
                html += `<div class="test-result ${panelVisible ? 'test-error' : ''}">
                    💰 价格信息面板: ${panelVisible ? '❌ 错误 - 应该隐藏但显示了' : '✅ 正确 - 已隐藏'}
                </div>`;
                html += `<div class="test-result ${priceVisible ? 'test-error' : ''}">
                    ✅ 价格字段权限: ${priceVisible ? '❌ 错误 - 应该隐藏但显示了' : '✅ 正确 - 已隐藏'}
                </div>`;
                html += `<div class="test-result ${pagingVisible ? 'test-error' : ''}">
                    🏷️ Paging选项权限: ${pagingVisible ? '❌ 错误 - 应该隐藏但显示了' : '✅ 正确 - 已隐藏'}
                </div>`;
            } else if (currentUser) {
                html += `<div class="test-result ${!panelVisible ? 'test-error' : ''}">
                    💰 价格信息面板: ${!panelVisible ? '❌ 错误 - 应该显示但隐藏了' : '✅ 正确 - 已显示'}
                </div>`;
                html += `<div class="test-result ${!priceVisible ? 'test-error' : ''}">
                    ✅ 价格字段权限: ${!priceVisible ? '❌ 错误 - 应该显示但隐藏了' : '✅ 正确 - 已显示'}
                </div>`;
                html += `<div class="test-result ${!pagingVisible ? 'test-error' : ''}">
                    🏷️ Paging选项权限: ${!pagingVisible ? '❌ 错误 - 应该显示但隐藏了' : '✅ 正确 - 已显示'}
                </div>`;
            } else {
                html += '<div class="test-result">ℹ️ 用户未登录，使用默认权限设置</div>';
            }

            results.innerHTML = html;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            applyPermissions();
            runPermissionTest();
        });
    </script>
</body>
</html>