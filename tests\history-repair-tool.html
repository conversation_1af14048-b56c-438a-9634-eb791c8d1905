<!DOCTYPE html>
<html>
<head>
    <title>历史订单问题修复和测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .success { color: #28a745; background: #d4edda; border-color: #c3e6cb; }
        .error { color: #dc3545; background: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; border-radius: 4px; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        button:hover { background: #0056b3; }
        button.success { background: #28a745; }
        button.success:hover { background: #218838; }
        button.warning { background: #ffc107; color: #212529; }
        button.warning:hover { background: #e0a800; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .status-card { padding: 15px; border-radius: 8px; border: 1px solid #ddd; }
        .status-ok { background: #d4edda; border-color: #c3e6cb; }
        .status-warning { background: #fff3cd; border-color: #ffeaa7; }
        .status-error { background: #f8d7da; border-color: #f5c6cb; }
        .log-output { background: #272822; color: #f8f8f2; padding: 15px; border-radius: 5px; font-family: monospace; height: 300px; overflow-y: auto; margin-top: 10px; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 历史订单问题修复和测试工具</h1>
        <p><strong>目标：</strong>解决历史订单模块的显示和持久化问题</p>
        
        <div class="section">
            <h2>🚀 快速操作</h2>
            <button onclick="runFullDiagnosis()">🔍 完整诊断</button>
            <button onclick="runFullRepair()" class="success">🔧 执行修复</button>
            <button onclick="testHistoryModule()">🧪 功能测试</button>
            <button onclick="createSampleData()" class="warning">📝 创建示例数据</button>
            <button onclick="clearAllData()" class="danger">🗑️ 清除所有数据</button>
            <button onclick="exportLogs()">📋 导出日志</button>
        </div>

        <div class="status-grid">
            <div id="statusLocalStorage" class="status-card">
                <h3>💾 数据存储</h3>
                <div id="storageStatus">检查中...</div>
            </div>
            <div id="statusDOM" class="status-card">
                <h3>🏗️ DOM结构</h3>
                <div id="domStatus">检查中...</div>
            </div>
            <div id="statusModule" class="status-card">
                <h3>⚙️ JavaScript模块</h3>
                <div id="moduleStatus">检查中...</div>
            </div>
            <div id="statusDisplay" class="status-card">
                <h3>🎨 显示功能</h3>
                <div id="displayStatus">检查中...</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 详细诊断结果</h2>
            <div id="diagnosticResults">等待诊断...</div>
        </div>

        <div class="section">
            <h2>🔧 修复操作日志</h2>
            <div class="log-output" id="logOutput">等待操作...</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/order-history-manager.js"></script>

    <script>
        let logMessages = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logMessages.push(logEntry);
            
            const logOutput = document.getElementById('logOutput');
            if (logOutput) {
                logOutput.innerHTML = logMessages.slice(-50).join('\n'); // 保持最后50条
                logOutput.scrollTop = logOutput.scrollHeight;
            }
            
            console.log(logEntry);
        }

        // 1. 数据存储检查
        function checkLocalStorage() {
            try {
                const historyData = localStorage.getItem('ota_order_history');
                const statusElement = document.getElementById('storageStatus');
                const cardElement = document.getElementById('statusLocalStorage');
                
                if (!historyData) {
                    statusElement.innerHTML = '❌ 未找到历史数据';
                    cardElement.className = 'status-card status-error';
                    return { status: 'error', message: '未找到历史数据' };
                }
                
                const parsed = JSON.parse(historyData);
                let totalOrders = 0;
                let userCount = 0;
                
                if (Array.isArray(parsed)) {
                    totalOrders = parsed.length;
                    userCount = 1;
                    statusElement.innerHTML = `⚠️ 旧格式数据<br>订单数: ${totalOrders}`;
                    cardElement.className = 'status-card status-warning';
                    return { status: 'warning', message: '使用旧格式数据', orders: totalOrders };
                } else if (typeof parsed === 'object') {
                    userCount = Object.keys(parsed).length;
                    Object.values(parsed).forEach(userOrders => {
                        if (Array.isArray(userOrders)) {
                            totalOrders += userOrders.length;
                        }
                    });
                    statusElement.innerHTML = `✅ 数据正常<br>用户: ${userCount}, 订单: ${totalOrders}`;
                    cardElement.className = 'status-card status-ok';
                    return { status: 'ok', message: '数据格式正确', users: userCount, orders: totalOrders };
                }
                
            } catch (error) {
                const statusElement = document.getElementById('storageStatus');
                const cardElement = document.getElementById('statusLocalStorage');
                statusElement.innerHTML = `❌ 数据损坏<br>${error.message}`;
                cardElement.className = 'status-card status-error';
                return { status: 'error', message: error.message };
            }
        }

        // 2. DOM结构检查
        function checkDOM() {
            const requiredElements = [
                'historyPanel', 'historyListContainer', 'historyBtn',
                'closeHistoryBtn', 'clearHistoryBtn', 'exportHistoryBtn'
            ];
            
            let foundCount = 0;
            const missing = [];
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    foundCount++;
                } else {
                    missing.push(id);
                }
            });
            
            const statusElement = document.getElementById('domStatus');
            const cardElement = document.getElementById('statusDOM');
            const successRate = Math.round((foundCount / requiredElements.length) * 100);
            
            if (successRate === 100) {
                statusElement.innerHTML = '✅ DOM完整<br>所有元素存在';
                cardElement.className = 'status-card status-ok';
                return { status: 'ok', message: 'DOM结构完整', found: foundCount, total: requiredElements.length };
            } else {
                statusElement.innerHTML = `❌ DOM不完整<br>缺失: ${missing.join(', ')}`;
                cardElement.className = 'status-card status-error';
                return { status: 'error', message: 'DOM结构不完整', missing, found: foundCount, total: requiredElements.length };
            }
        }

        // 3. JavaScript模块检查
        function checkModules() {
            const checks = [
                { name: 'getOrderHistoryManager', fn: () => typeof getOrderHistoryManager === 'function' },
                { name: 'OrderHistoryManager', fn: () => typeof OrderHistoryManager === 'function' },
                { name: 'getLogger', fn: () => typeof getLogger === 'function' },
                { name: 'getAppState', fn: () => typeof getAppState === 'function' }
            ];
            
            let passedCount = 0;
            const failed = [];
            
            checks.forEach(check => {
                try {
                    if (check.fn()) {
                        passedCount++;
                    } else {
                        failed.push(check.name);
                    }
                } catch (error) {
                    failed.push(check.name);
                }
            });
            
            const statusElement = document.getElementById('moduleStatus');
            const cardElement = document.getElementById('statusModule');
            const successRate = Math.round((passedCount / checks.length) * 100);
            
            if (successRate === 100) {
                statusElement.innerHTML = '✅ 模块完整<br>所有函数可用';
                cardElement.className = 'status-card status-ok';
                return { status: 'ok', message: '所有模块可用', passed: passedCount, total: checks.length };
            } else {
                statusElement.innerHTML = `❌ 模块缺失<br>失败: ${failed.join(', ')}`;
                cardElement.className = 'status-card status-error';
                return { status: 'error', message: '模块缺失', failed, passed: passedCount, total: checks.length };
            }
        }

        // 4. 显示功能检查
        function checkDisplay() {
            try {
                if (typeof getOrderHistoryManager !== 'function') {
                    throw new Error('getOrderHistoryManager 函数不存在');
                }
                
                const manager = getOrderHistoryManager();
                const history = manager.getHistory();
                const canRender = typeof manager.renderHistory === 'function';
                const canShow = typeof manager.showHistoryPanel === 'function';
                
                const statusElement = document.getElementById('displayStatus');
                const cardElement = document.getElementById('statusDisplay');
                
                if (canRender && canShow) {
                    statusElement.innerHTML = `✅ 功能正常<br>历史记录: ${history.length}条`;
                    cardElement.className = 'status-card status-ok';
                    return { status: 'ok', message: '显示功能正常', historyCount: history.length };
                } else {
                    statusElement.innerHTML = '❌ 功能缺失<br>方法不完整';
                    cardElement.className = 'status-card status-error';
                    return { status: 'error', message: '显示功能缺失' };
                }
                
            } catch (error) {
                const statusElement = document.getElementById('displayStatus');
                const cardElement = document.getElementById('statusDisplay');
                statusElement.innerHTML = `❌ 功能异常<br>${error.message}`;
                cardElement.className = 'status-card status-error';
                return { status: 'error', message: error.message };
            }
        }

        // 运行完整诊断
        function runFullDiagnosis() {
            log('开始运行完整诊断');
            
            const results = {
                storage: checkLocalStorage(),
                dom: checkDOM(),
                modules: checkModules(),
                display: checkDisplay()
            };
            
            // 生成详细诊断报告
            let html = '<h3>诊断结果详情</h3>';
            
            Object.entries(results).forEach(([category, result]) => {
                const statusIcon = result.status === 'ok' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
                html += `<div class="${result.status === 'ok' ? 'success' : result.status === 'warning' ? 'warning' : 'error'}">`;
                html += `<strong>${statusIcon} ${category.toUpperCase()}:</strong> ${result.message}`;
                if (result.orders !== undefined) html += ` (订单数: ${result.orders})`;
                if (result.users !== undefined) html += ` (用户数: ${result.users})`;
                if (result.missing) html += `<br>缺失: ${result.missing.join(', ')}`;
                if (result.failed) html += `<br>失败: ${result.failed.join(', ')}`;
                html += '</div>';
            });
            
            // 计算总体健康度
            const totalChecks = Object.keys(results).length;
            const passedChecks = Object.values(results).filter(r => r.status === 'ok').length;
            const healthScore = Math.round((passedChecks / totalChecks) * 100);
            
            html += `<div class="${healthScore === 100 ? 'success' : healthScore >= 75 ? 'warning' : 'error'}">`;
            html += `<strong>系统健康度: ${healthScore}% (${passedChecks}/${totalChecks})</strong>`;
            html += '</div>';
            
            document.getElementById('diagnosticResults').innerHTML = html;
            log(`诊断完成，健康度: ${healthScore}%`);
            
            return results;
        }

        // 执行修复
        function runFullRepair() {
            log('开始执行完整修复');
            
            try {
                // 修复1: 修复localStorage数据格式
                log('修复localStorage数据格式...');
                const storageKey = 'ota_order_history';
                const existingData = localStorage.getItem(storageKey);
                
                if (!existingData) {
                    localStorage.setItem(storageKey, JSON.stringify({}));
                    log('✅ 初始化空的历史数据结构');
                } else {
                    const parsed = JSON.parse(existingData);
                    if (Array.isArray(parsed)) {
                        const migratedData = { '<EMAIL>': parsed };
                        localStorage.setItem(storageKey, JSON.stringify(migratedData));
                        log(`✅ 迁移 ${parsed.length} 条旧格式数据`);
                    }
                }
                
                // 修复2: 确保历史管理器正确初始化
                log('初始化历史管理器...');
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    manager.setCurrentUser('<EMAIL>');
                    log('✅ 历史管理器初始化完成');
                } else {
                    log('❌ getOrderHistoryManager 函数不存在');
                }
                
                // 修复3: 强化事件绑定
                log('强化事件绑定...');
                const historyBtn = document.getElementById('historyBtn');
                if (historyBtn) {
                    historyBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        log('历史按钮被点击');
                        
                        try {
                            const manager = getOrderHistoryManager();
                            if (manager && manager.showHistoryPanel) {
                                manager.showHistoryPanel();
                                log('✅ 通过管理器显示历史面板');
                            } else {
                                const historyPanel = document.getElementById('historyPanel');
                                if (historyPanel) {
                                    historyPanel.classList.remove('hidden');
                                    historyPanel.style.display = 'flex';
                                    log('✅ 使用降级方案显示面板');
                                }
                            }
                        } catch (error) {
                            log(`❌ 显示历史面板失败: ${error.message}`);
                        }
                    }, { passive: false });
                    log('✅ 事件绑定加强完成');
                } else {
                    log('❌ historyBtn 元素不存在');
                }
                
                log('🎉 修复完成');
                
                // 重新运行诊断
                setTimeout(() => {
                    runFullDiagnosis();
                }, 1000);
                
            } catch (error) {
                log(`❌ 修复过程出错: ${error.message}`);
            }
        }

        // 功能测试
        function testHistoryModule() {
            log('开始功能测试');
            
            try {
                // 测试1: 获取历史管理器
                const manager = getOrderHistoryManager();
                if (!manager) {
                    log('❌ 无法获取历史管理器');
                    return;
                }
                log('✅ 历史管理器获取成功');
                
                // 测试2: 获取历史数据
                const history = manager.getHistory();
                log(`✅ 获取历史数据成功: ${history.length}条`);
                
                // 测试3: 测试渲染
                if (manager.renderHistory) {
                    manager.renderHistory(history);
                    log('✅ 渲染功能测试通过');
                } else {
                    log('❌ renderHistory 方法不存在');
                }
                
                // 测试4: 测试统计更新
                if (manager.updateStatistics) {
                    manager.updateStatistics(history);
                    log('✅ 统计更新功能测试通过');
                } else {
                    log('❌ updateStatistics 方法不存在');
                }
                
                // 测试5: 测试记录数量更新
                if (manager.updateRecordCount) {
                    manager.updateRecordCount(history.length);
                    log('✅ 记录数量更新功能测试通过');
                } else {
                    log('❌ updateRecordCount 方法不存在');
                }
                
                log('🎉 功能测试完成');
                
            } catch (error) {
                log(`❌ 功能测试失败: ${error.message}`);
            }
        }

        // 创建示例数据
        function createSampleData() {
            log('创建示例数据');
            
            try {
                const manager = getOrderHistoryManager();
                if (!manager) {
                    log('❌ 无法获取历史管理器');
                    return;
                }
                
                const sampleOrders = [
                    {
                        customerName: '测试客户A',
                        customerContact: '+60123456789',
                        pickup: '吉隆坡国际机场',
                        destination: '双子塔',
                        otaReferenceNumber: 'TEST_001',
                        otaChannel: 'Booking.com',
                        date: new Date().toISOString().split('T')[0],
                        time: '14:30'
                    },
                    {
                        customerName: '测试客户B',
                        customerContact: '+60987654321',
                        pickup: 'KL Sentral',
                        destination: 'KLCC',
                        otaReferenceNumber: 'TEST_002',
                        otaChannel: 'Agoda',
                        date: new Date().toISOString().split('T')[0],
                        time: '16:45'
                    }
                ];
                
                sampleOrders.forEach((orderData, index) => {
                    const orderId = `SAMPLE_${Date.now()}_${index}`;
                    manager.addOrder(orderData, orderId, {
                        success: true,
                        message: '示例订单创建成功'
                    });
                });
                
                log(`✅ 已创建 ${sampleOrders.length} 个示例订单`);
                
                // 重新运行诊断和测试
                setTimeout(() => {
                    runFullDiagnosis();
                    testHistoryModule();
                }, 500);
                
            } catch (error) {
                log(`❌ 创建示例数据失败: ${error.message}`);
            }
        }

        // 清除所有数据
        function clearAllData() {
            if (confirm('确定要清除所有历史订单数据吗？此操作不可恢复！')) {
                try {
                    localStorage.removeItem('ota_order_history');
                    log('✅ 所有历史订单数据已清除');
                    
                    // 重新运行诊断
                    setTimeout(runFullDiagnosis, 500);
                    
                } catch (error) {
                    log(`❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        // 导出日志
        function exportLogs() {
            const logData = {
                timestamp: new Date().toISOString(),
                logs: logMessages,
                systemInfo: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    localStorage: localStorage.getItem('ota_order_history')
                }
            };
            
            const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `history-repair-log-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('✅ 日志已导出');
        }

        // 页面加载时自动运行诊断
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                log('页面加载完成，开始自动诊断');
                runFullDiagnosis();
            }, 1000);
        });
    </script>
</body>
</html>
