<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Order Clean Fields Test</title>
    
    <!-- 引入必需的CSS文件 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <link rel="stylesheet" href="css/multi-order/mobile.css">
    
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .test-button {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .results {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .field-item {
            margin: 5px 0;
            padding: 5px;
            background: var(--bg-primary);
            border-radius: 3px;
        }
        
        .status-ok { color: #4caf50; }
        .status-warn { color: #ff9800; }
        .status-error { color: #f44336; }
    </style>
</head>
<body>
    <h1>多订单字段清理验证</h1>
    
    <div class="test-section">
        <h2>字段清理状态</h2>
        <p>已移除的字段：</p>
        <ul>
            <li>❌ currency (币种)</li>
            <li>❌ arrivalTime (到达时间)</li>
            <li>❌ departureTime (离开时间)</li>
            <li>❌ duration (时长)</li>
            <li>❌ distance (距离)</li>
        </ul>
        <p>修正的字段：</p>
        <ul>
            <li>🔄 otaChannel → ota (由 #batchOtaSelect 控制)</li>
        </ul>
        
        <button class="test-button" onclick="testCleanFields()">验证字段清理</button>
        <button class="test-button" onclick="testFieldMapping()">测试字段映射</button>
        <button class="test-button" onclick="clearResults()">清空结果</button>
        
        <div class="results" id="results"></div>
    </div>

    <!-- 隐藏的多订单面板 -->
    <div class="multi-order-panel hidden" id="multiOrderPanel">
        <div class="multi-order-content">
            <button class="multi-order-close-btn" onclick="closeMultiOrder()">×</button>
            <div class="multi-order-container" id="multiOrderContainer">
                <!-- 多订单内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入必需的JS文件 -->
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>
    
    <script>
        // 测试用的订单数据
        const cleanTestOrders = [
            {
                customer_name: "张三",
                customer_contact: "0123456789", 
                pickup: "KLIA",
                dropoff: "Sunway Pyramid",
                pickup_date: "2024-01-15",
                pickup_time: "14:30",
                price: 80.50,
                ota_channel: "Booking.com", // 应该映射到 ota 字段
                vehicle_type: 2, // 应该显示为 Premium
                driving_region: 1, // 应该显示为 Kuala Lumpur
                passenger_count: 2,
                meet_and_greet: true, // 应该显示为 "接机服务"
                baby_chair: false, // 不应该显示
                flight_info: "MH123",
                extra_requirement: "需要中文司机"
            }
        ];
        
        function logResult(message, status = 'ok') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `field-item status-${status}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function initializeSystemData() {
            const systemData = {
                carTypes: [
                    { id: 1, name: 'Standard' },
                    { id: 2, name: 'Premium' },
                    { id: 3, name: 'Luxury' }
                ],
                drivingRegions: [
                    { id: 1, name: 'Kuala Lumpur' },
                    { id: 2, name: 'Selangor' },
                    { id: 3, name: 'Penang' }
                ]
            };
            
            if (typeof getAppState === 'function') {
                getAppState().set('systemData', systemData);
            }
            
            if (typeof getApiService === 'function') {
                const apiService = getApiService();
                if (apiService) {
                    apiService.staticData = systemData;
                }
            }
            
            logResult('✅ 系统数据初始化完成', 'ok');
        }
        
        function testCleanFields() {
            logResult('=== 开始字段清理验证 ===', 'ok');
            
            try {
                initializeSystemData();
                const manager = new MultiOrderManagerV2();
                
                // 显示多订单面板
                manager.showMultiOrderPanel(cleanTestOrders);
                
                setTimeout(() => {
                    checkRemovedFields();
                    checkModifiedFields();
                    checkFieldValues();
                }, 500);
                
            } catch (error) {
                logResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        function checkRemovedFields() {
            logResult('--- 检查已移除字段 ---', 'ok');
            
            const removedFields = ['currency', 'arrivalTime', 'departureTime', 'duration', 'distance'];
            
            removedFields.forEach(fieldName => {
                const fieldElements = document.querySelectorAll(`[data-field="${fieldName}"]`);
                if (fieldElements.length === 0) {
                    logResult(`✅ 字段 ${fieldName} 已正确移除`, 'ok');
                } else {
                    logResult(`❌ 字段 ${fieldName} 仍然存在 (${fieldElements.length}个)`, 'error');
                }
            });
        }
        
        function checkModifiedFields() {
            logResult('--- 检查字段修正 ---', 'ok');
            
            // 检查 otaChannel 是否改为 ota
            const oldOtaFields = document.querySelectorAll('[data-field="otaChannel"]');
            const newOtaFields = document.querySelectorAll('[data-field="ota"]');
            
            if (oldOtaFields.length === 0) {
                logResult('✅ 旧字段 otaChannel 已移除', 'ok');
            } else {
                logResult(`❌ 旧字段 otaChannel 仍然存在 (${oldOtaFields.length}个)`, 'error');
            }
            
            if (newOtaFields.length > 0) {
                logResult(`✅ 新字段 ota 已存在 (${newOtaFields.length}个)`, 'ok');
                
                // 检查字段值
                newOtaFields.forEach((field, index) => {
                    const value = field.querySelector('.grid-value')?.textContent || '';
                    logResult(`   OTA字段 ${index + 1}: "${value}"`, 'ok');
                });
            } else {
                logResult('❌ 新字段 ota 不存在', 'error');
            }
        }
        
        function checkFieldValues() {
            logResult('--- 检查字段值显示 ---', 'ok');
            
            // 检查车型字段是否显示名称而不是ID
            const vehicleFields = document.querySelectorAll('[data-field="vehicleType"]');
            vehicleFields.forEach((field, index) => {
                const value = field.querySelector('.grid-value')?.textContent || '';
                if (/^\d+$/.test(value)) {
                    logResult(`⚠️ 车型字段 ${index + 1} 显示ID: ${value}`, 'warn');
                } else if (value) {
                    logResult(`✅ 车型字段 ${index + 1} 显示名称: ${value}`, 'ok');
                }
            });
            
            // 检查区域字段是否显示名称而不是ID
            const regionFields = document.querySelectorAll('[data-field="drivingRegion"]');
            regionFields.forEach((field, index) => {
                const value = field.querySelector('.grid-value')?.textContent || '';
                if (/^\d+$/.test(value)) {
                    logResult(`⚠️ 区域字段 ${index + 1} 显示ID: ${value}`, 'warn');
                } else if (value) {
                    logResult(`✅ 区域字段 ${index + 1} 显示名称: ${value}`, 'ok');
                }
            });
            
            // 检查布尔字段显示
            const booleanTests = [
                { field: 'meetAndGreet', expected: '接机服务' },
                { field: 'babyChair', expected: '' } // false值不应该显示
            ];
            
            booleanTests.forEach(test => {
                const field = document.querySelector(`[data-field="${test.field}"]`);
                if (test.expected === '') {
                    if (!field) {
                        logResult(`✅ 布尔字段 ${test.field} (false) 正确隐藏`, 'ok');
                    } else {
                        logResult(`⚠️ 布尔字段 ${test.field} (false) 不应该显示`, 'warn');
                    }
                } else {
                    if (field) {
                        const value = field.querySelector('.grid-value')?.textContent || '';
                        if (value === test.expected) {
                            logResult(`✅ 布尔字段 ${test.field} 显示正确: ${value}`, 'ok');
                        } else {
                            logResult(`⚠️ 布尔字段 ${test.field} 显示错误: ${value} (期望: ${test.expected})`, 'warn');
                        }
                    } else {
                        logResult(`❌ 布尔字段 ${test.field} 未找到`, 'error');
                    }
                }
            });
        }
        
        function testFieldMapping() {
            logResult('=== 测试字段映射功能 ===', 'ok');
            
            try {
                initializeSystemData();
                const manager = new MultiOrderManagerV2();
                
                // 测试各种字段映射
                const testCases = [
                    { field: 'vehicleType', value: 2, expected: 'Premium' },
                    { field: 'drivingRegion', value: 1, expected: 'Kuala Lumpur' },
                    { field: 'meetAndGreet', value: true, expected: '接机服务' },
                    { field: 'babyChair', value: false, expected: '' },
                    { field: 'ota', value: 'Booking.com', expected: 'Booking.com' }
                ];
                
                testCases.forEach(testCase => {
                    const result = manager.processFieldValue ? 
                        manager.processFieldValue(testCase.value, testCase.field) : 
                        manager.getFieldValue({ [testCase.field]: testCase.value }, testCase.field);
                    
                    const status = result === testCase.expected ? 'ok' : 'warn';
                    const statusIcon = status === 'ok' ? '✅' : '⚠️';
                    logResult(`${statusIcon} ${testCase.field}: ${testCase.value} → "${result}" (期望: "${testCase.expected}")`, status);
                });
                
            } catch (error) {
                logResult(`❌ 字段映射测试失败: ${error.message}`, 'error');
            }
        }
        
        function closeMultiOrder() {
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.add('hidden');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            logResult('字段清理验证页面加载完成', 'ok');
            logResult('点击"验证字段清理"按钮开始测试', 'ok');
        });
    </script>
</body>
</html>
