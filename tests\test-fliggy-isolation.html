<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fliggy特供配置隔离测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .fliggy-only { background-color: #fff3cd; border-color: #ffeaa7; }
        .general-ota { background-color: #d1ecf1; border-color: #b3d4fc; }
        .isolation-test { background-color: #f8d7da; border-color: #f5c6cb; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔒 Fliggy特供配置完全隔离测试</h1>
    
    <div class="test-section">
        <h2>📋 隔离测试结果</h2>
        <div id="testResults"></div>
    </div>

    <!-- 按修复后的加载顺序引入文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/hotel-name-database.js"></script>
    <script src="js/address-translation-service.js"></script>
    <script src="js/ota-system/ota-system-loader.js"></script>

    <script>
        // 隔离测试脚本
        function runIsolationTests() {
            const results = [];
            const resultDiv = document.getElementById('testResults');

            // 测试1: Fliggy特供功能隔离检查
            results.push('=== 1. Fliggy特供功能隔离检查 ===');
            
            if (window.OTA && window.OTA.customizationEngine) {
                const engine = window.OTA.customizationEngine;
                
                // 尝试在非Fliggy上下文中使用Fliggy特供功能
                try {
                    // 这应该失败，因为没有设置Fliggy上下文
                    const result = engine.containsChinese('测试中文');
                    results.push(`✅ containsChinese函数正常工作: ${result}`);
                    
                    // 尝试在非Fliggy上下文中翻译地址（应该被阻止）
                    engine.translateChineseAddress('新山万丽酒店').then(translation => {
                        if (translation === null) {
                            results.push('✅ 地址翻译正确阻止非Fliggy访问');
                        } else {
                            results.push('❌ 地址翻译未正确隔离');
                        }
                        updateResults();
                    });
                    
                } catch (error) {
                    results.push(`❌ Fliggy功能访问测试失败: ${error.message}`);
                }
            }

            // 测试2: 不同渠道配置完全分离
            results.push('\n=== 2. 渠道配置分离测试 ===');
            
            if (window.OTA && window.OTA.customizationEngine) {
                const engine = window.OTA.customizationEngine;
                
                // 测试各渠道配置独立性
                const channels = ['Fliggy', 'Ctrip', 'Chong Dealer', 'Klook West Malaysia', 'default'];
                
                channels.forEach(channel => {
                    const config = engine.getChannelConfig(channel);
                    results.push(`   - ${channel}: ${Object.keys(config.fieldProcessing).length}个专属字段处理`);
                    
                    if (channel === 'Fliggy') {
                        // Fliggy应该有客户名字和地址处理
                        if (config.fieldProcessing.customer_name && 
                            config.fieldProcessing.pickup_location && 
                            config.fieldProcessing.dropoff_location) {
                            results.push('     ✅ Fliggy专属字段处理完整');
                        } else {
                            results.push('     ❌ Fliggy专属字段处理缺失');
                        }
                    } else {
                        // 其他渠道不应该有地址翻译处理
                        if (!config.fieldProcessing.pickup_location && 
                            !config.fieldProcessing.dropoff_location) {
                            results.push(`     ✅ ${channel}正确隔离，无地址翻译`);
                        } else {
                            results.push(`     ❌ ${channel}未正确隔离，存在地址翻译`);
                        }
                    }
                });
            }

            // 测试3: Fliggy专属价格计算隔离
            results.push('\n=== 3. Fliggy专属价格计算测试 ===');
            
            if (window.OTA && window.OTA.customizationEngine) {
                const engine = window.OTA.customizationEngine;
                
                // Fliggy特殊价格计算
                const fliggyOrder = {
                    orderContent: 'malaysia kuala lumpur',
                    passenger_count: 5
                };
                const fliggyPrice = engine.calculateChannelPrice('Fliggy', 100, fliggyOrder);
                
                // 通用渠道价格计算
                const generalOrder = {
                    passenger_count: 5
                };
                const generalPrice = engine.calculateChannelPrice('default', 100, generalOrder);
                
                results.push(`   - Fliggy价格: ${fliggyPrice.originalPrice} → ${fliggyPrice.finalPrice}`);
                results.push(`   - 通用价格: ${generalPrice.originalPrice} → ${generalPrice.finalPrice}`);
                
                if (fliggyPrice.finalPrice !== generalPrice.finalPrice) {
                    results.push('   ✅ Fliggy价格计算正确隔离');
                } else {
                    results.push('   ❌ Fliggy价格计算未正确隔离');
                }
            }

            // 测试4: 上下文隔离测试
            results.push('\n=== 4. 处理上下文隔离测试 ===');
            
            (async () => {
                if (window.OTA && window.OTA.customizationEngine) {
                    const engine = window.OTA.customizationEngine;
                    
                    try {
                        // 测试Fliggy上下文
                        const fliggyTestData = {
                            customer_name: '原始客户',
                            contact_name: '联系人张先生',
                            pickup_location: '新山万丽酒店'
                        };
                        
                        const fliggyResult = await engine.processChannelFields('Fliggy', fliggyTestData);
                        results.push(`   - Fliggy处理结果: 客户名=${fliggyResult.customer_name}`);
                        if (fliggyResult.pickup_location_en) {
                            results.push(`   - Fliggy地址翻译: ${fliggyResult.pickup_location_en}`);
                        }
                        
                        // 测试非Fliggy上下文
                        const generalTestData = {
                            customer_name: '通用客户',
                            pickup_location: '新山万丽酒店'
                        };
                        
                        const generalResult = await engine.processChannelFields('Ctrip', generalTestData);
                        results.push(`   - Ctrip处理结果: 客户名=${generalResult.customer_name}`);
                        if (!generalResult.pickup_location_en) {
                            results.push('   ✅ Ctrip正确隔离，无地址翻译');
                        } else {
                            results.push('   ❌ Ctrip未正确隔离，存在地址翻译');
                        }
                        
                    } catch (error) {
                        results.push(`❌ 上下文隔离测试失败: ${error.message}`);
                    }
                }
                
                updateResults();
            })();

            // 测试5: 特征识别精确性
            results.push('\n=== 5. Fliggy特征识别精确性测试 ===');
            
            const testCases = [
                { ref: '订单编号1234567890123456789', channel: '', expected: true, desc: '标准Fliggy订单号' },
                { ref: 'CD123456AB', channel: '', expected: false, desc: 'Chong Dealer订单号' },
                { ref: 'KL789ABC', channel: '', expected: false, desc: 'Klook订单号' },
                { ref: 'NORMAL123', channel: 'Fliggy', expected: true, desc: '明确Fliggy渠道' },
                { ref: 'TEST456', channel: 'Ctrip', expected: false, desc: '其他渠道' }
            ];
            
            testCases.forEach(testCase => {
                const mockOrderData = {
                    otaReferenceNumber: testCase.ref,
                    otaChannel: testCase.channel
                };
                
                // 模拟识别逻辑
                const otaRef = mockOrderData.otaReferenceNumber || '';
                const otaChannel = mockOrderData.otaChannel || '';
                const isFliggyOrder = /订单编号\d{19}/.test(otaRef) || 
                                     otaChannel.toLowerCase().includes('fliggy') ||
                                     otaRef.toLowerCase().includes('fliggy');
                
                const result = isFliggyOrder === testCase.expected ? '✅' : '❌';
                results.push(`   ${result} ${testCase.desc}: ${isFliggyOrder ? 'Fliggy' : '非Fliggy'}`);
            });

            function updateResults() {
                resultDiv.innerHTML = '<pre>' + results.join('\n') + '</pre>';
            }
            
            updateResults();
        }

        // 等待所有脚本加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runIsolationTests, 1000);
        });
    </script>
</body>
</html>
