/**
 * ============================================================================
 * 🚀 核心业务流程 - 地址翻译器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 地址翻译器 - 子层实现
 * @description 负责地址翻译和标准化处理，从gemini-service.js拆分而来
 * 
 * @businessFlow 地址翻译和标准化
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * 订单数据解析 → 【当前文件职责】地址翻译和标准化 - 本地处理
 *     ↓
 * 标准化订单数据 → 订单管理
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：地址翻译的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为订单解析器提供翻译服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - flow/order-parser.js (订单解析时调用)
 * - flow/knowledge-base.js (查询酒店和机场数据)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 中文地址到英文/马来文翻译
 * - 🟢 酒店名称标准化和匹配
 * - 🟢 机场代码和名称转换
 * - 🟢 地址格式化和清理
 * - 🟢 翻译结果缓存和优化
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地翻译模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有翻译结果格式
 * - 兼容现有的翻译接口
 * - 保持翻译准确率
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程翻译API（严格本地处理）
 * - ✅ 不能依赖其他子层（除知识库查询）
 * - ✅ 必须保持翻译准确率
 * - ✅ 保持现有的翻译逻辑
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 地址翻译器 - 子层实现
     */
    class AddressTranslator {
        constructor() {
            this.logger = this.getLogger();
            
            // 翻译配置
            this.config = {
                supportedLanguages: ['en', 'ms'],
                defaultTargetLanguages: ['ms', 'en'],
                cacheTimeout: 30 * 60 * 1000, // 30分钟缓存
                minAddressLength: 3,
                maxAddressLength: 200
            };

            // 翻译缓存
            this.translationCache = new Map();
            
            // 常用地址翻译映射
            this.commonTranslations = new Map([
                // 机场相关
                ['吉隆坡国际机场', { en: 'Kuala Lumpur International Airport', ms: 'Lapangan Terbang Antarabangsa Kuala Lumpur' }],
                ['梳邦机场', { en: 'Sultan Abdul Aziz Shah Airport', ms: 'Lapangan Terbang Sultan Abdul Aziz Shah' }],
                ['KLIA', { en: 'KLIA', ms: 'KLIA' }],
                ['KLIA2', { en: 'KLIA2', ms: 'KLIA2' }],
                
                // 常用地点
                ['双子塔', { en: 'Petronas Twin Towers', ms: 'Menara Berkembar Petronas' }],
                ['市中心', { en: 'City Center', ms: 'Pusat Bandar' }],
                ['武吉免登', { en: 'Bukit Bintang', ms: 'Bukit Bintang' }],
                ['中央车站', { en: 'KL Sentral', ms: 'KL Sentral' }],
                
                // 酒店相关
                ['酒店', { en: 'Hotel', ms: 'Hotel' }],
                ['度假村', { en: 'Resort', ms: 'Resort' }],
                ['公寓', { en: 'Apartment', ms: 'Apartmen' }]
            ]);
            
            this.logger.log('地址翻译器已初始化', 'info');
        }

        /**
         * 翻译地址
         * @param {string} address - 需要翻译的地址
         * @param {array} targetLanguages - 目标语言数组
         * @returns {Promise<object>} 翻译结果
         */
        async translateAddress(address, targetLanguages = ['ms', 'en']) {
            try {
                if (!address || typeof address !== 'string') {
                    throw new Error('无效的地址输入');
                }

                const cleanAddress = address.trim();
                if (cleanAddress.length < this.config.minAddressLength) {
                    return { original: address, translations: {} };
                }

                this.logger.log('开始翻译地址', 'info', { 
                    address: cleanAddress,
                    targetLanguages 
                });

                // 检查缓存
                const cacheKey = `${cleanAddress}_${targetLanguages.join('_')}`;
                const cachedResult = this.getCachedTranslation(cacheKey);
                if (cachedResult) {
                    this.logger.log('使用缓存翻译结果', 'info');
                    return cachedResult;
                }

                // 执行翻译
                const translations = {};
                
                for (const lang of targetLanguages) {
                    const translation = await this.translateToLanguage(cleanAddress, lang);
                    if (translation) {
                        translations[lang] = translation;
                    }
                }

                const result = {
                    original: cleanAddress,
                    translations: translations,
                    translatedAt: new Date().toISOString()
                };

                // 缓存结果
                this.setCachedTranslation(cacheKey, result);

                this.logger.log('地址翻译完成', 'success', { 
                    address: cleanAddress,
                    translationCount: Object.keys(translations).length 
                });

                return result;

            } catch (error) {
                this.logger.log('地址翻译失败', 'error', { error: error.message });
                return { 
                    original: address, 
                    translations: {}, 
                    error: error.message 
                };
            }
        }

        /**
         * 翻译到指定语言
         * @param {string} address - 地址
         * @param {string} targetLanguage - 目标语言
         * @returns {Promise<string|null>} 翻译结果
         */
        async translateToLanguage(address, targetLanguage) {
            try {
                // 1. 检查常用翻译映射
                const commonTranslation = this.commonTranslations.get(address);
                if (commonTranslation && commonTranslation[targetLanguage]) {
                    return commonTranslation[targetLanguage];
                }

                // 2. 查询知识库
                const knowledgeBase = window.OTA?.knowledgeBase;
                if (knowledgeBase) {
                    // 查询酒店数据
                    const hotelInfo = knowledgeBase.queryHotel(address);
                    if (hotelInfo && targetLanguage === 'en' && hotelInfo.english) {
                        return hotelInfo.english;
                    }

                    // 查询机场数据
                    const airportInfo = knowledgeBase.queryAirport(address);
                    if (airportInfo) {
                        if (targetLanguage === 'en') {
                            return airportInfo.name;
                        }
                        if (targetLanguage === 'ms' && airportInfo.malay) {
                            return airportInfo.malay;
                        }
                    }
                }

                // 3. 基于规则的翻译
                return this.ruleBasedTranslation(address, targetLanguage);

            } catch (error) {
                this.logger.log('语言翻译失败', 'error', { 
                    address, 
                    targetLanguage, 
                    error: error.message 
                });
                return null;
            }
        }

        /**
         * 基于规则的翻译
         * @param {string} address - 地址
         * @param {string} targetLanguage - 目标语言
         * @returns {string|null} 翻译结果
         */
        ruleBasedTranslation(address, targetLanguage) {
            // 简单的规则翻译
            if (targetLanguage === 'en') {
                // 检查是否已经是英文
                if (/^[a-zA-Z\s\d,.-]+$/.test(address)) {
                    return address; // 已经是英文，直接返回
                }
            }

            // 如果无法翻译，返回原地址
            return address;
        }

        /**
         * 异步地址翻译（不阻塞主流程）
         * @param {string} address - 原始地址
         * @param {string} fieldName - 字段名称
         * @param {object} dataObject - 数据对象引用
         */
        async translateAddressAsync(address, fieldName, dataObject) {
            try {
                if (!address || typeof address !== 'string' || address.trim().length < 3) {
                    return; // 地址太短或无效，跳过翻译
                }

                // 检查是否包含中文字符，如果没有则跳过翻译
                if (!/[\u4e00-\u9fa5]/.test(address)) {
                    return; // 不包含中文，跳过翻译
                }

                const translationResult = await this.translateAddress(address);
                
                // 更新数据对象
                if (translationResult.translations.en) {
                    dataObject[`${fieldName}_en`] = translationResult.translations.en;
                }
                if (translationResult.translations.ms) {
                    dataObject[`${fieldName}_ms`] = translationResult.translations.ms;
                }

            } catch (error) {
                this.logger.log('异步地址翻译失败', 'error', { 
                    address, 
                    fieldName, 
                    error: error.message 
                });
            }
        }

        /**
         * 获取缓存翻译结果
         * @param {string} cacheKey - 缓存键
         * @returns {object|null} 缓存结果
         */
        getCachedTranslation(cacheKey) {
            const cached = this.translationCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
                return cached.result;
            }
            return null;
        }

        /**
         * 设置缓存翻译结果
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 翻译结果
         */
        setCachedTranslation(cacheKey, result) {
            this.translationCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });

            // 清理过期缓存
            this.cleanExpiredCache();
        }

        /**
         * 清理过期缓存
         */
        cleanExpiredCache() {
            const now = Date.now();
            for (const [key, cached] of this.translationCache.entries()) {
                if (now - cached.timestamp >= this.config.cacheTimeout) {
                    this.translationCache.delete(key);
                }
            }
        }

        /**
         * 获取翻译统计信息
         * @returns {object} 统计信息
         */
        getTranslationStats() {
            return {
                cacheSize: this.translationCache.size,
                commonTranslations: this.commonTranslations.size,
                supportedLanguages: this.config.supportedLanguages,
                version: '2.0.0'
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const addressTranslator = new AddressTranslator();

    // 导出到全局作用域
    window.AddressTranslator = AddressTranslator;
    window.OTA.AddressTranslator = AddressTranslator;
    window.OTA.addressTranslator = addressTranslator;

    console.log('✅ AddressTranslator (子层实现) 已加载');

})();
