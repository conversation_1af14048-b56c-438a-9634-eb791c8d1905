/**
 * ============================================================================
 * 🚀 核心业务流程 - Gemini API调用器 (子层实现)
 * ============================================================================
 *
 * @fileoverview Gemini API调用器 - 子层实现
 * @description 负责Gemini API调用的具体实现，处理远程AI服务
 * 
 * @businessFlow Gemini API调用
 * 在核心业务流程中的位置：
 * 输入内容 → 本地渠道特征检测 → 提示词组合
 *     ↓
 * 【当前文件职责】发送Gemini API - 远程处理
 *     ↓
 * Gemini返回解析结果 - 远程处理
 *     ↓
 * 本地结果处理 (result-processor.js) → 订单管理
 *
 * @architecture Child Layer (子层) - 远程处理实现
 * - 职责：Gemini API调用的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供API调用服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责
 * - 🟢 API请求参数构建和验证
 * - 🟢 错误处理和重试机制
 * - 🟢 响应数据解析和格式化
 * - 🟢 请求缓存和去重
 *
 * @remoteProcessing 远程处理职责（核心功能）
 * - 🔴 调用Gemini API进行文本解析
 * - 🔴 调用Gemini Vision API进行图像分析
 * - 🔴 调用Gemini API进行多订单检测和分割
 * - 🔴 处理API响应和错误
 *
 * @compatibility 兼容性保证
 * - 保持现有API调用接口不变
 * - 兼容现有的错误处理机制
 * - 保持响应数据格式一致
 *
 * @refactoringConstraints 重构约束
 * - ✅ 专注于API调用，不处理业务逻辑
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持API调用的稳定性
 * - ✅ 保持现有的重试和错误处理机制
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * Gemini API调用器 - 子层实现
     */
    class GeminiCaller {
        constructor() {
            this.logger = this.getLogger();
            
            // 🚀 性能优化：API配置优化（大幅提升响应速度）
            this.config = {
                apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 个人项目API密钥
                modelVersion: 'gemini-2.5-flash',
                baseTimeout: 6000,    // 🚀 优化：基础超时6秒，快速发现网络问题
                maxRetries: 2,        // 🚀 优化：减少重试次数到2次，避免过长等待
                retryDelays: [500, 1500], // 🚀 优化：指数退避策略 [0.5s, 1.5s]
                maxOutputTokens: 8192 // 🚀 优化：从20000降到8192，平衡质量与速度
            };

            // 构建API URL
            this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.config.modelVersion}:generateContent`;
            
            // 🚀 性能优化：请求缓存配置（智能缓存策略）
            this.requestCache = new Map();
            this.pendingRequests = new Map(); // 🚀 新增：防止重复请求的待处理队列
            this.cacheTimeout = 3 * 60 * 1000; // 3分钟缓存（普通请求）
            this.realtimeCacheTimeout = 10 * 1000; // 10秒缓存（实时请求，快速响应用户修改）
            
            this.logger.log('Gemini API调用器已初始化', 'info', { 
                model: this.config.modelVersion 
            });
        }

        /**
         * 调用API - 统一入口
         * @param {string} prompt - 提示词
         * @param {string} type - 调用类型 ('text' | 'image')
         * @param {object} options - 调用选项
         * @returns {Promise<object>} API响应结果
         */
        async callAPI(prompt, type = 'text', options = {}) {
            try {
                this.logger.log('开始调用Gemini API', 'info', { 
                    type, 
                    promptLength: prompt.length,
                    options 
                });

                // 🚀 性能优化：检查缓存（支持不同超时策略）
                const cacheKey = this.generateCacheKey(prompt, type, options);
                const cachedResult = this.getCachedResult(cacheKey, options.isRealtime);
                if (cachedResult) {
                    this.logger.log('🚀 使用缓存结果，跳过API调用', 'info', {
                        cacheKey: cacheKey.substring(0, 20) + '...',
                        isRealtime: options.isRealtime
                    });
                    return cachedResult;
                }

                // 🚀 性能优化：检查是否有相同请求正在处理（请求去重）
                if (this.pendingRequests.has(cacheKey)) {
                    this.logger.log('🚀 等待相同请求完成，避免重复调用', 'info', {
                        cacheKey: cacheKey.substring(0, 20) + '...'
                    });
                    return await this.pendingRequests.get(cacheKey);
                }

                // 🚀 创建待处理的请求Promise
                const requestPromise = (async () => {
                    try {
                        let result;
                        if (type === 'image') {
                            result = await this.callVisionAPI(prompt, options);
                        } else {
                            result = await this.callTextAPI(prompt, options);
                        }

                        // 缓存结果
                        this.setCachedResult(cacheKey, result);

                        this.logger.log('Gemini API调用成功', 'success', { 
                            type,
                            hasResult: !!result 
                        });

                        return result;
                    } finally {
                        // 🚀 清理待处理请求记录
                        this.pendingRequests.delete(cacheKey);
                    }
                })();

                // 🚀 将请求添加到待处理队列
                this.pendingRequests.set(cacheKey, requestPromise);

                // 返回请求结果
                return await requestPromise;

            } catch (error) {
                this.logger.log('Gemini API调用失败', 'error', { 
                    type,
                    error: error.message 
                });
                throw error;
            }
        }

        /**
         * 调用文本API
         * @param {string} prompt - 文本提示词
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async callTextAPI(prompt, options = {}) {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: this.config.maxOutputTokens,  // 🚀 使用配置文件的8192 token限制
                    responseMimeType: "application/json"
                }
            };

            return await this.makeAPIRequest(requestBody, options);
        }

        /**
         * 调用Vision API
         * @param {string} base64Image - Base64图片数据
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async callVisionAPI(base64Image, options = {}) {
            // 构建Vision API请求
            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: "请分析这张图片中的订单信息，并按照JSON格式返回结构化数据。"
                        },
                        {
                            inline_data: {
                                mime_type: "image/jpeg",
                                data: base64Image.replace(/^data:image\/[a-z]+;base64,/, '')
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: this.config.maxOutputTokens,  // 🚀 使用配置文件的8192 token限制
                    responseMimeType: "application/json"
                }
            };

            return await this.makeAPIRequest(requestBody, options);
        }

        /**
         * 执行API请求
         * @param {object} requestBody - 请求体
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async makeAPIRequest(requestBody, options = {}) {
            let lastError;

            for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
                // 🚀 性能监控：记录API调用开始时间
                const apiStartTime = performance.now();

                // 🚀 优化：梯度超时策略 - 首次6秒，后续递增
                const currentTimeout = this.config.baseTimeout + (attempt * 2000);

                try {
                    // 🚀 最小化修复：添加输出token限制到请求体
                    const requestBodyWithLimits = {
                        ...requestBody,
                        generationConfig: {
                            ...requestBody.generationConfig,
                            maxOutputTokens: this.config.maxOutputTokens
                        }
                    };

                    const response = await fetch(`${this.baseURL}?key=${this.config.apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBodyWithLimits),
                        signal: AbortSignal.timeout(currentTimeout)
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
                    }

                    const data = await response.json();

                    // 🚀 性能监控：记录成功的API调用时间
                    const apiDuration = performance.now() - apiStartTime;
                    this.logger.log(`🚀 Gemini API调用成功: ${apiDuration.toFixed(2)}ms`, 'success', {
                        attempt: attempt + 1,
                        timeout: currentTimeout,
                        model: this.config.modelVersion
                    });

                    return this.parseAPIResponse(data);

                } catch (error) {
                    lastError = error;
                    this.logger.log(`API请求失败 (尝试 ${attempt + 1}/${this.config.maxRetries})`, 'warning', { 
                        error: error.message,
                        timeout: currentTimeout
                    });

                    if (attempt < this.config.maxRetries - 1) {
                        // 🚀 优化：指数退避策略，避免频率限制
                        const retryDelay = this.config.retryDelays[attempt] || 2000;
                        this.logger.log(`等待 ${retryDelay}ms 后重试...`, 'info');
                        await this.delay(retryDelay);
                    }
                }
            }

            throw new Error(`API请求最终失败: ${lastError.message}`);
        }

        /**
         * 解析API响应
         * @param {object} data - API响应数据
         * @returns {object} 解析后的结果
         */
        parseAPIResponse(data) {
            try {
                // 🔧 临时调试：记录完整的API响应结构
                console.log('🔍 Gemini API完整响应:', JSON.stringify(data, null, 2));

                // 🚀 最小化修复：增强API响应格式兼容性
                if (!data.candidates || data.candidates.length === 0) {
                    throw new Error('API响应中没有候选结果');
                }

                const candidate = data.candidates[0];
                console.log('🔍 第一个候选结果:', JSON.stringify(candidate, null, 2));

                // 检查是否被安全过滤器阻止或达到token限制
                if (candidate.finishReason === 'SAFETY') {
                    throw new Error('内容被安全过滤器阻止');
                }

                // 🚀 最小化修复：处理MAX_TOKENS情况
                if (candidate.finishReason === 'MAX_TOKENS') {
                    throw new Error('API响应因达到最大token限制而截断，请减少提示词长度');
                }

                // 更宽松的内容检查
                if (!candidate.content) {
                    throw new Error('API响应中没有内容');
                }

                if (!candidate.content.parts || candidate.content.parts.length === 0) {
                    throw new Error('API响应内容部分为空');
                }

                const textContent = candidate.content.parts[0].text;
                if (!textContent) {
                    throw new Error('API响应文本内容为空');
                }

                // 尝试解析JSON
                try {
                    return JSON.parse(textContent);
                } catch (jsonError) {
                    // 如果不是JSON格式，返回原始文本
                    return { text: textContent, rawResponse: true };
                }

            } catch (error) {
                this.logger.log('API响应解析失败', 'error', {
                    error: error.message,
                    responseStructure: data ? Object.keys(data) : 'null',
                    candidateCount: data?.candidates?.length || 0,
                    firstCandidate: data?.candidates?.[0] ? Object.keys(data.candidates[0]) : 'none'
                });

                // 🚀 最小化修复：提供fallback机制
                if (data && typeof data === 'object') {
                    // 尝试从其他可能的位置提取内容
                    const fallbackContent = this.extractFallbackContent(data);
                    if (fallbackContent) {
                        this.logger.log('使用fallback内容解析', 'warning', { content: fallbackContent.substring(0, 100) + '...' });
                        return { text: fallbackContent, rawResponse: true, fallback: true };
                    }
                }

                throw new Error(`响应解析失败: ${error.message}`);
            }
        }

        /**
         * 🚀 最小化修复：从API响应中提取fallback内容
         * @param {object} data - API响应数据
         * @returns {string|null} 提取的内容
         */
        extractFallbackContent(data) {
            try {
                // 尝试多种可能的响应格式
                if (data.candidates && data.candidates.length > 0) {
                    const candidate = data.candidates[0];

                    // 尝试不同的内容路径
                    if (candidate.content?.parts?.[0]?.text) {
                        return candidate.content.parts[0].text;
                    }
                    if (candidate.text) {
                        return candidate.text;
                    }
                    if (candidate.message) {
                        return candidate.message;
                    }
                }

                // 尝试直接从根级别提取
                if (data.text) {
                    return data.text;
                }
                if (data.message) {
                    return data.message;
                }
                if (data.content) {
                    return typeof data.content === 'string' ? data.content : JSON.stringify(data.content);
                }

                return null;
            } catch (error) {
                return null;
            }
        }

        /**
         * 生成缓存键
         * 🚀 性能优化：为实时输入优化缓存策略
         * @param {string} prompt - 提示词
         * @param {string} type - 类型
         * @param {object} options - 选项
         * @returns {string} 缓存键
         */
        generateCacheKey(prompt, type, options) {
            // 🚀 性能优化：对于实时输入，使用简化的缓存键
            if (options.isRealtime) {
                // 提取关键信息作为缓存键，忽略细微差异
                const keyInfo = this.extractKeyInfoForCache(prompt);
                return `realtime_${type}_${this.hashString(keyInfo)}`;
            }

            const key = `${type}_${this.hashString(prompt)}_${JSON.stringify(options)}`;
            return key;
        }

        /**
         * 提取关键信息用于缓存
         * 🚀 性能优化：提取订单的关键信息，忽略时间戳等变化信息
         * @param {string} prompt - 原始提示词
         * @returns {string} 关键信息
         */
        extractKeyInfoForCache(prompt) {
            // 移除时间戳、订单号等变化信息，保留核心内容
            return prompt
                .replace(/\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/g, 'TIMESTAMP') // 移除时间戳
                .replace(/订单编号[：:]\s*\d+/g, 'ORDER_ID') // 移除订单号
                .replace(/支付时间[：:][^\n]+/g, 'PAYMENT_TIME') // 移除支付时间
                .trim();
        }

        /**
         * 获取缓存结果
         * 🚀 性能优化：支持不同的缓存超时策略
         * @param {string} cacheKey - 缓存键
         * @param {boolean} isRealtime - 是否为实时请求
         * @returns {object|null} 缓存结果
         */
        getCachedResult(cacheKey, isRealtime = false) {
            const cached = this.requestCache.get(cacheKey);
            if (cached) {
                // 🚀 性能优化：实时请求使用更短的缓存时间
                const timeout = isRealtime ? this.realtimeCacheTimeout : this.cacheTimeout;
                if (Date.now() - cached.timestamp < timeout) {
                    return cached.result;
                }
            }
            return null;
        }

        /**
         * 设置缓存结果
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 结果
         */
        setCachedResult(cacheKey, result) {
            this.requestCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });

            // 清理过期缓存
            this.cleanExpiredCache();
        }

        /**
         * 清理过期缓存
         */
        cleanExpiredCache() {
            const now = Date.now();
            for (const [key, cached] of this.requestCache.entries()) {
                if (now - cached.timestamp >= this.cacheTimeout) {
                    this.requestCache.delete(key);
                }
            }
        }

        /**
         * 字符串哈希
         * @param {string} str - 字符串
         * @returns {string} 哈希值
         */
        hashString(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString(36);
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取API状态
         * @returns {object} API状态信息
         */
        getAPIStatus() {
            return {
                model: this.config.modelVersion,
                cacheSize: this.requestCache.size,
                pendingRequests: this.pendingRequests.size,
                baseURL: this.baseURL,
                baseTimeout: this.config.baseTimeout,
                maxRetries: this.config.maxRetries,
                maxOutputTokens: this.config.maxOutputTokens,
                retryDelays: this.config.retryDelays,
                cacheTimeout: this.cacheTimeout,
                realtimeCacheTimeout: this.realtimeCacheTimeout
            };
        }

        /**
         * 🚀 新增：获取性能统计信息
         * @returns {object} 性能统计
         */
        getPerformanceStats() {
            const cacheHits = Array.from(this.requestCache.values()).length;
            const totalRequests = cacheHits; // 简化统计，实际应该跟踪更多指标
            
            return {
                cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests * 100).toFixed(2) + '%' : '0%',
                totalCachedResults: cacheHits,
                pendingRequestCount: this.pendingRequests.size,
                averageTimeout: `${this.config.baseTimeout}ms (+2s per retry)`,
                optimizedTokenLimit: this.config.maxOutputTokens,
                cacheStrategy: {
                    normal: `${this.cacheTimeout / 1000}s`,
                    realtime: `${this.realtimeCacheTimeout / 1000}s`
                }
            };
        }

        /**
         * 🚀 新增：清理缓存和重置统计
         */
        clearCacheAndStats() {
            this.requestCache.clear();
            this.pendingRequests.clear();
            this.logger.log('🚀 已清理所有缓存和待处理请求', 'info');
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const geminiCaller = new GeminiCaller();

    // 导出到全局作用域
    window.GeminiCaller = GeminiCaller;
    window.OTA.GeminiCaller = GeminiCaller;
    window.OTA.geminiCaller = geminiCaller;

    console.log('✅ GeminiCaller (子层实现) 已加载');

})();
