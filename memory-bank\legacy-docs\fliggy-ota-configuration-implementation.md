# Fliggy OTA配置实现文档

## 📋 概述

本文档详细说明了Fliggy OTA渠道配置的完整实现，包括渠道识别、价格处理和系统集成。实现遵循"做减法开发"原则，充分利用现有OTA系统架构。

## 🎯 实现目标

1. **渠道识别**: 自动检测包含"订单编号" + 19位数字模式的Fliggy订单
2. **价格处理**: 
   - 马来西亚订单: `price × 0.84 × 0.615`
   - 新加坡订单: `price × 0.84 × 0.2`
3. **系统集成**: 无缝整合到现有OTA处理流程中

## 🔧 技术实现

### 1. 渠道检测 (ota-channel-detector.js)

#### 1.1 参考号模式识别
```javascript
// 添加到 this.referencePatterns
fliggy: {
    pattern: /订单编号\d{19}/,
    channel: 'Fliggy',
    confidence: 0.95,
    description: 'Fliggy订单编号识别模式'
}
```

#### 1.2 内容特征检测
```javascript
// 新增专门的检测方法
detectFliggyFromContent(text) {
    const fliggyPattern = /订单编号\d{19}/g;
    const matches = text.match(fliggyPattern);
    
    if (matches && matches.length > 0) {
        return {
            detectedChannel: 'Fliggy',
            confidence: 0.95,
            details: [{
                type: 'fliggy_order_pattern',
                pattern: '订单编号+19位数字',
                matches: matches,
                description: 'Fliggy订单编号特征识别'
            }]
        };
    }
    return { detectedChannel: null, confidence: 0, details: [] };
}
```

### 2. 价格处理 (ota-customization-engine.js)

#### 2.1 Fliggy渠道配置
```javascript
'Fliggy': {
    geminiPromptTemplate: `
**Fliggy渠道专属处理规则：**
- 参考号格式：订单编号 + 19位数字
- 客户沟通风格：中文为主，注重服务体验
- 价格显示：支持CNY/MYR，根据地区自动转换
- 车型偏好：注重性价比和舒适性
- 特殊要求：马来西亚订单价格调整(×0.84×0.615)，新加坡订单价格调整(×0.84×0.2)`,
    
    priceCalculator: function(basePrice, orderData) {
        let finalPrice = basePrice;
        let adjustments = [];
        
        const orderContent = orderData.orderContent || orderData.notes || '';
        
        // 马来西亚订单价格调整
        if (orderContent.toLowerCase().includes('malaysia') || 
            orderContent.includes('马来西亚') || 
            orderContent.toLowerCase().includes('kuala lumpur') ||
            orderContent.includes('吉隆坡')) {
            finalPrice = basePrice * 0.84 * 0.615;
            adjustments.push({ 
                type: 'malaysia_pricing', 
                applied: true, 
                factor: 0.84 * 0.615,
                description: '马来西亚订单价格调整'
            });
        }
        // 新加坡订单价格调整
        else if (orderContent.toLowerCase().includes('singapore') || 
                 orderContent.includes('新加坡')) {
            finalPrice = basePrice * 0.84 * 0.2;
            adjustments.push({ 
                type: 'singapore_pricing', 
                applied: true, 
                factor: 0.84 * 0.2,
                description: '新加坡订单价格调整'
            });
        }
        
        // 团体订单优惠
        if (orderData.passenger_count >= 5) {
            finalPrice *= 0.97;
            adjustments.push({ 
                type: 'group_discount', 
                applied: true,
                factor: 0.97,
                description: '团体订单优惠'
            });
        }
        
        return {
            originalPrice: basePrice,
            finalPrice: Math.round(finalPrice * 100) / 100,
            currency: 'MYR',
            adjustments: adjustments
        };
    },
    
    vehicleMapping: {
        '5 Seater': '5座舒适轿车',
        '7 Seater MPV': '7座商务车',
        'Extended 5': '5座豪华轿车',
        'Velfire/Alphard': '豪华商务车'
    },
    
    fieldProcessing: {
        customer_name: (value) => value ? value.trim() : null,
        pickup_time: (value) => value,
        ota_reference_number: (value) => value ? value.trim() : null,
        languages_id_array: (value) => value && value.length > 0 ? value : [4] // 默认中文
    }
}
```

### 3. 渠道映射配置

Fliggy已在现有渠道映射中配置：
```javascript
// ota-channel-mapping.js 中的 commonChannels
{ value: 'Fliggy', text: 'Fliggy' }
```

## 🔄 工作流程

### 1. 渠道识别流程
```
输入订单内容
    ↓
检查是否包含"订单编号\d{19}"
    ↓
是 → 识别为Fliggy(置信度0.95)
    ↓
否 → 继续其他检测方法
```

### 2. 价格处理流程
```
获取基础价格
    ↓
分析订单内容
    ↓
检测地区(马来西亚/新加坡)
    ↓
应用相应价格调整
    ↓
检查团体优惠条件
    ↓
返回最终价格和调整详情
```

### 3. 综合处理流程
```
接收订单数据
    ↓
字段数据处理(Fliggy特定)
    ↓
车型推荐(基于乘客和行李数量)
    ↓
价格计算(如果提供基础价格)
    ↓
返回完整处理结果
```

## 🧪 测试验证

### 测试文件
- `test-fliggy-ota-config.html` - 完整的功能测试页面

### 测试用例

#### 1. 马来西亚订单测试
```
输入内容: "尊敬的客户，您的马来西亚接机服务已确认。订单编号1234567890123456789"
基础价格: 150 MYR
预期结果: 
- 检测渠道: Fliggy
- 最终价格: 150 × 0.84 × 0.615 = 77.49 MYR
```

#### 2. 新加坡订单测试
```
输入内容: "Singapore airport transfer confirmed. 订单编号9876543210987654321"
基础价格: 200 MYR
预期结果:
- 检测渠道: Fliggy
- 最终价格: 200 × 0.84 × 0.2 = 33.6 MYR
```

#### 3. 团体订单测试
```
输入内容: "Group transfer for 6 passengers. 订单编号1111222233334444555"
基础价格: 300 MYR
乘客数量: 6
预期结果:
- 检测渠道: Fliggy
- 团体优惠: 300 × 0.97 = 291 MYR
```

## 📊 集成点

### 1. 与现有系统的集成
- **渠道检测器**: 自动识别Fliggy订单特征
- **定制化引擎**: 应用Fliggy专属处理逻辑
- **渠道映射**: 已包含在通用渠道列表中
- **订单历史**: 支持Fliggy订单的复制和管理

### 2. 数据流整合
```
订单输入 → 渠道检测 → Fliggy配置应用 → 价格计算 → 结果输出
    ↓              ↓              ↓            ↓          ↓
现有UI    →  检测器增强  →  引擎配置扩展  →  计算逻辑  →  统一输出
```

## ⚠️ 注意事项

### 1. 识别准确性
- 确保"订单编号"后紧跟19位数字
- 避免误识别其他平台的相似格式

### 2. 价格计算
- 地区识别基于订单内容的关键词
- 默认情况下不应用地区调整(如果无法识别地区)

### 3. 扩展性
- 新的地区调整可以在priceCalculator函数中添加
- 车型映射可以根据需要调整

## 🔮 未来改进

1. **更精确的地区识别**: 基于更多地理标识词
2. **动态价格调整**: 支持可配置的调整因子
3. **多语言支持**: 扩展订单内容的语言识别能力
4. **性能优化**: 缓存常用的检测结果

## 📝 更新日志

- **2024-01-XX**: 初始实现Fliggy OTA配置
- **功能**: 渠道识别、价格处理、系统集成
- **测试**: 完整的功能测试覆盖
- **文档**: 技术实现和使用指南完成

---

*此实现严格遵循"做减法开发"原则，充分利用现有OTA系统架构，避免创建补丁代码或复杂化方案。*
