// 批量修复可选链操作符的脚本
const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
    'js/gemini-service.js',
    'js/multi-order-manager-v2.js'
];

// 修复函数
function fixOptionalChaining(content) {
    // 修复 this.logger?.log 模式
    content = content.replace(/this\.logger\?\./g, 'if (this.logger) this.logger.');
    
    // 修复 obj?.prop 模式
    content = content.replace(/(\w+)\?\./g, 'if ($1) $1.');
    
    // 修复 obj?.[key] 模式
    content = content.replace(/(\w+)\?\.\[([^\]]+)\]/g, '($1 && $1[$2])');
    
    // 修复复杂的链式调用
    content = content.replace(/data\.candidates\?\.\[0\]\?\.content\?\.parts\?\.\[0\]\?\.text/g, 
        'data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0] && data.candidates[0].content.parts[0].text');
    
    return content;
}

// 处理每个文件
filesToFix.forEach(filePath => {
    try {
        const fullPath = path.resolve(filePath);
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            const fixedContent = fixOptionalChaining(content);
            fs.writeFileSync(fullPath, fixedContent, 'utf8');
            console.log(`✅ 已修复: ${filePath}`);
        } else {
            console.log(`❌ 文件不存在: ${filePath}`);
        }
    } catch (error) {
        console.error(`❌ 修复失败 ${filePath}:`, error.message);
    }
});

console.log('🎉 批量修复完成！');
