/**
 * 网格布局系统
 * 包含三列布局、响应式网格等布局相关样式
 */

/* =================================
   三列布局系统
   ================================= */
.three-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  height: calc(100vh - 120px);
  padding: 8px;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.three-column-layout .column-mobile {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}

/* 通用列样式 */
.column-left,
.column-middle,
.column-right {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  min-width: 0;
  width: 100%;
}

/* =================================
   三列移动端布局
   ================================= */
.three-column-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 4px;
  height: calc(100vh - 70px);
  overflow: hidden;
  padding: 4px;
}

.column-mobile {
  overflow-y: auto;
  padding: 4px;
  border-radius: 8px;
  background: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

/* =================================
   操作区域布局
   ================================= */
.action-section {
  grid-column: 1 / -1; /* 跨越所有列 */
  margin-top: var(--spacing-2);
}

.grid-span-full {
  grid-column: 1 / -1;
}

/* =================================
   表单网格布局
   ================================= */
.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.service-config-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

/* =================================
   响应式断点优化
   ================================= */
@media (max-width: 1200px) {
  .three-column-layout {
    gap: 6px;
    padding: 6px;
  }
}

@media (max-width: 992px) {
  .three-column-layout {
    gap: 5px;
    padding: 5px;
  }
}

@media (max-width: 768px) {
  .three-column-layout {
    height: calc(100vh - 100px);
    gap: 4px;
    padding: 4px;
  }
  
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .service-config-container {
    gap: var(--spacing-2);
    padding: var(--spacing-2);
  }
}

@media (max-width: 480px) {
  .three-column-layout {
    gap: 3px;
    padding: 3px;
    height: calc(100vh - 90px);
  }
}