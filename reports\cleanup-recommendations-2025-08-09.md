# 整体整理建议（2025-08-09）

目标：在不引入打包器的前提下，确保启动确定性、职责清晰、依赖有序、体量可控。

## 1. 启动与加载（已完成基础改造）

- 使用 `core/script-manifest.js` + `core/script-loader.js` 作为唯一加载入口
- `main.js` 只负责调用 `ApplicationBootstrap.start()` 并暴露 `window.OTA.app`
- 建议：
  - 将每个 phase 的职责写到注释中（core/base-utils/ota-system/strategies/services/multi-order/ui-deps/ui）
  - 对可能并行的纯数据脚本建立并行组（后续验证后开启）

## 2. 功能开关治理（FeatureToggle）

- 在 `core/feature-toggle.js` 定义生产默认：关闭开发/诊断型模块
- 将以下模块接入开关并默认在生产禁用：
  - `core/duplicate-checker.js`
  - `core/auto-validation-runner.js`
  - `core/interface-compatibility-validator.js`
  - `core/architecture-guardian.js` / `core/development-standards-guardian.js`
  - `js/monitoring-wrapper.js`（如仅用于调试）
- 生产 manifest 不应包含上述脚本；调试场景由开发 manifest 或 URL 参数开启

## 3. 归档与瘦身

- 移至 `archive/` 或 `tests/`：
  - `js/managers/simple-ota-manager.js`
  - `js/simple-channel-detection.js`
  - `demo-simplified-ota.html`
  - 其他调试页面（位于 `tests/`）需标注用途并与生产隔离
- 在 `reports/` 更新索引，记录已归档项及恢复方式

## 4. 静态数据与资源

- 将 `hotel-data-inline.js`, `hotel-name-database.js` 拆分为 `/data/*.json`
- 新增轻量 data loader：支持 fetch 失败时使用内置兜底（小体量快照）
- 清理与数据相关的重复工具，建立单一异步获取入口

## 5. i18n 与语言管理

- 合并/明确 `language-manager.js` 与 `i18n.js` 的边界
  - 来源与持久化（localStorage/URL）由 language-manager 负责
  - 翻译表与格式化由 i18n 负责
- 输出单一 API：`getLocale() / setLocale()`、`t(key, params)`

## 6. 日志与诊断

- 继续保留 `service-locator` 的容错 fallback logger
- 统一 `logger.log(message, level, data)` 用法，避免隐式第二参为 data 的歧义
- 在关键阶段打点（phase 开始/结束、失败重试），暴露 `window.OTA.loaderStats`

## 7. Multi-order 与策略层

- 确认 `multi-order-*` 顺序：state -> detector -> transformer -> processor -> renderer
- 将渠道策略与 `ota-system/` 统一管理，策略命名 `*OTAStrategy` 与检测器输出一致
- 用 `tests/diagnostic-history-comprehensive.html` 等页面做最小验证闭环

## 8. Manifest 治理规范

- 约定：
  - 任何新脚本必须标注所属 phase 与上游依赖
  - `ui` phase 仅包含渲染与入口（`main.js` 最后）
  - 严禁模块在加载时产生副作用修改全局（除非在 `core`）
- 在 `reports/architecture_solution_summary.md` 追加“如何新增脚本”章节

## 9. 部署与可见性

- `index.html`：
  - 移除 inline 样式，增加无障碍标签
  - 确认只引入 manifest/loader 两个 script；其他由 loader 注入
- `netlify.toml` 与 `deployment/`：排除调试页面，保留健康检查

## 10. 工具与质量

- 引入 ESLint/Prettier（宽松规则），统一风格与简单陷阱检查
- 为易回归模块补最小单测（浏览器测试页即可）

## 11. 目录目标（建议）

- `js/core`：基础设施（容器、定位器、bootstrap、loader、feature-toggle、registry）
- `js/ota-system`：渠道检测、策略、集成（含 `config/`、`integrations/`）
- `js/managers`：页面或业务级管理器
- `js/multi-order`：多订单流水线（state/detector/transformer/processor/renderer）
- `js/adapters`：衔接与装饰器
- `js/services`：远程/本地服务与工具（可现有分散逐步汇总）
- `data/`：所有静态 JSON 数据
- `archive/`：历史与 demo
- `tests/`：调试与诊断页面/脚本

## 12. 风险与回滚

- 若某模块被 gating 或归档引发功能缺失：
  - 先在 manifest 临时恢复，再定位 FeatureToggle 条件
  - 提供 URL 参数 `?dev=1` 以临时启用调试模块

## 13. 成功指标

- 首次加载无错误；启动时间与加载阶段可见
- 生产包不包含 demo/调试模块
- Fliggy 端到端诊断通过；其它 OTA 可同法扩展
