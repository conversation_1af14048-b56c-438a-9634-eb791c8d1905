/**
 * 依赖标签（Dependency Tags）
 * 文件: js/multi-order/batch-processor.js
 * 角色: 批处理执行器（并发/节流/重试队列）
 * 上游依赖(直接使用):
 *  - Logger(getLogger)
 *  - ApiService（createOrder 带重试/退避）
 *  - StateManager（批次与条目状态推进）
 * 下游被依赖(常见调用方):
 *  - MultiOrderCoordinator（在大批量场景使用）
 * 事件: 进度/完成/失败回调；错误上报到Monitoring/Logger
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_BATCH_PROCESSOR 批量订单处理器
 * 🏷️ 标签: @OTA_BATCH_PROCESSOR
 * 📝 说明: 负责批量处理多个订单，包括并发控制和进度跟踪
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.BatchProcessor) {
    console.log('批量订单处理器已存在，跳过重复加载');
} else {

/**
 * 批量订单处理器类
 */
class BatchProcessor {
    constructor(config = {}) {
        this.config = {
            maxConcurrentRequests: 3, // 最大并发请求数
            requestDelay: 1000, // 请求间隔（毫秒）
            retryAttempts: 2, // 重试次数
            timeout: 30000, // 超时时间（毫秒）
            ...config
        };

        this.state = {
            isProcessing: false,
            currentBatch: null,
            progress: {
                total: 0,
                completed: 0,
                failed: 0,
                processing: 0
            },
            results: []
        };

        this.activeRequests = new Map();
        this.requestQueue = [];
        this.logger = this.getLogger();
    }

    /**
     * 处理批量订单
     * @param {array} orders - 订单列表
     * @param {object} options - 处理选项
     * @returns {Promise<object>} 处理结果
     */
    async processBatch(orders, options = {}) {
        if (this.state.isProcessing) {
            throw new Error('批量处理正在进行中，请等待完成');
        }

        try {
            this.initializeBatch(orders, options);
            
            this.logger.log('开始批量处理订单', 'info', {
                总数量: orders.length,
                最大并发: this.config.maxConcurrentRequests
            });

            // 创建处理队列
            const processingTasks = orders.map((order, index) => ({
                order,
                index,
                retryCount: 0
            }));

            // 并发处理
            const results = await this.processTasksConcurrently(processingTasks, options);

            // 汇总结果
            const summary = this.generateBatchSummary(results);
            
            this.logger.log('批量处理完成', 'info', summary);
            
            return {
                success: true,
                summary,
                results,
                totalTime: Date.now() - this.state.startTime
            };

        } catch (error) {
            this.logger.log('批量处理失败', 'error', { error: error.message });
            return {
                success: false,
                error: error.message,
                partialResults: this.state.results
            };
        } finally {
            this.cleanup();
        }
    }

    /**
     * 初始化批量处理
     * @param {array} orders - 订单列表
     * @param {object} options - 选项
     */
    initializeBatch(orders, options) {
        this.state.isProcessing = true;
        this.state.currentBatch = { orders, options };
        this.state.startTime = Date.now();
        this.state.progress = {
            total: orders.length,
            completed: 0,
            failed: 0,
            processing: 0
        };
        this.state.results = [];
        this.activeRequests.clear();
        this.requestQueue = [];
    }

    /**
     * 并发处理任务
     * @param {array} tasks - 任务列表
     * @param {object} options - 选项
     * @returns {Promise<array>} 处理结果
     */
    async processTasksConcurrently(tasks, options) {
        const results = new Array(tasks.length);
        const semaphore = new Semaphore(this.config.maxConcurrentRequests);
        
        const processingPromises = tasks.map(async (task, index) => {
            await semaphore.acquire();
            
            try {
                const result = await this.processTask(task, options);
                results[index] = result;
                return result;
            } finally {
                semaphore.release();
            }
        });

        await Promise.allSettled(processingPromises);
        return results;
    }

    /**
     * 处理单个任务
     * @param {object} task - 任务对象
     * @param {object} options - 选项
     * @returns {Promise<object>} 处理结果
     */
    async processTask(task, options) {
        const { order, index, retryCount } = task;
        
        try {
            this.updateProcessingState(index, 'processing');
            
            // 添加延迟以避免请求过于频繁
            if (index > 0) {
                await this.delay(this.config.requestDelay);
            }

            // 调用进度回调
            if (options.onProgress) {
                options.onProgress(this.getProgressInfo());
            }

            // 处理订单
            const result = await this.processOrder(order, index, options);
            
            this.updateProcessingState(index, 'completed');
            
            // 调用完成回调
            if (options.onOrderComplete) {
                options.onOrderComplete(index, result);
            }

            return {
                success: true,
                orderIndex: index,
                order,
                result,
                processingTime: Date.now() - this.state.startTime
            };

        } catch (error) {
            // 重试逻辑
            if (retryCount < this.config.retryAttempts) {
                this.logger.log(`订单 ${index + 1} 处理失败，准备重试 (${retryCount + 1}/${this.config.retryAttempts})`, 'warning');
                
                task.retryCount++;
                await this.delay(1000 * (retryCount + 1)); // 递增延迟
                return await this.processTask(task, options);
            }

            this.updateProcessingState(index, 'failed');
            
            // 调用错误回调
            if (options.onError) {
                options.onError(index, error);
            }

            return {
                success: false,
                orderIndex: index,
                order,
                error: error.message,
                retryCount
            };
        }
    }

    /**
     * 处理单个订单
     * @param {object} order - 订单数据
     * @param {number} index - 订单索引
     * @param {object} options - 选项
     * @returns {Promise<object>} 处理结果
     */
    async processOrder(order, index, options) {
        // 获取API服务
        const apiService = this.getApiService();
        if (!apiService) {
            throw new Error('API服务不可用');
        }

        // 准备订单数据
        const orderData = this.prepareOrderData(order, index);
        
        // 创建订单
        const response = await this.createOrderWithTimeout(apiService, orderData);
        
        this.logger.log(`订单 ${index + 1} 创建成功`, 'info', {
            订单ID: response.id,
            客户: orderData.customer_name
        });

        return {
            apiResponse: response,
            orderData,
            createdAt: new Date().toISOString()
        };
    }

    /**
     * 带超时的订单创建
     * @param {object} apiService - API服务
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} API响应
     */
    async createOrderWithTimeout(apiService, orderData) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('订单创建超时'));
            }, this.config.timeout);

            apiService.createOrder(orderData)
                .then(response => {
                    clearTimeout(timeoutId);
                    resolve(response);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    }

    /**
     * 准备订单数据
     * @param {object} order - 原始订单数据
     * @param {number} index - 订单索引
     * @returns {object} 处理后的订单数据
     */
    prepareOrderData(order, index) {
        // 基础数据清理
        const cleanedOrder = {
            ...order,
            // 添加批量处理标识
            _batchProcessing: {
                batchId: this.generateBatchId(),
                orderIndex: index,
                processedAt: new Date().toISOString()
            }
        };

        // 应用OTA渠道特定处理
        if (order._otaChannel && window.OTA?.customizationEngine) {
            const processed = window.OTA.customizationEngine.processOrder(order._otaChannel, cleanedOrder);
            if (processed.success) {
                return processed.processedData;
            }
        }

        return cleanedOrder;
    }

    /**
     * 更新处理状态
     * @param {number} index - 订单索引
     * @param {string} status - 状态
     */
    updateProcessingState(index, status) {
        if (status === 'processing') {
            this.state.progress.processing++;
        } else if (status === 'completed') {
            this.state.progress.processing--;
            this.state.progress.completed++;
        } else if (status === 'failed') {
            this.state.progress.processing--;
            this.state.progress.failed++;
        }
    }

    /**
     * 获取进度信息
     * @returns {object} 进度信息
     */
    getProgressInfo() {
        const { total, completed, failed, processing } = this.state.progress;
        return {
            total,
            completed,
            failed,
            processing,
            remaining: total - completed - failed,
            completionRate: Math.round((completed / total) * 100),
            failureRate: Math.round((failed / total) * 100),
            isComplete: completed + failed >= total
        };
    }

    /**
     * 生成批量处理汇总
     * @param {array} results - 处理结果
     * @returns {object} 汇总信息
     */
    generateBatchSummary(results) {
        const successful = results.filter(r => r && r.success);
        const failed = results.filter(r => r && !r.success);
        
        return {
            total: results.length,
            successful: successful.length,
            failed: failed.length,
            successRate: Math.round((successful.length / results.length) * 100),
            failureRate: Math.round((failed.length / results.length) * 100),
            errors: failed.map(f => ({
                orderIndex: f.orderIndex,
                error: f.error,
                retryCount: f.retryCount
            }))
        };
    }

    /**
     * 清理批量处理状态
     */
    cleanup() {
        this.state.isProcessing = false;
        this.state.currentBatch = null;
        this.activeRequests.clear();
        this.requestQueue = [];
    }

    /**
     * 取消批量处理
     */
    cancelBatch() {
        if (!this.state.isProcessing) {
            return false;
        }

        this.logger.log('取消批量处理', 'warning');
        
        // 取消所有活动请求
        this.activeRequests.forEach((request, id) => {
            if (request.abort) {
                request.abort();
            }
        });

        this.cleanup();
        return true;
    }

    /**
     * 生成批次ID
     * @returns {string} 批次ID
     */
    generateBatchId() {
        return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 延迟函数
     * @param {number} ms - 毫秒数
     * @returns {Promise<void>}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取API服务
     * @returns {object|null} API服务实例
     */
    getApiService() {
        if (window.OTA && window.OTA.apiService) {
            return window.OTA.apiService;
        }
        if (typeof getApiService === 'function') {
            return getApiService();
        }
        return null;
    }

    /**
     * 获取Logger实例
     * @returns {object} Logger实例
     */
    getLogger() {
        if (typeof getLogger === 'function') {
            return getLogger();
        }
        return {
            log: (message, level, data) => {
                console.log(`[BATCH][${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
            },
            logError: (message, error) => {
                console.error(`[BATCH][ERROR] ${message}`, error || '');
            },
            logUserAction: (action, details) => {
                console.log(`[BATCH][USER_ACTION] ${action}`, details || '');
            }
        };
    }
}

/**
 * 信号量实现，用于控制并发
 */
class Semaphore {
    constructor(permits) {
        this.permits = permits;
        this.waiting = [];
    }

    async acquire() {
        return new Promise((resolve) => {
            if (this.permits > 0) {
                this.permits--;
                resolve();
            } else {
                this.waiting.push(resolve);
            }
        });
    }

    release() {
        this.permits++;
        if (this.waiting.length > 0) {
            const resolve = this.waiting.shift();
            this.permits--;
            resolve();
        }
    }
}

// 创建全局实例
const batchProcessor = new BatchProcessor();

// 暴露到OTA命名空间
window.OTA = window.OTA || {};
window.OTA.BatchProcessor = BatchProcessor;
window.OTA.batchProcessor = batchProcessor;
window.OTA.Semaphore = Semaphore;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('batchProcessor', batchProcessor, '@OTA_BATCH_PROCESSOR');
}

}