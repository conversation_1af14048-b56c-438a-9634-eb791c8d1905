# 方案二：策略模式 + 工厂系统

## 🎯 核心理念

采用策略模式 + 抽象工厂模式，实现OTA渠道的完全解耦和独立切割。每个渠道作为独立的策略类，通过工厂模式统一管理。

## 📁 文件结构

```
js/ota-strategy/
├── core/
│   ├── ota-strategy-factory.js      # 策略工厂
│   ├── ota-base-strategy.js         # 策略基类
│   ├── ota-context-manager.js       # 上下文管理器
│   └── ota-isolation-guard.js       # 隔离守护器
├── strategies/
│   ├── fliggy-strategy.js           # 飞猪策略类
│   ├── ctrip-strategy.js            # 携程策略类
│   ├── klook-strategy.js            # Klook策略类
│   ├── chong-dealer-strategy.js     # Chong Dealer策略类
│   └── kkday-strategy.js            # KKday策略类
├── detectors/
│   ├── fliggy-detector.js           # 飞猪专属检测器
│   ├── ctrip-detector.js            # 携程专属检测器
│   └── common-detector.js           # 通用检测器
└── processors/
    ├── fliggy-processor.js          # 飞猪专属处理器
    ├── ctrip-processor.js           # 携程专属处理器
    └── default-processor.js         # 默认处理器
```

## 🏗️ 架构设计

### 策略基类
```javascript
// js/ota-strategy/core/ota-base-strategy.js
class OTABaseStrategy {
    constructor(channelName) {
        this.channelName = channelName;
        this.isActive = false;
        this.processingContext = null;
    }
    
    // 抽象方法 - 子类必须实现
    async detect(orderData) {
        throw new Error('detect方法必须由子类实现');
    }
    
    async process(orderData) {
        throw new Error('process方法必须由子类实现');
    }
    
    async validateConfig() {
        throw new Error('validateConfig方法必须由子类实现');
    }
    
    // 策略激活控制
    activate(context) {
        if (this.isActive) {
            throw new Error(`${this.channelName}策略已激活，不能重复激活`);
        }
        this.isActive = true;
        this.processingContext = context;
        console.log(`✅ ${this.channelName}策略已激活`);
    }
    
    deactivate() {
        this.isActive = false;
        this.processingContext = null;
        console.log(`🔽 ${this.channelName}策略已停用`);
    }
    
    // 隔离验证
    validateIsolation() {
        if (!this.isActive) {
            throw new Error(`${this.channelName}策略未激活，拒绝访问`);
        }
        if (!this.processingContext) {
            throw new Error(`${this.channelName}策略缺少处理上下文`);
        }
    }
}
```

### 飞猪专属策略类
```javascript
// js/ota-strategy/strategies/fliggy-strategy.js
class FliggyStrategy extends OTABaseStrategy {
    constructor() {
        super('Fliggy');
        this.exclusiveFeatures = new Set(['addressTranslation', 'chineseCustomerName']);
        this.isolationLevel = 'STRICT'; // 严格隔离
    }
    
    async detect(orderData) {
        this.validateIsolation();
        
        const detectionRules = [
            // 飞猪专属检测规则
            {
                type: 'referencePattern',
                pattern: /订单编号\d{19}/,
                confidence: 0.95,
                exclusive: true // 飞猪独有
            },
            {
                type: 'keyword',
                keywords: ['fliggy', '飞猪', '阿里旅行'],
                confidence: 0.9,
                exclusive: true
            },
            {
                type: 'customerFormat',
                pattern: /^[\u4e00-\u9fa5]{2,4}$/,
                confidence: 0.8,
                exclusive: false
            }
        ];
        
        let totalConfidence = 0;
        const matchedRules = [];
        
        for (const rule of detectionRules) {
            const match = await this._evaluateRule(rule, orderData);
            if (match.matched) {
                totalConfidence += rule.confidence * match.strength;
                matchedRules.push(match);
            }
        }
        
        return {
            channel: 'Fliggy',
            confidence: Math.min(totalConfidence, 1.0),
            matched: totalConfidence > 0.7,
            rules: matchedRules,
            exclusive: true,
            isolationLevel: this.isolationLevel
        };
    }
    
    async process(orderData) {
        this.validateIsolation();
        
        // 飞猪专属处理流程
        const processedData = { ...orderData };
        
        // 1. 客户姓名中文处理
        if (processedData.customer_name) {
            processedData.customer_name = await this._processChineseCustomerName(
                processedData.customer_name
            );
        }
        
        // 2. 地址翻译服务（飞猪独有）
        if (processedData.pickup_address) {
            processedData.pickup_address = await this._translateChineseAddress(
                processedData.pickup_address
            );
        }
        
        // 3. 飞猪专属定价逻辑
        if (processedData.base_price) {
            processedData.price_info = await this._calculateFliggyPrice(
                processedData.base_price, 
                processedData
            );
        }
        
        // 4. 数据格式标准化
        processedData._processed_by = 'FliggyStrategy';
        processedData._processing_timestamp = Date.now();
        processedData._isolation_level = this.isolationLevel;
        
        return processedData;
    }
    
    async validateConfig() {
        const requiredServices = ['addressTranslationService', 'chineseNameProcessor'];
        const missingServices = [];
        
        for (const service of requiredServices) {
            if (!window.OTA[service]) {
                missingServices.push(service);
            }
        }
        
        if (missingServices.length > 0) {
            throw new Error(`飞猪策略缺少必需服务: ${missingServices.join(', ')}`);
        }
        
        return {
            valid: true,
            channel: 'Fliggy',
            services: requiredServices,
            isolationLevel: this.isolationLevel
        };
    }
    
    // 🔒 飞猪专属功能 - 严格隔离
    async _translateChineseAddress(address) {
        if (!this.exclusiveFeatures.has('addressTranslation')) {
            throw new Error('地址翻译功能仅限飞猪渠道使用');
        }
        
        // 调用地址翻译服务
        if (window.OTA.addressTranslationService) {
            return await window.OTA.addressTranslationService.translate(address);
        }
        
        return address; // 降级处理
    }
    
    async _processChineseCustomerName(name) {
        if (!this.exclusiveFeatures.has('chineseCustomerName')) {
            throw new Error('中文客户名处理功能仅限飞猪渠道使用');
        }
        
        // 中文姓名标准化处理
        return name.trim().replace(/\s+/g, '');
    }
    
    async _calculateFliggyPrice(basePrice, orderData) {
        let finalPrice = basePrice;
        
        // 飞猪专属定价规则
        if (orderData.is_vip_customer) {
            finalPrice *= 0.95; // VIP客户5%折扣
        }
        
        if (orderData.payment_method === 'alipay') {
            finalPrice *= 0.98; // 支付宝支付2%优惠
        }
        
        return {
            originalPrice: basePrice,
            finalPrice: Math.round(finalPrice * 100) / 100,
            currency: 'CNY',
            discounts: [
                { type: 'vip_discount', applied: orderData.is_vip_customer },
                { type: 'alipay_discount', applied: orderData.payment_method === 'alipay' }
            ]
        };
    }
    
    async _evaluateRule(rule, orderData) {
        // 规则评估逻辑
        switch (rule.type) {
            case 'referencePattern':
                const refMatch = rule.pattern.test(orderData.raw_text || '');
                return { matched: refMatch, strength: refMatch ? 1.0 : 0.0, rule: rule.type };
                
            case 'keyword':
                const text = (orderData.raw_text || '').toLowerCase();
                const keywordMatch = rule.keywords.some(keyword => text.includes(keyword));
                return { matched: keywordMatch, strength: keywordMatch ? 1.0 : 0.0, rule: rule.type };
                
            case 'customerFormat':
                const nameMatch = rule.pattern.test(orderData.customer_name || '');
                return { matched: nameMatch, strength: nameMatch ? 0.8 : 0.0, rule: rule.type };
                
            default:
                return { matched: false, strength: 0.0, rule: 'unknown' };
        }
    }
}
```

### 策略工厂
```javascript
// js/ota-strategy/core/ota-strategy-factory.js
class OTAStrategyFactory {
    constructor() {
        this.strategies = new Map();
        this.activeStrategy = null;
        this.isolationMode = true;
    }
    
    // 注册策略
    registerStrategy(channelName, strategyClass) {
        if (this.strategies.has(channelName)) {
            console.warn(`策略 ${channelName} 已存在，将被覆盖`);
        }
        
        this.strategies.set(channelName, strategyClass);
        console.log(`📝 已注册策略: ${channelName}`);
    }
    
    // 创建并激活策略（独占模式）
    async createStrategy(channelName, context = {}) {
        // 严格隔离检查
        if (this.isolationMode && this.activeStrategy) {
            throw new Error(`隔离模式下已有活跃策略: ${this.activeStrategy.channelName}`);
        }
        
        const StrategyClass = this.strategies.get(channelName);
        if (!StrategyClass) {
            throw new Error(`未找到渠道 ${channelName} 的策略类`);
        }
        
        // 创建策略实例
        const strategy = new StrategyClass();
        
        // 验证配置
        await strategy.validateConfig();
        
        // 激活策略
        strategy.activate(context);
        this.activeStrategy = strategy;
        
        console.log(`🚀 已创建并激活策略: ${channelName}`);
        return strategy;
    }
    
    // 停用当前策略
    deactivateCurrentStrategy() {
        if (this.activeStrategy) {
            this.activeStrategy.deactivate();
            console.log(`🔽 已停用策略: ${this.activeStrategy.channelName}`);
            this.activeStrategy = null;
        }
    }
    
    // 获取当前活跃策略
    getCurrentStrategy() {
        if (!this.activeStrategy) {
            throw new Error('当前没有活跃的策略');
        }
        return this.activeStrategy;
    }
    
    // 列出所有可用策略
    listStrategies() {
        return Array.from(this.strategies.keys());
    }
}
```

### 隔离守护器
```javascript
// js/ota-strategy/core/ota-isolation-guard.js
class OTAIsolationGuard {
    constructor() {
        this.accessLog = [];
        this.violations = [];
        this.strictMode = true;
    }
    
    // 验证渠道访问权限
    validateChannelAccess(requestedChannel, currentChannel, feature) {
        const access = {
            timestamp: Date.now(),
            requestedChannel,
            currentChannel,
            feature,
            allowed: false,
            reason: ''
        };
        
        // 严格隔离检查
        if (this.strictMode && currentChannel && requestedChannel !== currentChannel) {
            access.reason = `严格隔离模式下不允许跨渠道访问: ${currentChannel} -> ${requestedChannel}`;
            this.violations.push(access);
            throw new Error(access.reason);
        }
        
        // 特殊功能隔离检查
        const exclusiveFeatures = {
            'Fliggy': ['addressTranslation', 'chineseCustomerName'],
            'Ctrip': ['chineseService', 'currencyConversion'],
            'Klook': ['internationalPayment', 'multiLanguage']
        };
        
        if (exclusiveFeatures[requestedChannel]?.includes(feature)) {
            if (currentChannel !== requestedChannel) {
                access.reason = `功能 ${feature} 仅限 ${requestedChannel} 渠道使用`;
                this.violations.push(access);
                throw new Error(access.reason);
            }
        }
        
        access.allowed = true;
        access.reason = '访问权限验证通过';
        this.accessLog.push(access);
        
        return access;
    }
    
    // 获取违规记录
    getViolations() {
        return this.violations;
    }
    
    // 清理记录
    clearLogs() {
        this.accessLog = [];
        this.violations = [];
    }
}
```

## ⚖️ 优势

- ✅ **完美隔离**: 策略模式确保各渠道逻辑完全独立
- ✅ **易于扩展**: 新增渠道只需实现策略接口
- ✅ **类型安全**: 基类定义清晰的接口契约
- ✅ **运行时安全**: 隔离守护器防止意外的跨渠道访问
- ✅ **可测试性**: 每个策略都可以独立单元测试

## ⚠️ 劣势

- ❌ **复杂度高**: 需要理解策略模式和工厂模式
- ❌ **初期工作量大**: 需要重构现有代码
- ❌ **性能开销**: 策略切换和验证有一定开销

## 🚀 实施步骤

1. **创建基础框架**: 实现策略基类和工厂
2. **实现飞猪策略**: 完整的飞猪独立策略类
3. **添加隔离守护器**: 确保严格的渠道隔离
4. **迁移其他渠道**: 逐步将其他渠道改造为策略类
5. **替换调用代码**: 更新现有代码使用策略工厂
6. **清理旧代码**: 移除分散的配置文件

## 🔒 隔离保证

- **编译时隔离**: 通过类型系统确保接口一致性
- **运行时隔离**: 通过守护器检查访问权限
- **功能隔离**: 专属功能只能在对应渠道中访问
- **上下文隔离**: 每个策略维护独立的处理上下文

这种方案特别适合需要严格隔离和高度可控的场景，确保各个OTA渠道之间零干扰。
