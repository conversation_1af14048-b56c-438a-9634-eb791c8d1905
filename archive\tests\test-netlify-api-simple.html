<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netlify Functions API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            background-color: #3498db;
            color: white;
        }
        .btn:hover { opacity: 0.8; }
        
        .flight-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .flight-example {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
        }
        .flight-example:hover {
            background-color: #2980b9;
        }
        
        #testLog {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .api-endpoint {
            background-color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛫 Netlify Functions API 测试</h1>
        
        <div class="api-endpoint">
            GET /api/flight-info?flight={flightNumber}
        </div>
        
        <p>测试Netlify Functions是否正确代理FlightAware API调用</p>
        
        <div class="flight-examples">
            <div class="flight-example" onclick="testFlight('MH370')">MH370</div>
            <div class="flight-example" onclick="testFlight('AK6285')">AK6285</div>
            <div class="flight-example" onclick="testFlight('CZ3021')">CZ3021</div>
            <div class="flight-example" onclick="testFlight('9W123')">9W123</div>
            <div class="flight-example" onclick="testFlight('SQ001')">SQ001</div>
            <div class="flight-example" onclick="testFlight('INVALID')">INVALID</div>
        </div>
        
        <input type="text" id="customFlightInput" class="test-input" placeholder="输入自定义航班号进行测试...">
        <button class="btn" onclick="testCustomFlight()">测试自定义航班号</button>
        <button class="btn" onclick="clearLog()">清空日志</button>
        
        <div id="testLog">等待测试开始...\n</div>
    </div>

    <script>
        // 测试日志管理
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logElement.textContent += `[${timestamp}] ${statusIcon} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
            log('测试日志已清空');
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            log('🚀 Netlify Functions API 测试页面已加载');
            
            // 检查当前环境
            const isNetlifyEnv = window.location.hostname.includes('netlify.app') || 
                               window.location.hostname.includes('netlify.com');
            
            if (isNetlifyEnv) {
                log('🌐 检测到Netlify生产环境');
            } else {
                log('🏠 检测到本地开发环境');
                log('⚠️ 在本地环境中，API调用将失败（这是正常的）', 'warning');
            }
            
            log('🎯 可以开始测试API端点');
        });

        // 测试航班信息API
        async function testFlight(flightNumber) {
            log(`🔍 测试航班: ${flightNumber}`);
            
            try {
                const startTime = Date.now();
                const response = await fetch(`/api/flight-info?flight=${encodeURIComponent(flightNumber)}`);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                log(`⏱️ 响应时间: ${responseTime}ms`);
                
                if (!response.ok) {
                    const errorText = await response.text().catch(() => 'Unknown error');
                    log(`❌ HTTP错误: ${response.status} ${response.statusText}`, 'error');
                    log(`错误内容: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API调用成功: ${flightNumber}`, 'success');
                    log(`航班数据: ${JSON.stringify(data.data, null, 2)}`);
                } else {
                    log(`⚠️ API返回错误: ${data.message}`, 'warning');
                    log(`错误代码: ${data.error}`);
                }
                
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
                
                if (error.message.includes('fetch')) {
                    log('💡 这可能是因为：', 'info');
                    log('   1. 在本地环境中运行（Netlify Functions不可用）');
                    log('   2. 网络连接问题');
                    log('   3. Netlify Functions未正确部署');
                }
            }
        }

        // 测试自定义航班号
        function testCustomFlight() {
            const flightNumber = document.getElementById('customFlightInput').value.trim();
            if (!flightNumber) {
                log('⚠️ 请输入航班号', 'warning');
                return;
            }
            testFlight(flightNumber);
        }

        // 测试多个航班
        async function testMultipleFlights() {
            log('🧪 开始批量测试...');
            const flights = ['MH370', 'AK6285', 'CZ3021', 'SQ001'];
            
            for (const flight of flights) {
                await testFlight(flight);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒间隔
            }
            
            log('🎯 批量测试完成', 'success');
        }

        // 键盘事件处理
        document.getElementById('customFlightInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testCustomFlight();
            }
        });
    </script>
</body>
</html>
