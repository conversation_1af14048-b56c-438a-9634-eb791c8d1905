<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言智能识别测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .test-input {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 语言智能识别测试</h1>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button onclick="testGeminiLanguageDetection()">🧠 测试Gemini语言检测</button>
            <button onclick="testFormManagerDetection()">📝 测试表单管理器检测</button>
            <button onclick="testApiServiceDetection()">🚀 测试API服务检测</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div class="test-section">
            <h2>🧠 Gemini服务语言检测</h2>
            <div id="geminiTests"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 表单管理器语言检测</h2>
            <div id="formManagerTests"></div>
        </div>
        
        <div class="test-section">
            <h2>🚀 API服务语言检测</h2>
            <div id="apiServiceTests"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/gemini-service.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/managers/form-manager.js"></script>
    
    <script>
        // 测试用例数据
        const testCases = [
            {
                customerName: '张三',
                extraRequirement: '需要中文司机',
                expected: [4], // 中文
                description: '纯中文客户'
            },
            {
                customerName: 'John Smith',
                extraRequirement: 'English speaking driver please',
                expected: [2], // 英文
                description: '纯英文客户'
            },
            {
                customerName: '李明',
                extraRequirement: 'Need English driver',
                expected: [4], // 中英混合时返回中文
                description: '中英混合内容'
            },
            {
                customerName: 'Mary Wang',
                extraRequirement: '请安排会说中文的司机',
                expected: [4], // 中英混合时返回中文
                description: '英文姓名+中文要求'
            },
            {
                customerName: '',
                extraRequirement: '',
                expected: [2], // 默认英文
                description: '空内容默认'
            }
        ];

        // 测试Gemini语言检测
        function testGeminiLanguageDetection() {
            const container = document.getElementById('geminiTests');
            container.innerHTML = '';
            
            if (!window.OTA || !window.OTA.geminiService) {
                const errorCase = document.createElement('div');
                errorCase.className = 'test-case';
                errorCase.innerHTML = `
                    <div class="test-input">Gemini服务检测</div>
                    <div class="test-result error">❌ Gemini服务未找到</div>
                `;
                container.appendChild(errorCase);
                return;
            }
            
            testCases.forEach((testCase, index) => {
                try {
                    const combinedText = `${testCase.extraRequirement} ${testCase.customerName}`;
                    const result = window.OTA.geminiService.getLanguagesIdArray(
                        testCase.extraRequirement,
                        testCase.customerName
                    );
                    
                    const matches = JSON.stringify(result.sort()) === JSON.stringify(testCase.expected.sort());
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    
                    testDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result ${matches ? 'success' : 'error'}">
                            ${matches ? '✅' : '❌'} 检测结果: ${matches ? '正确' : '不匹配'}
                            <br>客户姓名: "${testCase.customerName}"
                            <br>额外要求: "${testCase.extraRequirement}"
                            <br>期望语言: [${testCase.expected.join(', ')}]
                            <br>实际结果: [${result.join(', ')}]
                        </div>
                    `;
                    
                    container.appendChild(testDiv);
                    
                } catch (error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'test-case';
                    errorDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result error">❌ 测试异常: ${error.message}</div>
                    `;
                    container.appendChild(errorDiv);
                }
            });
        }

        // 测试表单管理器检测
        function testFormManagerDetection() {
            const container = document.getElementById('formManagerTests');
            container.innerHTML = '';
            
            // 创建模拟的表单元素
            const mockElement = document.createElement('select');
            mockElement.multiple = true;
            
            // 添加语言选项
            const languages = [
                { id: 2, name: 'English' },
                { id: 3, name: 'Malay' },
                { id: 4, name: 'Chinese' }
            ];
            
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.id;
                option.textContent = lang.name;
                mockElement.appendChild(option);
            });
            
            testCases.forEach((testCase, index) => {
                try {
                    // 模拟表单管理器的fillLanguageMultiSelect方法
                    const mockData = {
                        customer_name: testCase.customerName,
                        extra_requirement: testCase.extraRequirement,
                        remark: testCase.extraRequirement
                    };
                    
                    // 重置选择
                    Array.from(mockElement.options).forEach(option => option.selected = false);
                    
                    // 执行智能检测逻辑 - 使用统一语言检测器
                    let languageIds = [];
                    if (window.OTA && window.OTA.unifiedLanguageDetector) {
                        const combinedText = `${mockData.remark || ''} ${mockData.extra_requirement || ''} ${mockData.customer_name || ''}`;
                        const hasChinese = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/.test(combinedText);
                        languageIds = hasChinese ? [4] : [2]; // 中文[4] 或 英文[2]
                    }
                    
                    // 如果没有检测到，使用默认
                    if (languageIds.length === 0) {
                        languageIds = [2]; // 默认英文
                    }
                    
                    // 应用选择
                    languageIds.forEach(langId => {
                        const option = Array.from(mockElement.options).find(opt => parseInt(opt.value) === langId);
                        if (option) {
                            option.selected = true;
                        }
                    });
                    
                    const matches = JSON.stringify(languageIds.sort()) === JSON.stringify(testCase.expected.sort());
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    
                    testDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result ${matches ? 'success' : 'error'}">
                            ${matches ? '✅' : '❌'} 表单检测结果: ${matches ? '正确' : '不匹配'}
                            <br>检测到的语言: [${languageIds.join(', ')}]
                            <br>期望语言: [${testCase.expected.join(', ')}]
                        </div>
                    `;
                    
                    container.appendChild(testDiv);
                    
                } catch (error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'test-case';
                    errorDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result error">❌ 测试异常: ${error.message}</div>
                    `;
                    container.appendChild(errorDiv);
                }
            });
        }

        // 测试API服务检测
        function testApiServiceDetection() {
            const container = document.getElementById('apiServiceTests');
            container.innerHTML = '';
            
            if (!window.getApiService) {
                const errorCase = document.createElement('div');
                errorCase.className = 'test-case';
                errorCase.innerHTML = `
                    <div class="test-input">API服务检测</div>
                    <div class="test-result error">❌ API服务未找到</div>
                `;
                container.appendChild(errorCase);
                return;
            }
            
            testCases.forEach((testCase, index) => {
                try {
                    const apiService = window.getApiService();
                    const result = apiService.getDefaultLanguagesArray(testCase.customerName);
                    
                    // API服务返回对象格式，需要转换为数组进行比较
                    const resultArray = Object.values(result).map(id => parseInt(id));
                    
                    // API服务只基于客户姓名检测，逻辑较简单
                    const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
                    const expectedForApi = chineseRegex.test(testCase.customerName) ? [4] : [2];
                    
                    const matches = JSON.stringify(resultArray.sort()) === JSON.stringify(expectedForApi.sort());
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    
                    testDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result ${matches ? 'success' : 'warning'}">
                            ${matches ? '✅' : '⚠️'} API检测结果: ${matches ? '正确' : '简化逻辑'}
                            <br>客户姓名: "${testCase.customerName}"
                            <br>API返回: [${resultArray.join(', ')}]
                            <br>API期望: [${expectedForApi.join(', ')}]
                            <br>注: API服务只基于姓名检测，不考虑额外要求
                        </div>
                    `;
                    
                    container.appendChild(testDiv);
                    
                } catch (error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'test-case';
                    errorDiv.innerHTML = `
                        <div class="test-input">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-result error">❌ 测试异常: ${error.message}</div>
                    `;
                    container.appendChild(errorDiv);
                }
            });
        }

        // 清除结果
        function clearResults() {
            document.getElementById('geminiTests').innerHTML = '';
            document.getElementById('formManagerTests').innerHTML = '';
            document.getElementById('apiServiceTests').innerHTML = '';
        }

        // 页面加载时自动运行测试
        window.addEventListener('DOMContentLoaded', function() {
            console.log('语言智能识别测试页面已加载');
        });
    </script>
</body>
</html>
