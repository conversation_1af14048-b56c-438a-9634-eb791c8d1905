<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三个核心功能修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover { background-color: #2980b9; }
        .btn.success { background-color: #27ae60; }
        .btn.warning { background-color: #f39c12; }
        .btn.danger { background-color: #e74c3c; }
        .result-area {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            font-size: 12px;
        }
        .input-area {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 15px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-info { background-color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 三个核心功能修复测试</h1>
        <p>测试和修复：语言智能选择、渠道特征检测、自动分析功能</p>

        <div class="test-section">
            <h3>📝 测试输入区域</h3>
            <textarea id="testInput" class="input-area" placeholder="输入订单内容进行测试...">飞猪订单：从上海浦东机场到市中心酒店，客人姓名：张三，航班号：CA123，联系电话：13800138000</textarea>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn success" onclick="manualFix()">手动修复</button>
            <button class="btn warning" onclick="checkStatus()">检查状态</button>
        </div>

        <div class="test-section">
            <h3>1️⃣ 简易语言智能选择</h3>
            <button class="btn" onclick="testLanguageDetection()">测试语言检测</button>
            <button class="btn warning" onclick="fixLanguageDetection()">修复语言检测</button>
            <div id="languageResult" class="result-area"></div>
        </div>

        <div class="test-section">
            <h3>2️⃣ 渠道特征检测</h3>
            <button class="btn" onclick="testChannelDetection()">测试渠道检测</button>
            <button class="btn warning" onclick="fixChannelDetection()">修复渠道检测</button>
            <div id="channelResult" class="result-area"></div>
        </div>

        <div class="test-section">
            <h3>3️⃣ 自动分析功能</h3>
            <button class="btn" onclick="testAutoAnalysis()">测试自动分析</button>
            <button class="btn warning" onclick="fixAutoAnalysis()">修复自动分析</button>
            <div id="analysisResult" class="result-area"></div>
        </div>

        <div class="test-section">
            <h3>🔄 综合测试</h3>
            <button class="btn success" onclick="testInputEvent()">测试输入事件</button>
            <button class="btn danger" onclick="forceReinitialize()">强制重新初始化</button>
            <div id="overallResult" class="result-area"></div>
        </div>
    </div>

    <!-- 使用新的脚本加载器 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${type}`;
            element.innerHTML += `<span class="status-indicator ${statusClass}"></span>[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 1. 测试语言检测功能
        async function testLanguageDetection() {
            clearLog('languageResult');
            log('languageResult', '测试语言检测功能...', 'info');
            
            const testText = document.getElementById('testInput').value;
            const unifiedDetector = window.OTA?.unifiedLanguageDetector;
            
            if (unifiedDetector) {
                log('languageResult', '✅ 统一语言检测器存在', 'success');
                log('languageResult', `初始化状态: ${unifiedDetector.initialized ? '✅ 已初始化' : '❌ 未初始化'}`, 
                    unifiedDetector.initialized ? 'success' : 'warning');
                
                if (unifiedDetector.initialized) {
                    try {
                        const result = await unifiedDetector.detectAndApply(testText, 'testInput');
                        log('languageResult', `语言检测结果: ${result ? '✅ 成功' : '❌ 失败'}`, result ? 'success' : 'error');
                    } catch (error) {
                        log('languageResult', `语言检测错误: ${error.message}`, 'error');
                    }
                } else {
                    log('languageResult', '⚠️ 检测器未初始化', 'warning');
                }
            } else {
                log('languageResult', '❌ 统一语言检测器不存在', 'error');
            }
        }

        async function fixLanguageDetection() {
            clearLog('languageResult');
            log('languageResult', '修复语言检测功能...', 'info');
            
            if (!window.OTA?.unifiedLanguageDetector && window.OTA?.UnifiedLanguageDetector) {
                log('languageResult', '🔧 创建新的语言检测器实例...', 'info');
                const detector = new window.OTA.UnifiedLanguageDetector();
                const success = await detector.init();
                log('languageResult', `初始化结果: ${success ? '✅ 成功' : '❌ 失败'}`, success ? 'success' : 'error');
            } else if (window.OTA?.unifiedLanguageDetector && !window.OTA.unifiedLanguageDetector.initialized) {
                log('languageResult', '🔧 重新初始化现有检测器...', 'info');
                const success = await window.OTA.unifiedLanguageDetector.init();
                log('languageResult', `重新初始化结果: ${success ? '✅ 成功' : '❌ 失败'}`, success ? 'success' : 'error');
            } else {
                log('languageResult', '✅ 语言检测器状态正常', 'success');
            }
        }

        // 2. 测试渠道检测功能
        async function testChannelDetection() {
            clearLog('channelResult');
            log('channelResult', '测试渠道检测功能...', 'info');
            
            const testText = document.getElementById('testInput').value;
            const channelDetector = window.OTA?.channelDetector;
            
            if (channelDetector) {
                log('channelResult', '✅ 渠道检测器存在', 'success');
                
                try {
                    const result = await channelDetector.detectChannel(testText);
                    log('channelResult', `渠道检测结果: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.channel) {
                        log('channelResult', `✅ 检测到渠道: ${result.channel} (置信度: ${result.confidence})`, 'success');
                    } else {
                        log('channelResult', '⚠️ 未检测到特定渠道', 'warning');
                    }
                } catch (error) {
                    log('channelResult', `渠道检测错误: ${error.message}`, 'error');
                }
            } else {
                log('channelResult', '❌ 渠道检测器不存在', 'error');
            }
        }

        async function fixChannelDetection() {
            clearLog('channelResult');
            log('channelResult', '修复渠道检测功能...', 'info');
            
            if (!window.OTA?.channelDetector) {
                log('channelResult', '❌ 渠道检测器不存在，无法修复', 'error');
                log('channelResult', '请检查 js/flow/channel-detector.js 是否正确加载', 'warning');
            } else {
                log('channelResult', '✅ 渠道检测器存在，功能正常', 'success');
            }
        }

        // 3. 测试自动分析功能
        async function testAutoAnalysis() {
            clearLog('analysisResult');
            log('analysisResult', '测试自动分析功能...', 'info');
            
            const geminiService = window.getGeminiService?.();
            if (geminiService) {
                log('analysisResult', '✅ Gemini服务存在', 'success');
                log('analysisResult', `可用性: ${geminiService.isAvailable?.() ? '✅ 可用' : '❌ 不可用'}`, 
                    geminiService.isAvailable?.() ? 'success' : 'error');
                
                const realtimeManager = window.OTA?.uiManager?.managers?.realtimeAnalysis;
                log('analysisResult', `实时分析管理器: ${realtimeManager ? '✅ 存在' : '❌ 不存在'}`, 
                    realtimeManager ? 'success' : 'error');
                
                if (realtimeManager) {
                    log('analysisResult', `事件处理方法: ${typeof realtimeManager.handleRealtimeInput === 'function' ? '✅ 可用' : '❌ 不可用'}`, 
                        typeof realtimeManager.handleRealtimeInput === 'function' ? 'success' : 'error');
                }
            } else {
                log('analysisResult', '❌ Gemini服务不存在', 'error');
            }
        }

        async function fixAutoAnalysis() {
            clearLog('analysisResult');
            log('analysisResult', '修复自动分析功能...', 'info');
            
            const realtimeManager = window.OTA?.uiManager?.managers?.realtimeAnalysis;
            if (realtimeManager) {
                log('analysisResult', '🔧 重新初始化实时分析管理器...', 'info');
                try {
                    realtimeManager.init();
                    log('analysisResult', '✅ 实时分析管理器重新初始化成功', 'success');
                } catch (error) {
                    log('analysisResult', `重新初始化失败: ${error.message}`, 'error');
                }
            } else {
                log('analysisResult', '❌ 实时分析管理器不存在，无法修复', 'error');
            }
        }

        // 综合测试
        async function testInputEvent() {
            clearLog('overallResult');
            log('overallResult', '测试输入事件触发...', 'info');
            
            const testInput = document.getElementById('testInput');
            const testText = testInput.value;
            
            // 模拟在主页面的orderInput上输入
            const orderInput = document.getElementById('orderInput');
            if (orderInput) {
                orderInput.value = testText;
                
                // 触发input事件
                const inputEvent = new Event('input', { bubbles: true });
                orderInput.dispatchEvent(inputEvent);
                
                log('overallResult', '✅ input事件已触发到orderInput', 'success');
                log('overallResult', `输入内容: ${testText}`, 'info');
                
                // 等待并检查结果
                setTimeout(() => {
                    const languageSelect = document.getElementById('language');
                    const otaSelect = document.getElementById('ota');
                    
                    log('overallResult', '📊 检查自动更新结果:', 'info');
                    log('overallResult', `语言选择: ${languageSelect?.value || '未设置'} (${languageSelect?.selectedOptions[0]?.text || ''})`, 'info');
                    log('overallResult', `OTA选择: ${otaSelect?.value || '未设置'} (${otaSelect?.selectedOptions[0]?.text || ''})`, 'info');
                }, 3000);
            } else {
                log('overallResult', '❌ orderInput元素不存在', 'error');
            }
        }

        async function forceReinitialize() {
            clearLog('overallResult');
            log('overallResult', '强制重新初始化所有功能...', 'info');
            
            try {
                // 1. 重新初始化语言检测器
                if (window.OTA?.UnifiedLanguageDetector) {
                    log('overallResult', '🔧 重新创建语言检测器...', 'info');
                    const detector = new window.OTA.UnifiedLanguageDetector();
                    await detector.init();
                    log('overallResult', '✅ 语言检测器重新初始化完成', 'success');
                }
                
                // 2. 重新初始化实时分析管理器
                const uiManager = window.OTA?.uiManager;
                if (uiManager && window.OTA?.managers?.RealtimeAnalysisManager) {
                    log('overallResult', '🔧 重新创建实时分析管理器...', 'info');
                    uiManager.managers.realtimeAnalysis = new window.OTA.managers.RealtimeAnalysisManager(uiManager.elements, uiManager);
                    uiManager.managers.realtimeAnalysis.init();
                    log('overallResult', '✅ 实时分析管理器重新初始化完成', 'success');
                }
                
                // 3. 重新绑定事件
                const orderInput = document.getElementById('orderInput');
                if (orderInput && uiManager?.managers?.realtimeAnalysis) {
                    log('overallResult', '🔧 重新绑定事件监听器...', 'info');
                    orderInput.removeEventListener('input', uiManager.managers.realtimeAnalysis.handleRealtimeInput);
                    orderInput.addEventListener('input', uiManager.managers.realtimeAnalysis.handleRealtimeInput.bind(uiManager.managers.realtimeAnalysis));
                    orderInput._realtimeAnalysisListener = true;
                    log('overallResult', '✅ 事件监听器重新绑定完成', 'success');
                }
                
                log('overallResult', '🎉 强制重新初始化完成', 'success');
                
            } catch (error) {
                log('overallResult', `重新初始化失败: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            await testLanguageDetection();
            await testChannelDetection();
            await testAutoAnalysis();
            await testInputEvent();
        }

        async function manualFix() {
            await fixLanguageDetection();
            await fixChannelDetection();
            await fixAutoAnalysis();
        }

        function checkStatus() {
            clearLog('overallResult');
            log('overallResult', '检查所有组件状态...', 'info');
            
            const components = {
                'UnifiedLanguageDetector': window.OTA?.unifiedLanguageDetector,
                'ChannelDetector': window.OTA?.channelDetector,
                'GeminiService': window.getGeminiService?.(),
                'RealtimeAnalysisManager': window.OTA?.uiManager?.managers?.realtimeAnalysis,
                'UIManager': window.OTA?.uiManager
            };
            
            Object.entries(components).forEach(([name, component]) => {
                const status = component ? '✅ 存在' : '❌ 不存在';
                const type = component ? 'success' : 'error';
                log('overallResult', `${name}: ${status}`, type);
            });
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('overallResult', '页面加载完成，开始状态检查...', 'info');
                checkStatus();
                
                // 自动运行一次测试
                setTimeout(runAllTests, 5000);
            }, 3000);
        });
    </script>
</body>
</html>
