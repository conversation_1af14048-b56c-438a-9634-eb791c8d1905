<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netlify Functions 航班信息查询测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary { background-color: #3498db; color: white; }
        .btn-success { background-color: #27ae60; color: white; }
        .btn-warning { background-color: #f39c12; color: white; }
        .btn-danger { background-color: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .flight-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .flight-example {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .flight-example:hover {
            background-color: #2980b9;
        }
        
        #testLog {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #27ae60; }
        .status-error { background-color: #e74c3c; }
        .status-warning { background-color: #f39c12; }
        .status-info { background-color: #3498db; }
        
        .api-endpoint {
            background-color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛫 Netlify Functions 航班信息查询测试</h1>
        
        <div class="test-section">
            <h2>📡 API端点测试</h2>
            <div class="api-endpoint">
                GET /api/flight-info?flight={flightNumber}
            </div>
            <p>测试Netlify Functions是否正确代理FlightAware API调用</p>
            
            <div class="flight-examples">
                <div class="flight-example" onclick="testApiEndpoint('MH370')">MH370</div>
                <div class="flight-example" onclick="testApiEndpoint('AK6285')">AK6285</div>
                <div class="flight-example" onclick="testApiEndpoint('CZ3021')">CZ3021</div>
                <div class="flight-example" onclick="testApiEndpoint('9W123')">9W123</div>
                <div class="flight-example" onclick="testApiEndpoint('SQ001')">SQ001</div>
                <div class="flight-example" onclick="testApiEndpoint('INVALID')">INVALID</div>
            </div>
            
            <input type="text" id="customFlightInput" class="test-input" placeholder="输入自定义航班号进行测试...">
            <button class="btn btn-primary" onclick="testCustomFlight()">测试自定义航班号</button>
        </div>
        
        <div class="test-section">
            <h2>🔧 前端服务集成测试</h2>
            <p>测试前端FlightInfoService是否正确调用Netlify Functions</p>
            
            <button class="btn btn-success" onclick="testFrontendIntegration()">测试前端集成</button>
            <button class="btn btn-warning" onclick="testErrorHandling()">测试错误处理</button>
            <button class="btn btn-danger" onclick="testInvalidInput()">测试无效输入</button>
        </div>
        
        <div class="test-section">
            <h2>⚡ 性能和缓存测试</h2>
            <p>测试防抖机制、缓存策略和响应时间</p>
            
            <button class="btn btn-primary" onclick="testCaching()">测试缓存机制</button>
            <button class="btn btn-primary" onclick="testDebouncing()">测试防抖机制</button>
            <button class="btn btn-primary" onclick="testPerformance()">性能基准测试</button>
        </div>
        
        <div class="test-section">
            <h2>📊 测试日志</h2>
            <div id="testLog">等待测试开始...\n</div>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
            <button class="btn btn-success" onclick="runAllTests()">运行所有测试</button>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/logger.js"></script>
    <script src="js/flight-info-service.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        // 测试日志管理
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const statusClass = `status-${type}`;
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logElement.textContent += `[${timestamp}] ${statusIcon} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
            log('测试日志已清空');
        }

        // 初始化测试环境
        let flightInfoService;
        
        window.addEventListener('load', async () => {
            log('🚀 初始化Netlify Functions测试环境...');

            try {
                // 等待一下确保所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 检查FlightInfoService是否可用
                if (typeof FlightInfoService !== 'undefined') {
                    flightInfoService = new FlightInfoService();
                    log('✅ FlightInfoService 初始化成功');
                } else {
                    log('❌ FlightInfoService 未找到，请检查脚本加载', 'error');
                    return;
                }

                // 检查当前环境
                const isNetlifyEnv = window.location.hostname.includes('netlify.app') ||
                                   window.location.hostname.includes('netlify.com');

                if (isNetlifyEnv) {
                    log('🌐 检测到Netlify生产环境');
                } else {
                    log('🏠 检测到本地开发环境');
                }

                log('🎯 测试环境准备完成，可以开始测试');

            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        });

        // API端点直接测试
        async function testApiEndpoint(flightNumber) {
            log(`🔍 直接测试API端点: /api/flight-info?flight=${flightNumber}`);
            
            try {
                const startTime = Date.now();
                const response = await fetch(`/api/flight-info?flight=${encodeURIComponent(flightNumber)}`);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                log(`⏱️ 响应时间: ${responseTime}ms`);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    log(`❌ API调用失败: ${response.status} ${response.statusText}`, 'error');
                    log(`错误详情: ${JSON.stringify(errorData, null, 2)}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API调用成功: ${flightNumber}`, 'success');
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                } else {
                    log(`⚠️ API返回错误: ${data.message}`, 'warning');
                }
                
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 测试自定义航班号
        function testCustomFlight() {
            const flightNumber = document.getElementById('customFlightInput').value.trim();
            if (!flightNumber) {
                log('⚠️ 请输入航班号', 'warning');
                return;
            }
            testApiEndpoint(flightNumber);
        }

        // 前端服务集成测试
        async function testFrontendIntegration() {
            log('🧪 测试前端服务集成...');
            
            if (!flightInfoService) {
                log('❌ FlightInfoService未初始化', 'error');
                return;
            }
            
            try {
                const testFlights = ['MH370', 'AK6285', 'SQ001'];
                
                for (const flight of testFlights) {
                    log(`🔍 测试航班: ${flight}`);
                    const startTime = Date.now();
                    
                    const result = await flightInfoService.queryFlightInfoWithDebounce(flight, `test-${flight}`);
                    
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    if (result.success) {
                        log(`✅ ${flight} 查询成功 (${responseTime}ms)`, 'success');
                        log(`状态: ${result.status}, 机型: ${result.aircraft}`);
                    } else {
                        log(`❌ ${flight} 查询失败: ${result.error}`, 'error');
                    }
                }
                
                log('🎯 前端服务集成测试完成', 'success');
                
            } catch (error) {
                log(`❌ 前端服务测试失败: ${error.message}`, 'error');
            }
        }

        // 错误处理测试
        async function testErrorHandling() {
            log('🧪 测试错误处理机制...');
            
            const errorCases = [
                { flight: '', description: '空航班号' },
                { flight: '123', description: '纯数字航班号' },
                { flight: 'TOOLONG123456', description: '过长航班号' },
                { flight: 'NOTEXIST999', description: '不存在的航班' }
            ];
            
            for (const testCase of errorCases) {
                log(`🔍 测试 ${testCase.description}: "${testCase.flight}"`);
                
                try {
                    if (testCase.flight === '') {
                        // 测试空航班号
                        const result = await flightInfoService.queryFlightInfo('');
                        log(`⚠️ 空航班号应该抛出错误，但返回了结果`, 'warning');
                    } else {
                        const result = await testApiEndpoint(testCase.flight);
                    }
                } catch (error) {
                    log(`✅ 正确捕获错误: ${error.message}`, 'success');
                }
            }
            
            log('🎯 错误处理测试完成', 'success');
        }

        // 无效输入测试
        async function testInvalidInput() {
            log('🧪 测试无效输入处理...');
            
            const invalidInputs = [null, undefined, {}, [], 123, true];
            
            for (const input of invalidInputs) {
                log(`🔍 测试无效输入: ${JSON.stringify(input)}`);
                
                try {
                    const result = await flightInfoService.queryFlightInfo(input);
                    log(`⚠️ 无效输入应该抛出错误，但返回了结果`, 'warning');
                } catch (error) {
                    log(`✅ 正确处理无效输入: ${error.message}`, 'success');
                }
            }
            
            log('🎯 无效输入测试完成', 'success');
        }

        // 缓存机制测试
        async function testCaching() {
            log('🧪 测试缓存机制...');
            
            const testFlight = 'MH370';
            
            // 第一次查询
            log(`🔍 第一次查询 ${testFlight}...`);
            const start1 = Date.now();
            const result1 = await flightInfoService.queryFlightInfoWithDebounce(testFlight, 'cache-test-1');
            const time1 = Date.now() - start1;
            log(`⏱️ 第一次查询耗时: ${time1}ms`);
            
            // 立即第二次查询（应该使用缓存）
            log(`🔍 第二次查询 ${testFlight} (应该使用缓存)...`);
            const start2 = Date.now();
            const result2 = await flightInfoService.queryFlightInfoWithDebounce(testFlight, 'cache-test-2');
            const time2 = Date.now() - start2;
            log(`⏱️ 第二次查询耗时: ${time2}ms`);
            
            if (time2 < time1 / 2) {
                log(`✅ 缓存机制工作正常，第二次查询明显更快`, 'success');
            } else {
                log(`⚠️ 缓存可能未生效，两次查询时间相近`, 'warning');
            }
            
            log('🎯 缓存机制测试完成', 'success');
        }

        // 防抖机制测试
        async function testDebouncing() {
            log('🧪 测试防抖机制...');
            
            const testFlight = 'AK6285';
            const promises = [];
            
            // 快速连续发起多个请求
            for (let i = 0; i < 5; i++) {
                log(`🔍 发起第 ${i + 1} 个请求...`);
                promises.push(flightInfoService.queryFlightInfoWithDebounce(testFlight, 'debounce-test'));
                await new Promise(resolve => setTimeout(resolve, 50)); // 50ms间隔
            }
            
            log('⏳ 等待所有请求完成...');
            const results = await Promise.all(promises);
            
            // 检查结果
            const successCount = results.filter(r => r.success).length;
            log(`📊 ${promises.length} 个请求中有 ${successCount} 个成功`);
            
            if (successCount > 0) {
                log(`✅ 防抖机制工作正常，避免了重复请求`, 'success');
            } else {
                log(`❌ 防抖机制可能有问题`, 'error');
            }
            
            log('🎯 防抖机制测试完成', 'success');
        }

        // 性能基准测试
        async function testPerformance() {
            log('🧪 开始性能基准测试...');
            
            const testFlights = ['MH370', 'AK6285', 'CZ3021', 'SQ001'];
            const results = [];
            
            for (const flight of testFlights) {
                log(`🔍 性能测试: ${flight}`);
                const startTime = Date.now();
                
                try {
                    const result = await flightInfoService.queryFlightInfoWithDebounce(flight, `perf-${flight}`);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    results.push({
                        flight,
                        responseTime,
                        success: result.success
                    });
                    
                    log(`⏱️ ${flight}: ${responseTime}ms ${result.success ? '✅' : '❌'}`);
                    
                } catch (error) {
                    log(`❌ ${flight}: 测试失败 - ${error.message}`, 'error');
                }
            }
            
            // 计算统计信息
            const successfulResults = results.filter(r => r.success);
            if (successfulResults.length > 0) {
                const avgTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
                const minTime = Math.min(...successfulResults.map(r => r.responseTime));
                const maxTime = Math.max(...successfulResults.map(r => r.responseTime));
                
                log(`📊 性能统计:`, 'success');
                log(`   平均响应时间: ${avgTime.toFixed(2)}ms`);
                log(`   最快响应时间: ${minTime}ms`);
                log(`   最慢响应时间: ${maxTime}ms`);
                log(`   成功率: ${(successfulResults.length / results.length * 100).toFixed(1)}%`);
            }
            
            log('🎯 性能基准测试完成', 'success');
        }

        // 运行所有测试
        async function runAllTests() {
            log('🚀 开始运行完整测试套件...');
            clearLog();
            
            await testFrontendIntegration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testErrorHandling();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCaching();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDebouncing();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPerformance();
            
            log('🎉 完整测试套件执行完成！', 'success');
        }
    </script>
</body>
</html>
