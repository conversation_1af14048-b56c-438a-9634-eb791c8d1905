/**
 * OTA策略统一配置 - 合并版本
 * 
 * 设计目标：
 * - 将所有OTA策略合并到单一文件中
 * - 保持原有的静态方法调用接口
 * - 简化策略文件管理
 * 
 * <AUTHOR>
 * @version 1.0.0 (Unified Strategies)
 */

(function() {
    'use strict';

    /**
     * Fliggy渠道策略
     * 保持原有接口完全不变
     */
    class FliggyOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'fliggy';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
                // 价格与车型ID映射片段
                ota_price: '价格识别与换算：请识别"商家实收"数值作为订单价格；识别订单所属地区，若为马来西亚，最终价格=商家实收×0.84×0.615；若为新加坡，最终价格=商家实收×0.84×0.2；均保留两位小数，输出最终价；无法确定地区时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                // 新增：日期和地区字段强化
                pickup_date: 'Fliggy日期提取：**必须从订单文本中提取具体日期**，如"2025-08-11"等完整格式，转换为YYYY-MM-DD格式输出。',
                driving_region_id: 'Fliggy地区映射：根据地点准确识别driving_region_id，特别注意斗湖机场→4(Sabah)，不可返回null。',
                // 字段完整性确保
                pickup_location: '上车地点：必须从订单中提取完整地点名称，保持原始描述准确性。',
                dropoff_location: '下车地点：必须从订单中提取完整地点名称，保持原始描述准确性。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 价格计算规则
         */
        static calculatePrice(basePrice, region = 'malaysia') {
            const factor = region === 'singapore' ? 0.84 * 0.2 : 0.84 * 0.615;
            return Math.round(basePrice * factor * 100) / 100;
        }
    }

    /**
     * JingGe渠道策略
     * 保持原有接口完全不变
     */
    class JingGeOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'jingge';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是JingGe商铺渠道的订单，输出JSON时ota字段请设置为"Jing Ge"。',
                // 价格、车型ID、联系方式、订单号
                ota_price: '价格识别与换算：若为JingGe商铺订单，最终价格=基础价×0.615；保留两位小数，明确输出最终价；无法确定时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                customer_contact: '若订单未提供手机号，可临时使用订单号(ota_reference_number)作为联系标识填充customer_contact字段；若存在手机号，请保持原值，不要覆盖。',
                ota_reference_number: '订单号识别：请从文本中抽取明确的订单编号；一般为纯数字组合，若无可靠线索，请返回null，不要凭空生成。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 价格计算规则
         */
        static calculatePrice(basePrice) {
            return Math.round(basePrice * 0.615 * 100) / 100;
        }
    }

    /**
     * 统一策略配置对象
     * 提供统一的策略访问接口
     */
    const UnifiedOTAStrategies = {
        fliggy: FliggyOTAStrategy,
        jingge: JingGeOTAStrategy,
        
        /**
         * 获取指定策略
         */
        getStrategy(channel) {
            return this[channel] || null;
        },

        /**
         * 获取所有可用策略
         */
        getAllStrategies() {
            return {
                fliggy: FliggyOTAStrategy,
                jingge: JingGeOTAStrategy
            };
        },

        /**
         * 检查策略是否存在
         */
        hasStrategy(channel) {
            return channel in this && typeof this[channel] === 'function';
        }
    };

    // 暴露到全局作用域（保持向后兼容）
    window.FliggyOTAStrategy = FliggyOTAStrategy;
    window.JingGeOTAStrategy = JingGeOTAStrategy;
    
    // 新的统一接口
    window.OTA = window.OTA || {};
    window.OTA.strategies = UnifiedOTAStrategies;

    console.log('✅ 统一OTA策略配置已加载');

})();