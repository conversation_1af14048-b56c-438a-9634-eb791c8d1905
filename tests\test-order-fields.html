<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单字段显示测试</title>
    
    <!-- 加载CSS -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <link rel="stylesheet" href="css/multi-order/mobile.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .test-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .test-btn.secondary {
            background: #6c757d;
        }
        
        .test-btn.secondary:hover {
            background: #545b62;
        }
        
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            color: #333;
        }
        
        .old-style {
            border-left: 4px solid #dc3545;
        }
        
        .new-style {
            border-left: 4px solid #28a745;
        }
        
        /* 旧版样式模拟 */
        .old-order-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            font-size: 14px; /* 原始大小 */
        }
        
        .old-order-header {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        
        .old-order-details {
            color: #6c757d;
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            .comparison-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🎨 多订单字段显示优化测试</h1>
        <p>测试新增的OTA、车型、行驶区域字段以及字体大小减少15%的效果</p>
    </div>
    
    <div class="test-controls">
        <button class="test-btn" onclick="showMultiOrderTest()">显示多订单面板</button>
        <button class="test-btn secondary" onclick="toggleComparison()">显示对比</button>
        <button class="test-btn secondary" onclick="hideMultiOrder()">隐藏面板</button>
    </div>
    
    <div id="comparisonContainer" class="comparison-container" style="display: none;">
        <div class="comparison-section old-style">
            <div class="comparison-title">旧版样式 (原始字体大小)</div>
            <div class="old-order-card">
                <div class="old-order-header">订单 1</div>
                <div class="old-order-details">
                    客户: 张三 (12345678901)<br>
                    路线: 吉隆坡国际机场 → 吉隆坡希尔顿酒店<br>
                    时间: 2025-08-07 10:00<br>
                    价格: 150 MYR<br>
                    <em>缺少OTA、车型、区域信息</em>
                </div>
            </div>
        </div>
        
        <div class="comparison-section new-style">
            <div class="comparison-title">新版样式 (字体减少15% + 新字段)</div>
            <div id="newStyleExample">
                <!-- 这里将通过JavaScript生成新样式的订单卡片 -->
            </div>
        </div>
    </div>

    <!-- 多订单面板HTML结构 -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-content">
            <div class="multi-order-header">
                <div class="header-left">
                    <button type="button" id="backToMainBtn" class="btn btn-header-back" title="返回主页">←返回主页</button>
                    <h3>📦多订单管理 - 字段显示测试</h3>
                </div>
                <div class="multi-order-controls">
                    <div class="header-actions">
                        <button type="button" id="closeMultiOrderBtn" class="btn btn-icon btn-close" title="关闭">✕</button>
                    </div>
                </div>
            </div>

            <!-- 简化版批量操作控件 -->
            <div class="batch-controls">
                <span class="batch-controls-label">批量设置:</span>
                <select id="batchOtaSelect" class="batch-dropdown-btn">
                    <option value="">选择OTA渠道</option>
                    <!-- OTA选项将通过JavaScript动态生成 -->
                </select>
                <span class="batch-info-text">选择后自动应用到订单</span>
            </div>

            <div class="multi-order-list" id="multiOrderList">
                <!-- 多订单项将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 模拟基础环境 -->
    <script>
        window.OTA = window.OTA || {};
        
        function getLogger() {
            return {
                log: (message, level, data) => {
                    console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
                }
            };
        }
    </script>

    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        function showMultiOrderTest() {
            console.log('🧪 显示多订单字段测试...');
            
            const mockMultiOrderResult = {
                isMultiOrder: true,
                confidence: 0.9,
                orderCount: 3,
                orders: [
                    {
                        customer_name: "张三",
                        customer_contact: "12345678901",
                        pickup: "吉隆坡国际机场 T1航站楼",
                        dropoff: "吉隆坡希尔顿酒店 (金三角区)",
                        pickup_date: "2025-08-07",
                        pickup_time: "10:00",
                        ota_price: 150,
                        currency: "MYR",
                        _otaChannel: "agoda",
                        vehicle_type: "豪华轿车",
                        driving_region: "市区"
                    },
                    {
                        customer_name: "李四", 
                        customer_contact: "09876543210",
                        pickup: "双子塔购物中心",
                        dropoff: "云顶高原度假村",
                        pickup_date: "2025-08-07",
                        pickup_time: "14:00",
                        ota_price: 200,
                        currency: "MYR",
                        _otaChannel: "booking",
                        vehicle_type: "商务车",
                        driving_region: "跨州"
                    },
                    {
                        customer_name: "王五",
                        customer_contact: "18888888888",
                        pickup: "马六甲古城",
                        dropoff: "新山乐高乐园",
                        pickup_date: "2025-08-08",
                        pickup_time: "09:30",
                        ota_price: 280,
                        currency: "MYR",
                        _otaChannel: "expedia",
                        vehicle_type: "MPV",
                        driving_region: "跨州长途"
                    }
                ]
            };

            const event = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: mockMultiOrderResult,
                    orderText: "测试多个包含完整字段信息的订单..."
                }
            });

            document.dispatchEvent(event);
        }

        function toggleComparison() {
            const container = document.getElementById('comparisonContainer');
            const isVisible = container.style.display !== 'none';
            
            if (isVisible) {
                container.style.display = 'none';
            } else {
                container.style.display = 'grid';
                generateNewStyleExample();
            }
        }

        function generateNewStyleExample() {
            const container = document.getElementById('newStyleExample');
            const manager = window.OTA?.multiOrderManagerV2;
            
            if (manager) {
                const sampleOrder = {
                    customer_name: "张三",
                    customer_contact: "12345678901",
                    pickup: "吉隆坡国际机场",
                    dropoff: "吉隆坡希尔顿酒店",
                    pickup_date: "2025-08-07",
                    pickup_time: "10:00",
                    ota_price: 150,
                    currency: "MYR",
                    _otaChannel: "agoda",
                    vehicle_type: "豪华轿车",
                    driving_region: "市区"
                };
                
                container.innerHTML = manager.generateOrderListHTML([sampleOrder]);
            } else {
                container.innerHTML = '<p style="color: #dc3545;">MultiOrderManagerV2 未加载</p>';
            }
        }

        function hideMultiOrder() {
            const panel = document.getElementById('multiOrderPanel');
            panel.classList.add('hidden');
            panel.style.display = 'none';
        }

        // 页面加载完成后设置事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 设置关闭按钮事件
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            const backBtn = document.getElementById('backToMainBtn');
            
            if (closeBtn) {
                closeBtn.addEventListener('click', hideMultiOrder);
            }
            
            if (backBtn) {
                backBtn.addEventListener('click', hideMultiOrder);
            }
            
            console.log('📊 字段显示测试页面加载完成');
        });
    </script>
</body>
</html>
