<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单数据流测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 12px;
            margin-bottom: 20px;
            font-size: 20px;
        }
        .result {
            margin: 12px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border-left-color: #ffc107;
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px 5px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            margin: 15px 0;
            font-size: 14px;
            line-height: 1.5;
        }
        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(90deg, #e8f5e8, #f0f8ff);
            border-radius: 10px;
            border-left: 5px solid #4CAF50;
        }
        .step-number {
            background: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 多订单批量创建数据流测试</h1>
        <p>验证GoMyHire API字段映射、数据转换和批量提交完整性</p>
    </div>

    <div class="test-container">
        <h2 class="test-title">🎯 测试控制面板</h2>
        <button onclick="runFullDataFlowTest()">🔬 完整数据流测试</button>
        <button onclick="testApiFieldMapping()">📋 API字段映射测试</button>
        <button onclick="testOtaChannelMapping()">🌐 OTA渠道映射测试</button>
        <button onclick="testBackendUserIdAssignment()">👤 后台用户ID测试</button>
        <button onclick="simulateBatchSubmission()">🚀 模拟批量提交</button>
        <button onclick="clearResults()">🗑️ 清理结果</button>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 测试结果</h2>
        <div id="testResults"></div>
    </div>

    <!-- 加载必需的脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/core/global-field-standardization-layer.js"></script>

    <script>
        // 模拟的测试数据
        const MOCK_ORDERS = [
            {
                // 混合格式的订单数据（模拟Gemini输出）
                customerName: '张三',
                customerContact: '0123456789',
                carTypeId: 5,
                drivingRegionId: 1,
                pickupDate: '2025-01-10',
                pickupTime: '10:00',
                pickup: 'KLIA机场',
                destination: '吉隆坡市中心',
                otaPrice: '150.00',
                _otaChannel: 'Fliggy'
            },
            {
                // API格式的订单数据
                customer_name: '李四',
                customer_contact: '0198765432',
                car_type_id: 15,
                driving_region_id: 2,
                date: '2025-01-11',
                time: '14:30',
                pickup: '槟城机场',
                destination: '乔治市',
                ota_price: '200.00',
                ota: 'Booking.com'
            },
            {
                // 缺失字段的订单数据（测试默认值填充）
                customerName: '王五',
                pickup: '新山机场',
                destination: '新山市区',
                pickupDate: '2025-01-12'
            }
        ];

        let testResultsContainer;

        window.addEventListener('load', function() {
            testResultsContainer = document.getElementById('testResults');
            addResult('🔧 多订单数据流测试系统已加载', 'info');
        });

        function addResult(message, type = 'info') {
            if (!testResultsContainer) return;
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            testResultsContainer.appendChild(resultDiv);
            
            // 滚动到最新结果
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }

        function clearResults() {
            if (testResultsContainer) {
                testResultsContainer.innerHTML = '';
                addResult('🗑️ 测试结果已清理', 'info');
            }
        }

        // 完整数据流测试
        function runFullDataFlowTest() {
            addResult('<h3>🔬 开始完整数据流测试</h3>', 'info');
            
            const steps = [
                { name: '系统组件检查', func: checkSystemComponents },
                { name: '字段标准化测试', func: testFieldStandardization },
                { name: '必需字段补充测试', func: testRequiredFieldsCompletion },
                { name: '数据完整性验证', func: validateDataIntegrity },
                { name: 'API兼容性检查', func: checkApiCompatibility }
            ];
            
            let currentStep = 0;
            
            function runNextStep() {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    addResult(`<div class="flow-step"><div class="step-number">${currentStep + 1}</div><strong>${step.name}</strong></div>`, 'info');
                    
                    try {
                        step.func();
                        currentStep++;
                        setTimeout(runNextStep, 500);
                    } catch (error) {
                        addResult(`❌ 步骤 ${currentStep + 1} 失败: ${error.message}`, 'error');
                    }
                } else {
                    addResult('<h3>✅ 完整数据流测试完成</h3>', 'success');
                }
            }
            
            runNextStep();
        }

        // 检查系统组件
        function checkSystemComponents() {
            const checks = [
                {
                    name: '字段标准化层',
                    test: () => window.standardizeFieldsToApi && typeof window.standardizeFieldsToApi === 'function'
                },
                {
                    name: 'API服务',
                    test: () => window.getApiService && typeof window.getApiService === 'function'
                },
                {
                    name: '应用状态管理',
                    test: () => window.getAppState && typeof window.getAppState === 'function'
                }
            ];
            
            checks.forEach(check => {
                const passed = check.test();
                addResult(`${check.name}: ${passed ? '✅ 可用' : '❌ 不可用'}`, passed ? 'success' : 'error');
            });
        }

        // 测试字段标准化
        function testFieldStandardization() {
            MOCK_ORDERS.forEach((order, index) => {
                try {
                    const standardized = window.standardizeFieldsToApi ? 
                        window.standardizeFieldsToApi(order, `test-order-${index}`) : order;
                    
                    const hasApiFields = ['customer_name', 'car_type_id', 'driving_region_id'].every(field => 
                        standardized.hasOwnProperty(field) || order.hasOwnProperty(field)
                    );
                    
                    addResult(`订单 ${index + 1} 字段标准化: ${hasApiFields ? '✅ 成功' : '⚠️ 部分字段缺失'}`, hasApiFields ? 'success' : 'warning');
                    
                    if (Object.keys(order).length !== Object.keys(standardized).length) {
                        addResult(`<div class="json-display">原始字段: ${JSON.stringify(Object.keys(order), null, 2)}\n标准化后: ${JSON.stringify(Object.keys(standardized), null, 2)}</div>`, 'info');
                    }
                    
                } catch (error) {
                    addResult(`订单 ${index + 1} 标准化失败: ${error.message}`, 'error');
                }
            });
        }

        // 测试必需字段补充
        function testRequiredFieldsCompletion() {
            const requiredFields = ['incharge_by_backend_user_id', 'sub_category_id', 'car_type_id', 'driving_region_id', 'ota'];
            
            MOCK_ORDERS.forEach((order, index) => {
                // 模拟多订单管理器的字段补充逻辑
                const processedOrder = simulateRequiredFieldsCompletion(order);
                
                const missingFields = requiredFields.filter(field => !processedOrder[field]);
                
                if (missingFields.length === 0) {
                    addResult(`订单 ${index + 1} 必需字段: ✅ 完整`, 'success');
                } else {
                    addResult(`订单 ${index + 1} 缺失字段: ${missingFields.join(', ')}`, 'warning');
                }
                
                // 显示补充后的字段
                const addedFields = Object.keys(processedOrder).filter(field => !order[field]);
                if (addedFields.length > 0) {
                    addResult(`自动补充字段: ${addedFields.join(', ')}`, 'info');
                }
            });
        }

        // 模拟必需字段补充
        function simulateRequiredFieldsCompletion(order) {
            const processed = { ...order };
            
            // 模拟incharge_by_backend_user_id设置
            if (!processed.incharge_by_backend_user_id) {
                processed.incharge_by_backend_user_id = 1; // 默认值
            }
            
            // 模拟sub_category_id设置
            if (!processed.sub_category_id) {
                processed.sub_category_id = 2; // 接机服务
            }
            
            // 模拟car_type_id设置
            if (!processed.car_type_id && !processed.carTypeId) {
                processed.car_type_id = 5; // 默认5座车
            }
            
            // 模拟driving_region_id设置
            if (!processed.driving_region_id && !processed.drivingRegionId) {
                processed.driving_region_id = 1; // 默认KL/Selangor
            }
            
            // 模拟OTA字段设置
            if (!processed.ota) {
                processed.ota = processed._otaChannel || 'GoMyHire Direct';
            }
            
            return processed;
        }

        // 验证数据完整性
        function validateDataIntegrity() {
            let totalFields = 0;
            let standardizedFields = 0;
            let completedFields = 0;
            
            MOCK_ORDERS.forEach((order, index) => {
                totalFields += Object.keys(order).length;
                
                const standardized = window.standardizeFieldsToApi ? 
                    window.standardizeFieldsToApi(order, `integrity-test-${index}`) : order;
                standardizedFields += Object.keys(standardized).length;
                
                const completed = simulateRequiredFieldsCompletion(standardized);
                completedFields += Object.keys(completed).length;
            });
            
            addResult(`数据完整性统计:`, 'info');
            addResult(`- 原始字段总数: ${totalFields}`, 'info');
            addResult(`- 标准化后字段: ${standardizedFields}`, 'info');
            addResult(`- 补充完整后: ${completedFields}`, 'info');
            addResult(`- 字段完整率: ${Math.round((completedFields / (totalFields + (completedFields - totalFields))) * 100)}%`, 'info');
        }

        // 检查API兼容性
        function checkApiCompatibility() {
            const apiService = window.getApiService ? window.getApiService() : null;
            
            if (!apiService) {
                addResult('❌ API服务不可用', 'error');
                return;
            }
            
            // 测试API字段验证
            MOCK_ORDERS.forEach((order, index) => {
                const processed = simulateRequiredFieldsCompletion(
                    window.standardizeFieldsToApi ? 
                        window.standardizeFieldsToApi(order, `api-test-${index}`) : order
                );
                
                // 模拟API验证（不实际调用API）
                const hasRequiredFields = ['customer_name', 'car_type_id', 'driving_region_id', 'incharge_by_backend_user_id']
                    .every(field => processed[field]);
                
                addResult(`订单 ${index + 1} API兼容性: ${hasRequiredFields ? '✅ 兼容' : '❌ 不兼容'}`, 
                    hasRequiredFields ? 'success' : 'error');
            });
        }

        // 测试API字段映射
        function testApiFieldMapping() {
            addResult('<h3>📋 API字段映射测试</h3>', 'info');
            
            const testCases = [
                { input: 'customerName', expected: 'customer_name', category: '客户信息' },
                { input: 'carTypeId', expected: 'car_type_id', category: '车型' },
                { input: 'drivingRegionId', expected: 'driving_region_id', category: '区域' },
                { input: 'pickupDate', expected: 'date', category: '时间' },
                { input: 'otaPrice', expected: 'ota_price', category: '价格' }
            ];
            
            testCases.forEach(testCase => {
                const testData = { [testCase.input]: 'test_value' };
                const result = window.standardizeFieldsToApi ? 
                    window.standardizeFieldsToApi(testData, 'field-mapping-test') : testData;
                
                const actualField = Object.keys(result).find(key => result[key] === 'test_value');
                const success = actualField === testCase.expected;
                
                addResult(`${testCase.category} (${testCase.input}): ${success ? '✅' : '❌'} → ${actualField}`, 
                    success ? 'success' : 'error');
            });
        }

        // 测试OTA渠道映射
        function testOtaChannelMapping() {
            addResult('<h3>🌐 OTA渠道映射测试</h3>', 'info');
            
            const testChannels = ['Fliggy', 'Booking.com', 'Expedia', 'Klook West Malaysia'];
            
            testChannels.forEach(channel => {
                const testOrder = {
                    customerName: '测试用户',
                    _otaChannel: channel
                };
                
                const processed = simulateRequiredFieldsCompletion(testOrder);
                const hasCorrectOta = processed.ota === channel;
                
                addResult(`${channel}: ${hasCorrectOta ? '✅ 正确映射' : '❌ 映射失败'} (${processed.ota})`, 
                    hasCorrectOta ? 'success' : 'error');
            });
        }

        // 测试后台用户ID分配
        function testBackendUserIdAssignment() {
            addResult('<h3>👤 后台用户ID分配测试</h3>', 'info');
            
            const apiService = window.getApiService ? window.getApiService() : null;
            
            if (apiService && apiService.getDefaultBackendUserId) {
                const defaultUserId = apiService.getDefaultBackendUserId();
                addResult(`默认后台用户ID: ${defaultUserId || '未获取到'}`, defaultUserId ? 'success' : 'warning');
                
                // 测试字段补充
                const testOrder = { customerName: '测试用户' };
                const processed = simulateRequiredFieldsCompletion(testOrder);
                
                addResult(`自动分配后台用户ID: ${processed.incharge_by_backend_user_id ? '✅ 成功' : '❌ 失败'}`, 
                    processed.incharge_by_backend_user_id ? 'success' : 'error');
            } else {
                addResult('❌ 无法获取API服务或用户ID分配方法', 'error');
            }
        }

        // 模拟批量提交
        function simulateBatchSubmission() {
            addResult('<h3>🚀 模拟批量提交流程</h3>', 'info');
            
            MOCK_ORDERS.forEach((order, index) => {
                setTimeout(() => {
                    // 模拟完整的数据处理流程
                    let processedOrder = { ...order };
                    
                    // 1. 字段标准化
                    if (window.standardizeFieldsToApi) {
                        processedOrder = window.standardizeFieldsToApi(processedOrder, `batch-${index}`);
                        addResult(`📋 订单 ${index + 1}: 字段标准化完成`, 'info');
                    }
                    
                    // 2. 必需字段补充
                    processedOrder = simulateRequiredFieldsCompletion(processedOrder);
                    addResult(`🔧 订单 ${index + 1}: 必需字段补充完成`, 'info');
                    
                    // 3. API兼容性验证
                    const isCompatible = ['customer_name', 'car_type_id', 'incharge_by_backend_user_id']
                        .every(field => processedOrder[field]);
                    
                    addResult(`🎯 订单 ${index + 1}: ${isCompatible ? '✅ 准备就绪' : '❌ 验证失败'}`, 
                        isCompatible ? 'success' : 'error');
                    
                    // 4. 显示最终数据
                    addResult(`<div class="json-display">订单 ${index + 1} 最终数据:\n${JSON.stringify(processedOrder, null, 2)}</div>`, 'info');
                    
                    if (index === MOCK_ORDERS.length - 1) {
                        setTimeout(() => {
                            addResult('<h3>🎉 批量提交模拟完成</h3>', 'success');
                        }, 500);
                    }
                    
                }, index * 1000);
            });
        }

    </script>
</body>
</html>