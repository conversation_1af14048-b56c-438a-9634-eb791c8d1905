# Copilot instructions for this repo

Static SPA for OTA order processing (GoMyHire). No bundler; modules load via <script> tags in strict order. Global namespace: window.OTA with DI container + Service Locator + startup coordinator.

## Architecture to follow
- Critical script order (see bottom of index.html): Core (js/core/*) → utils/services/managers → UI manager → main.js.
- DI container: js/core/dependency-container.js exposes window.OTA.container and window.OTA.registerService/getService.
- Service Locator: js/core/service-locator.js exposes window.OTA.serviceLocator and getters (getAppState, getLogger, getApiService, getUIManager, getMultiOrder…). Prefer getService('name').
- Startup: js/core/application-bootstrap.js runs phases (dependencies → services → managers → ui → finalization). Exposes window.OTA.debug { bootstrap, container, serviceLocator, getStartupReport(), restart() }.
- UI: js/ui-manager.js orchestrates js/managers/*; relies on DOM IDs in index.html (loginPanel, workspace, orderForm, ota, carTypeId, subCategoryId, drivingRegionId, etc.). I18n (js/i18n.js) initializes before UI in the bootstrap UI phase.
- Multi-order/OTA: js/multi-order/* (detector/processor/renderer/state/coordinator) and js/ota-system/* (detection/customization). Channel prompts: js/ota-system/config/prompt-templates.js.

## Conventions
- Register then resolve services:
  - Register: window.OTA.registerService('myService', () => ({ init(){} }));
  - Resolve: const svc = window.OTA.getService('myService') or window.OTA.serviceLocator.getService('myService').
- Avoid direct window.OTA.x || window.x; use getService(). Fallbacks exist but log migration warnings.
- New UI sub-manager: attach a class to window.OTA.managers; UIManager will new/init if present. Ensure its script loads before js/ui-manager.js.
- Maintain script order when adding files (group with similar core/managers). Keep main.js last.
- Respect CSP in netlify.toml; update script-src/connect-src if adding external APIs/CDNs.

## Developer workflows
- Local run: open index.html in a browser (static). Optional simple static server.
- Validate deployment: npm run build (runs deployment/validate-deployment.js; writes deployment-validation-report.json).
- Netlify: publish = "."; SPA redirects; NODE_VERSION=18; env vars in dashboard (e.g., FLIGHTAWARE_API_KEY). Functions dir configured but optional.
- Manual tests: many test-*.html at repo root and in tests/ for UI/multi-order checks.

## Debugging
- Startup: window.OTA.debug.getStartupReport(); window.OTA.debug.restart().
- Services: window.OTA.container.getRegisteredServices(); window.OTA.serviceLocator.getAvailableServices().
- Logger: const logger = getLogger(); logger.debugMode = true (js/logger.js intercepts console).
- AppState toggles: getAppState().set('config.debugMode', true) to see UI changes.

## Integration points
- Allowed by CSP/connect-src: gomyhire.com.my, generativelanguage.googleapis.com (Gemini), aeroapi.flightaware.com.
- Add a new OTA channel: extend prompt-templates.js and update detection/mapping in js/ota-system/* as needed.

## Quick examples
- Register service: window.OTA.registerService('foo', () => ({ init(){} }));
- Use service: const api = getApiService(); // or window.OTA.getService('apiService')
- Add UI sub-manager:
  window.OTA.managers = window.OTA.managers || {};
  window.OTA.managers.MyMgr = class { constructor(els){} init(){} };

Key files: index.html (DOM IDs, script order), js/core/* (container/locator/bootstrap), js/ui-manager.js, js/managers/*, js/multi-order/*, js/ota-system/*, deployment/validate-deployment.js, netlify.toml.
