<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整的字段显示修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .editable-field {
            display: inline-block;
            padding: 8px 12px;
            margin: 5px;
            background: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            min-width: 150px;
            transition: all 0.2s ease;
        }
        .editable-field:hover {
            background: #dee2e6;
            border-color: #007bff;
        }
        .editable-field.editing {
            background: #fff;
            border-color: #007bff;
        }
        .grid-value {
            display: inline-block;
            font-weight: 500;
        }
        .field-editor {
            width: 100%;
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            background: white;
        }
        .field-editor:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            margin-right: 8px;
            display: inline-block;
            min-width: 120px;
        }
        .field-row {
            margin: 10px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .test-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .before-after {
            display: flex;
            gap: 20px;
        }
        .before, .after {
            flex: 1;
            padding: 10px;
            border-radius: 4px;
        }
        .before {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .after {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>完整的字段显示修复验证</h1>
    
    <div class="test-container">
        <h2 class="test-title">🎯 修复验证目标</h2>
        <div class="before-after">
            <div class="before">
                <h4>修复前 (❌ 问题状态)</h4>
                <ul>
                    <li>车型字段显示: <code>5</code> (ID)</li>
                    <li>区域字段显示: <code>1</code> (ID)</li>
                    <li>下拉菜单样式不一致</li>
                    <li>区域选项与实际数据不同步</li>
                </ul>
            </div>
            <div class="after">
                <h4>修复后 (✅ 期望状态)</h4>
                <ul>
                    <li>车型字段显示: <code>5 Seater</code> (名称)</li>
                    <li>区域字段显示: <code>Kl/selangor</code> (名称)</li>
                    <li>下拉菜单样式统一</li>
                    <li>区域选项完全同步</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 测试订单数据</h2>
        <div class="test-data" id="testOrderData">
            正在加载测试数据...
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 实际显示测试</h2>
        
        <!-- 测试订单1：常用ID -->
        <div class="order-card" data-index="0">
            <h4>测试订单 1 - 常用配置</h4>
            
            <div class="field-row">
                <span class="field-label">车型 (car_type_id: 5):</span>
                <div class="editable-field" data-field="vehicleType" onclick="testEditField(0, 'vehicleType')">
                    <span class="grid-value" id="vehicle-5">正在测试...</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">区域 (driving_region_id: 1):</span>
                <div class="editable-field" data-field="drivingRegion" onclick="testEditField(0, 'drivingRegion')">
                    <span class="grid-value" id="region-1">正在测试...</span>
                </div>
            </div>
        </div>

        <!-- 测试订单2：其他ID -->
        <div class="order-card" data-index="1">
            <h4>测试订单 2 - 其他配置</h4>
            
            <div class="field-row">
                <span class="field-label">车型 (car_type_id: 15):</span>
                <div class="editable-field" data-field="vehicleType" onclick="testEditField(1, 'vehicleType')">
                    <span class="grid-value" id="vehicle-15">正在测试...</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">区域 (driving_region_id: 4):</span>
                <div class="editable-field" data-field="drivingRegion" onclick="testEditField(1, 'drivingRegion')">
                    <span class="grid-value" id="region-4">正在测试...</span>
                </div>
            </div>
        </div>

        <!-- 测试订单3：高级ID -->
        <div class="order-card" data-index="2">
            <h4>测试订单 3 - 高级配置</h4>
            
            <div class="field-row">
                <span class="field-label">车型 (car_type_id: 32):</span>
                <div class="editable-field" data-field="vehicleType" onclick="testEditField(2, 'vehicleType')">
                    <span class="grid-value" id="vehicle-32">正在测试...</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">区域 (driving_region_id: 2):</span>
                <div class="editable-field" data-field="drivingRegion" onclick="testEditField(2, 'drivingRegion')">
                    <span class="grid-value" id="region-2">正在测试...</span>
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🧪 自动化测试</h2>
        <button onclick="runFullTest()">🚀 运行完整测试</button>
        <button onclick="testStandardization()">🔄 测试字段标准化</button>
        <button onclick="testDropdownStyling()">🎨 测试下拉样式</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
        <div id="testResults"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/core/global-field-standardization-layer.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        // 测试订单数据 - 包含各种字段格式
        const testOrders = [
            { 
                car_type_id: 5, 
                driving_region_id: 1, 
                customer_name: '测试客户1',
                vehicleType: '5座车',  // 混合格式测试
                region: 'KL'
            },
            { 
                car_type_id: 15, 
                driving_region_id: 4, 
                customer_name: '测试客户2',
                carTypeId: 15,  // 旧格式测试
                drivingRegionId: 4
            },
            { 
                car_type_id: 32, 
                driving_region_id: 2, 
                customer_name: '测试客户3',
                vehicle: 'Alphard',  // 名称格式测试
                area: 'Penang'
            }
        ];

        let testManager = null;

        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            
            // 自动滚动到结果
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function displayTestData() {
            const dataDisplay = document.getElementById('testOrderData');
            dataDisplay.innerHTML = `<pre>${JSON.stringify(testOrders, null, 2)}</pre>`;
        }

        function runFullTest() {
            clearResults();
            showResult('<h3>🚀 开始完整测试流程</h3>', 'info');
            
            // 1. 检查管理器加载
            if (!window.OTA || !window.OTA.multiOrderManager) {
                showResult('❌ 多订单管理器未加载', 'error');
                return;
            }
            
            testManager = window.OTA.multiOrderManager;
            
            // 设置测试数据
            if (!testManager.state) {
                testManager.state = { parsedOrders: testOrders };
            } else {
                testManager.state.parsedOrders = testOrders;
            }
            
            showResult('✅ 多订单管理器已加载并配置测试数据', 'success');
            
            // 2. 测试字段显示
            setTimeout(() => testFieldDisplays(), 100);
            
            // 3. 测试标准化
            setTimeout(() => testStandardization(), 500);
            
            // 4. 生成测试报告
            setTimeout(() => generateTestReport(), 1000);
        }

        function testFieldDisplays() {
            showResult('<h4>📊 测试字段显示功能</h4>', 'info');
            
            let passCount = 0;
            let totalTests = 0;
            
            testOrders.forEach((order, index) => {
                // 测试车型显示
                try {
                    const vehicleDisplay = testManager.getVehicleTypeDisplay(order);
                    const vehicleId = order.car_type_id;
                    const isNameNotId = isNaN(parseInt(vehicleDisplay)) || vehicleDisplay.length > 2;
                    
                    document.getElementById(`vehicle-${vehicleId}`).textContent = vehicleDisplay;
                    
                    totalTests++;
                    if (isNameNotId) {
                        passCount++;
                        showResult(`✅ 车型显示 #${index+1}: ID ${vehicleId} → "${vehicleDisplay}"`, 'success');
                    } else {
                        showResult(`❌ 车型显示 #${index+1}: ID ${vehicleId} → "${vehicleDisplay}" (仍显示ID)`, 'error');
                    }
                } catch (error) {
                    totalTests++;
                    showResult(`❌ 车型显示 #${index+1}: 异常 - ${error.message}`, 'error');
                }
                
                // 测试区域显示
                try {
                    const regionDisplay = testManager.getDrivingRegionDisplay(order);
                    const regionId = order.driving_region_id;
                    const isNameNotId = isNaN(parseInt(regionDisplay)) || regionDisplay.length > 2;
                    
                    document.getElementById(`region-${regionId}`).textContent = regionDisplay;
                    
                    totalTests++;
                    if (isNameNotId) {
                        passCount++;
                        showResult(`✅ 区域显示 #${index+1}: ID ${regionId} → "${regionDisplay}"`, 'success');
                    } else {
                        showResult(`❌ 区域显示 #${index+1}: ID ${regionId} → "${regionDisplay}" (仍显示ID)`, 'error');
                    }
                } catch (error) {
                    totalTests++;
                    showResult(`❌ 区域显示 #${index+1}: 异常 - ${error.message}`, 'error');
                }
            });
            
            // 显示测试结果汇总
            const passRate = Math.round((passCount / totalTests) * 100);
            if (passRate >= 80) {
                showResult(`📊 字段显示测试完成: ${passCount}/${totalTests} 通过 (${passRate}%)`, 'success');
            } else {
                showResult(`📊 字段显示测试完成: ${passCount}/${totalTests} 通过 (${passRate}%)`, 'warning');
            }
        }

        function testStandardization() {
            showResult('<h4>🔄 测试字段标准化集成</h4>', 'info');
            
            if (typeof window.standardizeFieldsToApi === 'function') {
                showResult('✅ 全局字段标准化函数可用', 'success');
                
                // 测试标准化效果
                const testData = { 
                    carTypeId: 5, 
                    drivingRegionId: 1,
                    vehicleType: '5座车',
                    region: 'KL'
                };
                
                try {
                    const standardized = window.standardizeFieldsToApi(testData, 'test');
                    showResult(`🔄 标准化测试: ${JSON.stringify(testData)} → ${JSON.stringify(standardized)}`, 'info');
                    
                    if (standardized.car_type_id && standardized.driving_region_id) {
                        showResult('✅ 字段标准化正常工作', 'success');
                    } else {
                        showResult('⚠️ 字段标准化可能有问题', 'warning');
                    }
                } catch (error) {
                    showResult(`❌ 字段标准化测试失败: ${error.message}`, 'error');
                }
            } else {
                showResult('⚠️ 全局字段标准化函数不可用', 'warning');
            }
        }

        function testEditField(orderIndex, fieldName) {
            showResult(`🖱️ 用户点击编辑: 订单${orderIndex} 字段${fieldName}`, 'info');
            
            if (testManager && testManager.editField) {
                try {
                    testManager.editField(orderIndex, fieldName);
                    showResult(`✅ 编辑功能调用成功`, 'success');
                } catch (error) {
                    showResult(`❌ 编辑功能调用失败: ${error.message}`, 'error');
                }
            } else {
                showResult('❌ 编辑功能不可用', 'error');
            }
        }

        function testDropdownStyling() {
            showResult('<h4>🎨 测试下拉菜单样式</h4>', 'info');
            
            if (testManager && testManager.createSelectEditor) {
                try {
                    // 创建测试下拉菜单
                    const vehicleSelect = testManager.createSelectEditor('vehicleType', '5 Seater');
                    const regionSelect = testManager.createSelectEditor('drivingRegion', 'Kl/selangor');
                    
                    showResult('✅ 下拉编辑器创建成功', 'success');
                    showResult(`📋 车型选项数量: ${vehicleSelect.options.length}`, 'info');
                    showResult(`📋 区域选项数量: ${regionSelect.options.length}`, 'info');
                    
                    // 检查样式
                    const hasCorrectStyling = vehicleSelect.style.border.includes('#007bff') && 
                                            vehicleSelect.style.borderRadius === '4px';
                    
                    if (hasCorrectStyling) {
                        showResult('✅ 下拉菜单样式正确', 'success');
                    } else {
                        showResult('⚠️ 下拉菜单样式可能需要调整', 'warning');
                    }
                    
                } catch (error) {
                    showResult(`❌ 下拉菜单测试失败: ${error.message}`, 'error');
                }
            } else {
                showResult('❌ 下拉编辑器功能不可用', 'error');
            }
        }

        function generateTestReport() {
            showResult('<h4>📋 测试报告生成</h4>', 'info');
            
            const results = document.querySelectorAll('.test-result');
            const successCount = document.querySelectorAll('.test-result.success').length;
            const errorCount = document.querySelectorAll('.test-result.error').length;
            const warningCount = document.querySelectorAll('.test-result.warning').length;
            
            const totalResults = successCount + errorCount + warningCount;
            const successRate = Math.round((successCount / totalResults) * 100);
            
            let reportClass = 'info';
            if (successRate >= 90) reportClass = 'success';
            else if (successRate >= 70) reportClass = 'warning';
            else reportClass = 'error';
            
            showResult(`
                <h4>📊 最终测试报告</h4>
                <ul>
                    <li>✅ 成功: ${successCount} 项</li>
                    <li>❌ 失败: ${errorCount} 项</li>
                    <li>⚠️ 警告: ${warningCount} 项</li>
                    <li><strong>总体成功率: ${successRate}%</strong></li>
                </ul>
                <p><strong>修复状态: ${successRate >= 80 ? '✅ 修复成功' : '❌ 需要进一步调试'}</strong></p>
            `, reportClass);
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            displayTestData();
            
            setTimeout(() => {
                showResult('<h3>🔄 系统初始化中...</h3>', 'info');
                
                // 等待系统完全加载
                let attempts = 0;
                const checkSystem = () => {
                    attempts++;
                    if (window.OTA && window.OTA.multiOrderManager) {
                        showResult('✅ 系统已就绪，可以开始测试', 'success');
                        showResult('👆 点击"运行完整测试"按钮开始验证修复效果', 'info');
                    } else if (attempts < 10) {
                        setTimeout(checkSystem, 500);
                    } else {
                        showResult('❌ 系统加载超时，请检查依赖项', 'error');
                    }
                };
                checkSystem();
            }, 1000);
        });
    </script>
</body>
</html>
