<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Order Final Fixes Test</title>
    
    <!-- 引入必需的CSS文件 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <link rel="stylesheet" href="css/multi-order/mobile.css">
    
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .test-button {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: var(--brand-darker);
        }
        
        .test-results {
            margin-top: 10px;
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 4px;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Multi-Order Final Fixes Test</h1>
    
    <div class="test-section">
        <h2>测试用例</h2>
        <p>测试多订单系统的最终修复：</p>
        <ul>
            <li>✅ 区域字段显示 - 已修复</li>
            <li>🔄 车型字段显示名称而不是ID</li>
            <li>🔄 下拉菜单颜色主题一致性</li>
            <li>✅ 下拉菜单自动收缩</li>
            <li>🔄 Gemini字段映射完整性</li>
            <li>🔄 Null/false值不显示</li>
        </ul>
        
        <button class="test-button" onclick="testMultiOrderSystem()">启动多订单测试</button>
        <button class="test-button" onclick="testFieldMapping()">测试字段映射</button>
        <button class="test-button" onclick="testValueProcessing()">测试值处理</button>
        <button class="test-button" onclick="clearConsole()">清空控制台</button>
        
        <div class="test-results">
            <h3>测试结果：</h3>
            <div class="console-output" id="console-output"></div>
        </div>
    </div>

    <!-- 隐藏的多订单面板 -->
    <div class="multi-order-panel hidden" id="multiOrderPanel">
        <div class="multi-order-content">
            <button class="multi-order-close-btn" onclick="closeMultiOrder()">×</button>
            <div class="multi-order-container" id="multiOrderContainer">
                <!-- 多订单内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入必需的JS文件 -->
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>
    
    <script>
        // 控制台输出函数
        function logToConsole(message, type = 'info') {
            const console = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const typePrefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : type === 'success' ? '✅ SUCCESS' : 'ℹ️ INFO';
            console.innerHTML += `[${timestamp}] ${typePrefix}: ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }
        
        // 初始化系统数据
        function initializeSystemData() {
            logToConsole('初始化系统数据...');
            
            // 创建模拟的系统数据
            const systemData = {
                carTypes: [
                    { id: 1, name: 'Standard' },
                    { id: 2, name: 'Premium' },
                    { id: 3, name: 'Luxury' },
                    { id: 4, name: 'MPV' },
                    { id: 5, name: 'Van' }
                ],
                drivingRegions: [
                    { id: 1, name: 'Kuala Lumpur' },
                    { id: 2, name: 'Selangor' },
                    { id: 3, name: 'Penang' },
                    { id: 4, name: 'Johor' },
                    { id: 5, name: 'Perak' }
                ],
                subCategories: [
                    { id: 1, name: '机场接送' },
                    { id: 2, name: '包车服务' },
                    { id: 3, name: '商务用车' }
                ],
                languages: [
                    { id: 1, name: '中文' },
                    { id: 2, name: '英文' },
                    { id: 3, name: '马来文' }
                ]
            };
            
            // 设置到全局状态
            if (typeof getAppState === 'function') {
                getAppState().set('systemData', systemData);
                logToConsole('系统数据已设置到AppState', 'success');
            }
            
            // 设置到API服务
            if (typeof getApiService === 'function') {
                const apiService = getApiService();
                if (apiService) {
                    apiService.staticData = systemData;
                    logToConsole('系统数据已设置到ApiService', 'success');
                }
            }
        }
        
        // 测试订单数据 - 包含更多Gemini可能返回的字段
        const testOrders = [
            {
                customer_name: "张三",
                customer_contact: "0123456789",
                customer_email: "<EMAIL>",
                pickup: "KLIA International Airport",
                dropoff: "Sunway Pyramid Mall",
                pickup_date: "2024-01-15",
                pickup_time: "14:30",
                price: 80.50,
                ota_channel: "Booking.com",
                ota_reference_number: "BK12345",
                vehicle_type: 2, // Premium ID
                driving_region: 1, // Kuala Lumpur ID
                passenger_count: 2,
                luggage_count: 3,
                meet_and_greet: true,
                baby_chair: false,
                tour_guide: null,
                wheelchair_accessible: false,
                flight_info: "MH123",
                extra_requirement: "需要中文司机",
                preferred_language: "zh",
                sub_category_id: 1,
                payment_method: "cash",
                urgency: "normal",
                hotel_name: "Sunway Resort Hotel",
                airport_terminal: "Terminal 1",
                gate_number: "A12",
                booking_reference: "HTL789",
                company_name: "ABC旅游公司",
                contact_person: "李经理",
                emergency_contact: "0198765432",
                driver_notes: "客户可能延误",
                internal_notes: "VIP客户",
                is_round_trip: true,
                needs_return: false
            },
            {
                customer_name: "李四",
                customerContact: "0198765432",
                email: "<EMAIL>",
                pickupLocation: "One Utama Shopping Centre",
                destination: "KLIA2 Terminal",
                date: "2024-01-16",
                time: "09:15",
                otaPrice: 65.00,
                ota: "Agoda",
                reference: "AG67890",
                carType: 1, // Standard ID
                drivingRegionId: 2, // Selangor ID
                passengers: 1,
                luggage: 1,
                meetAndGreet: false,
                babyChair: true,
                tourGuide: false,
                wheelchairAccessible: true,
                flight: "AK456",
                remark: "需要儿童座椅和轮椅设施",
                language: "en",
                serviceType: 1,
                paymentMethod: "card",
                urgency: "high",
                hotelName: "The Gardens Hotel",
                airportTerminal: "KLIA2",
                gateNumber: "L8",
                bookingReference: "HTL456",
                companyName: "XYZ Business Solutions",
                contactPerson: "王助理",
                emergencyContact: "0176543210",
                driverNotes: "客户要求准时",
                internalNotes: "企业客户",
                isRoundTrip: false,
                needsReturn: true
            },
            {
                customer_name: "陈五",
                customer_contact: "0134567890",
                customer_email: "<EMAIL>",
                pickup: "Petronas Twin Towers",
                dropoff: "Kuala Lumpur Sentral",
                pickup_date: "2024-01-17", 
                pickup_time: "16:45",
                price: 25.00,
                ota_channel: "Grab",
                ota_reference_number: "GR34567",
                vehicle_type: 3, // Luxury ID
                driving_region: 1, // Kuala Lumpur ID
                passenger_count: 1,
                luggage_count: 0,
                meet_and_greet: false,
                baby_chair: false,
                tour_guide: true,
                wheelchair_accessible: false,
                flight_info: "",
                extra_requirement: "需要英文导游",
                preferred_language: "en",
                sub_category_id: 2, // 包车服务
                payment_method: "online",
                urgency: "low",
                hotel_name: "",
                airport_terminal: "",
                gate_number: "",
                booking_reference: "TOUR123",
                company_name: "",
                contact_person: "",
                emergency_contact: "",
                driver_notes: "",
                internal_notes: "城市观光",
                is_round_trip: false,
                needs_return: false
            }
        ];
        
        // 测试多订单系统
        function testMultiOrderSystem() {
            logToConsole('开始测试多订单系统...');
            
            try {
                // 初始化系统数据
                initializeSystemData();
                
                // 创建多订单管理器实例
                const manager = new MultiOrderManagerV2();
                logToConsole('多订单管理器创建成功', 'success');
                
                // 显示多订单面板
                manager.showMultiOrderPanel(testOrders);
                logToConsole('多订单面板已显示', 'success');
                
                // 等待一段时间后检查字段
                setTimeout(() => {
                    checkFieldValues();
                }, 1000);
                
            } catch (error) {
                logToConsole(`多订单系统测试失败: ${error.message}`, 'error');
                console.error('Multi-order test error:', error);
            }
        }
        
        // 检查字段值
        function checkFieldValues() {
            logToConsole('检查字段值...');
            
            try {
                const allFields = document.querySelectorAll('.grid-item[data-field]');
                logToConsole(`找到 ${allFields.length} 个字段`);
                
                // 按字段类型分组统计
                const fieldTypes = {};
                const fieldValues = {};
                
                allFields.forEach((field, index) => {
                    const fieldName = field.getAttribute('data-field');
                    const value = field.querySelector('.grid-value')?.textContent?.trim() || '';
                    
                    if (!fieldTypes[fieldName]) {
                        fieldTypes[fieldName] = 0;
                        fieldValues[fieldName] = [];
                    }
                    fieldTypes[fieldName]++;
                    if (value) {
                        fieldValues[fieldName].push(value);
                    }
                });
                
                // 报告字段统计
                logToConsole('=== 字段统计 ===');
                Object.entries(fieldTypes).forEach(([fieldName, count]) => {
                    const values = fieldValues[fieldName];
                    const sampleValue = values.length > 0 ? values[0] : '(空值)';
                    logToConsole(`${fieldName}: ${count}个实例, 示例值: "${sampleValue}"`);
                });
                
                // 检查特定字段类型
                logToConsole('=== 特定字段检查 ===');
                
                // 检查车型字段
                const vehicleFields = document.querySelectorAll('[data-field="vehicleType"], [data-field="carType"]');
                vehicleFields.forEach((field, index) => {
                    const value = field.querySelector('.grid-value')?.textContent || '';
                    if (/^\d+$/.test(value)) {
                        logToConsole(`⚠️ 车型字段 ${index + 1} 显示ID: ${value}`, 'warn');
                    } else if (value) {
                        logToConsole(`✅ 车型字段 ${index + 1} 显示正确: ${value}`, 'success');
                    }
                });
                
                // 检查区域字段
                const regionFields = document.querySelectorAll('[data-field="drivingRegion"], [data-field="drivingRegionId"]');
                regionFields.forEach((field, index) => {
                    const value = field.querySelector('.grid-value')?.textContent || '';
                    if (/^\d+$/.test(value)) {
                        logToConsole(`⚠️ 区域字段 ${index + 1} 显示ID: ${value}`, 'warn');
                    } else if (value) {
                        logToConsole(`✅ 区域字段 ${index + 1} 显示正确: ${value}`, 'success');
                    }
                });
                
                // 检查OTA字段（应该为ota而不是otaChannel）
                const otaFields = document.querySelectorAll('[data-field="ota"]');
                otaFields.forEach((field, index) => {
                    const value = field.querySelector('.grid-value')?.textContent || '';
                    logToConsole(`OTA字段 ${index + 1}: "${value}"`);
                    if (value) {
                        logToConsole(`✅ OTA字段显示正确: ${value}`, 'success');
                    }
                });
                
                // 检查移除的字段不应该出现
                const removedFields = ['currency', 'arrivalTime', 'departureTime', 'duration', 'distance', 'otaChannel'];
                removedFields.forEach(fieldName => {
                    const field = document.querySelector(`[data-field="${fieldName}"]`);
                    if (field) {
                        logToConsole(`⚠️ 已移除的字段 ${fieldName} 仍在显示`, 'warn');
                    } else {
                        logToConsole(`✅ 已移除字段 ${fieldName} 正确隐藏`, 'success');
                    }
                });
                
                // 检查布尔字段
                const booleanFields = document.querySelectorAll('[data-field="meetAndGreet"], [data-field="babyChair"], [data-field="tourGuide"], [data-field="wheelchairAccessible"], [data-field="isRoundTrip"], [data-field="needsReturn"]');
                booleanFields.forEach((field, index) => {
                    const fieldName = field.getAttribute('data-field');
                    const value = field.querySelector('.grid-value')?.textContent || '';
                    if (value === 'true' || value === 'false') {
                        logToConsole(`⚠️ 布尔字段 ${fieldName} 显示原始值: ${value}`, 'warn');
                    } else if (value) {
                        logToConsole(`✅ 布尔字段 ${fieldName} 显示正确: ${value}`, 'success');
                    }
                });
                
                // 检查是否有空值显示
                allFields.forEach((field, index) => {
                    const value = field.querySelector('.grid-value')?.textContent?.trim() || '';
                    const fieldName = field.getAttribute('data-field');
                    if (value === 'null' || value === 'undefined' || value === 'false') {
                        logToConsole(`⚠️ 字段 ${fieldName} 显示空值: "${value}"`, 'warn');
                    }
                });
                
                logToConsole('=== 字段检查完成 ===');
                
            } catch (error) {
                logToConsole(`字段检查失败: ${error.message}`, 'error');
            }
        }
        
        // 测试字段映射
        function testFieldMapping() {
            logToConsole('测试字段映射...');
            
            try {
                initializeSystemData();
                const manager = new MultiOrderManagerV2();
                
                // 测试各种字段值
                const testCases = [
                    { field: 'vehicleType', value: 2, expected: 'Premium' },
                    { field: 'drivingRegion', value: 1, expected: 'Kuala Lumpur' },
                    { field: 'meetAndGreet', value: true, expected: '接机服务' },
                    { field: 'babyChair', value: false, expected: '' },
                    { field: 'customerName', value: null, expected: '' },
                    { field: 'paymentMethod', value: 'cash', expected: '现金' }
                ];
                
                testCases.forEach(testCase => {
                    const result = manager.processFieldValue(testCase.value, testCase.field);
                    const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
                    logToConsole(`${status} ${testCase.field}: ${testCase.value} -> "${result}" (期望: "${testCase.expected}")`);
                });
                
            } catch (error) {
                logToConsole(`字段映射测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试值处理
        function testValueProcessing() {
            logToConsole('测试值处理逻辑...');
            
            try {
                initializeSystemData();
                const manager = new MultiOrderManagerV2();
                
                // 测试空值和布尔值处理
                const nullValues = [null, undefined, '', false];
                const trueValues = [true];
                const idValues = [1, 2, 3, '1', '2'];
                
                nullValues.forEach(value => {
                    const result = manager.processFieldValue(value, 'testField');
                    const status = result === '' ? '✅ PASS' : '❌ FAIL';
                    logToConsole(`${status} Null处理: ${value} -> "${result}"`);
                });
                
                trueValues.forEach(value => {
                    const result = manager.processFieldValue(value, 'meetAndGreet');
                    const status = result === '接机服务' ? '✅ PASS' : '❌ FAIL';
                    logToConsole(`${status} Boolean处理: ${value} -> "${result}"`);
                });
                
                idValues.forEach(value => {
                    const result = manager.processFieldValue(value, 'vehicleType');
                    const isName = !(/^\d+$/.test(result));
                    const status = isName ? '✅ PASS' : '❌ FAIL';
                    logToConsole(`${status} ID转名称: ${value} -> "${result}"`);
                });
                
            } catch (error) {
                logToConsole(`值处理测试失败: ${error.message}`, 'error');
            }
        }
        
        // 关闭多订单面板
        function closeMultiOrder() {
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.add('hidden');
                logToConsole('多订单面板已关闭');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            logToConsole('测试页面加载完成');
            logToConsole('请点击测试按钮开始测试');
        });
    </script>
</body>
</html>
