<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强多订单界面测试 - 15%缩减 + 色块区分</title>
    
    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    
    <style>
        /* 测试页面专用样式 */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .test-subtitle {
            font-size: 1.1rem;
            color: #718096;
            margin-bottom: 16px;
        }
        
        .feature-list {
            display: flex;
            justify-content: center;
            gap: 24px;
            flex-wrap: wrap;
            margin-bottom: 24px;
        }
        
        .feature-item {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }
        
        .multi-order-demo {
            background: #f7fafc;
            border-radius: 12px;
            padding: 16px;
            border: 2px solid #e2e8f0;
        }
        
        /* 模拟多订单面板样式 */
        .demo-panel {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 24px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .demo-content {
            padding: 16px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">增强多订单界面测试</h1>
            <p class="test-subtitle">测试15%高度缩减 + 色块区分 + 新字段映射</p>
            
            <div class="feature-list">
                <div class="feature-item">📏 高度缩减15%</div>
                <div class="feature-item">🎨 色块区分内容</div>
                <div class="feature-item">🌐 OTA渠道映射</div>
                <div class="feature-item">🚗 驾驶区域显示</div>
                <div class="feature-item">🗣️ 需求语言字段</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="test-btn" onclick="showEnhancedMultiOrder()">显示增强多订单面板</button>
            <button class="test-btn" onclick="generateTestData()">生成测试数据</button>
            <button class="test-btn" onclick="clearDemo()">清空演示</button>
        </div>
        
        <div class="multi-order-demo">
            <div class="demo-panel">
                <div class="demo-header">增强多订单卡片演示</div>
                <div class="demo-content">
                    <div id="orderCardsContainer" class="multi-order-list">
                        <p style="text-align: center; color: #718096; padding: 40px;">
                            点击"显示增强多订单面板"查看效果
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/multi-order-manager-v2.js"></script>
    
    <script>
        // 测试用的增强订单数据
        const enhancedTestOrders = [
            {
                customer_name: "张先生",
                customer_contact: "12345678901",
                pickup: "吉隆坡国际机场 T1",
                dropoff: "武吉免登购物中心",
                pickup_date: "2025-08-07",
                pickup_time: "14:30",
                ota_price: 85,
                currency: "MYR",
                _otaChannel: "Ctrip West Malaysia",
                vehicle_type: "sedan",
                driving_region: "airport",
                preferred_language: "zh-cn"
            },
            {
                customer_name: "李女士",
                customer_contact: "98765432109",
                pickup: "双威金字塔购物中心",
                dropoff: "吉隆坡中央车站",
                pickup_date: "2025-08-07",
                pickup_time: "16:00",
                ota_price: 45,
                currency: "MYR",
                _otaChannel: "携程专车",
                vehicle_type: "suv",
                driving_region: "city",
                preferred_language: "english"
            },
            {
                customer_name: "王先生",
                customer_contact: "13579246810",
                pickup: "云顶高原",
                dropoff: "吉隆坡国际机场 T2",
                pickup_date: "2025-08-08",
                pickup_time: "09:15",
                ota_price: 120,
                currency: "MYR",
                _otaChannel: "Klook West Malaysia",
                vehicle_type: "mpv",
                driving_region: "highway",
                preferred_language: "malay"
            },
            {
                customer_name: "Raj Kumar",
                customer_contact: "11223344556",
                pickup: "Batu Caves",
                dropoff: "Pavilion KL",
                pickup_date: "2025-08-08",
                pickup_time: "11:45",
                ota_price: 65,
                currency: "MYR",
                _otaChannel: "GMH Sabah",
                vehicle_type: "luxury",
                driving_region: "suburban",
                preferred_language: "tamil"
            },
            {
                customer_name: "Sarah Johnson",
                customer_contact: "99887766554",
                pickup: "Mid Valley Megamall",
                dropoff: "Petronas Twin Towers",
                pickup_date: "2025-08-08",
                pickup_time: "19:30",
                ota_price: 35,
                currency: "MYR",
                _otaChannel: "Fliggy",
                vehicle_type: "economy",
                driving_region: "downtown",
                preferred_language: "en"
            },
            {
                customer_name: "田中太郎",
                customer_contact: "55443322110",
                pickup: "Sunway Lagoon",
                dropoff: "One Utama",
                pickup_date: "2025-08-09",
                pickup_time: "13:20",
                ota_price: 55,
                currency: "MYR",
                _otaChannel: "Kkday",
                vehicle_type: "standard",
                driving_region: "outskirts",
                preferred_language: "japanese"
            }
        ];
        
        // 多订单管理器实例
        let multiOrderManager = null;
        
        function initializeManager() {
            if (!multiOrderManager) {
                multiOrderManager = new MultiOrderManagerV2();
                console.log('增强多订单管理器已初始化');
            }
        }
        
        function showEnhancedMultiOrder() {
            initializeManager();
            
            const container = document.getElementById('orderCardsContainer');
            const html = multiOrderManager.generateOrderListHTML(enhancedTestOrders);
            
            container.innerHTML = html;
            
            // 添加展示动画
            const cards = container.querySelectorAll('.order-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }
        
        function generateTestData() {
            const randomOrders = [];
            const names = ['陈先生', 'Siti Rahman', 'Kumar Singh', 'Emily Watson', '김민수', 'Tanaka San'];
            const locations = ['KLCC', 'Bukit Bintang', 'Sunway', 'Mid Valley', 'Mont Kiara', 'Bangsar'];
            // 基于ota-channel-mapping.js的真实OTA渠道
            const otas = [
                'Klook West Malaysia', 'Klook Singapore', 'Kkday', 'Ctrip West Malaysia', 'Ctrip API', 
                '携程专车', '携程商铺 - CN', 'Fliggy', 'Traveloka', 'Heycar', 'Mozio',
                'GMH Sabah', 'GMH Terry', 'GMH Ashley', 'SMW Eric', 'SMW Wilson',
                'booking', 'agoda', 'expedia', 'airbnb', 'trip'
            ];
            const vehicles = ['sedan', 'suv', 'mpv', 'luxury', 'economy', 'premium'];
            const regions = ['city', 'airport', 'downtown', 'suburban', 'highway', 'outskirts'];
            const languages = ['zh-cn', 'english', 'malay', 'tamil', 'korean', 'japanese'];
            
            for (let i = 0; i < 4; i++) {
                randomOrders.push({
                    customer_name: names[Math.floor(Math.random() * names.length)],
                    customer_contact: `1${Math.floor(Math.random() * 1000000000).toString().padStart(9, '0')}`,
                    pickup: locations[Math.floor(Math.random() * locations.length)] + ' 购物中心',
                    dropoff: locations[Math.floor(Math.random() * locations.length)] + ' 酒店',
                    pickup_date: '2025-08-' + String(Math.floor(Math.random() * 7) + 7).padStart(2, '0'),
                    pickup_time: Math.floor(Math.random() * 24).toString().padStart(2, '0') + ':' + 
                                ['00', '15', '30', '45'][Math.floor(Math.random() * 4)],
                    ota_price: Math.floor(Math.random() * 150) + 30,
                    currency: 'MYR',
                    _otaChannel: otas[Math.floor(Math.random() * otas.length)],
                    vehicle_type: vehicles[Math.floor(Math.random() * vehicles.length)],
                    driving_region: regions[Math.floor(Math.random() * regions.length)],
                    preferred_language: languages[Math.floor(Math.random() * languages.length)]
                });
            }
            
            initializeManager();
            const container = document.getElementById('orderCardsContainer');
            const html = multiOrderManager.generateOrderListHTML(randomOrders);
            container.innerHTML = html;
            
            console.log('生成随机测试数据:', randomOrders);
        }
        
        function clearDemo() {
            const container = document.getElementById('orderCardsContainer');
            container.innerHTML = '<p style="text-align: center; color: #718096; padding: 40px;">演示已清空</p>';
        }
        
        // 模拟创建订单功能
        window.createOrder = function(index) {
            alert(`创建订单 ${index + 1} - 功能正常！`);
        };
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('增强多订单界面测试页面已加载');
        });
    </script>
</body>
</html>
