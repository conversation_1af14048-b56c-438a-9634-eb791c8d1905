<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单编辑修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .editable-field {
            display: inline-block;
            padding: 8px 12px;
            margin: 5px;
            background: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            min-width: 120px;
            transition: all 0.2s ease;
        }
        .editable-field:hover {
            background: #dee2e6;
            border-color: #007bff;
        }
        .editable-field.editing {
            background: #fff;
            border-color: #007bff;
        }
        .readonly-field {
            background: #f8f9fa !important;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6 !important;
        }
        .readonly-field:hover {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        .grid-value {
            display: inline-block;
            font-weight: 500;
        }
        .field-editor {
            width: 100%;
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            background: white;
        }
        .field-editor:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            margin-right: 8px;
        }
        .field-row {
            margin: 10px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <h1>下拉菜单编辑修复测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">🔧 测试说明</h2>
        <p><strong>功能变更</strong>：OTA渠道字段已设为只读，不支持单独编辑</p>
        <p><strong>修复内容</strong>：</p>
        <ul>
            <li>OTA渠道字段设为只读状态，需要通过批量操作设置</li>
            <li>车型和驾驶区域字段保持可编辑</li>
            <li>使用 change 事件替代 blur 事件（针对可编辑下拉字段）</li>
            <li>添加事件冒泡阻止</li>
            <li>延迟 blur 事件绑定</li>
            <li>改进下拉菜单样式和可见性</li>
        </ul>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 测试订单卡片</h2>
        <div class="order-card" data-index="0">
            <h4>测试订单 1</h4>
            
            <div class="field-row">
                <span class="field-label">客户姓名:</span>
                <div class="editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(0, 'customerName')">
                    <span class="grid-value">张三</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">联系方式:</span>
                <div class="editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(0, 'customerContact')">
                    <span class="grid-value">12345678901</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">OTA渠道:</span>
                <div class="editable-field readonly-field" data-field="otaChannel" title="OTA渠道需要通过批量操作设置，不支持单独编辑">
                    <span class="grid-value">Agoda</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">车型:</span>
                <div class="editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(0, 'vehicleType')">
                    <span class="grid-value">5 Seater</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">驾驶区域:</span>
                <div class="editable-field" data-field="drivingRegion" onclick="window.OTA.multiOrderManager.editField(0, 'drivingRegion')">
                    <span class="grid-value">市区</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">价格:</span>
                <div class="editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(0, 'price')">
                    <span class="grid-value">150</span>
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🧪 测试操作</h2>
        <button onclick="testDropdownFields()">测试下拉字段编辑</button>
        <button onclick="testTextFields()">测试文本字段编辑</button>
        <button onclick="resetTestData()">重置测试数据</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="testResult"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 测试日志</h2>
        <div id="testLog" class="log-area"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/multi-order-manager-v2.js"></script>

    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function showResult(message, type = 'info') {
            const container = document.getElementById('testResult');
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // 测试下拉字段编辑
        function testDropdownFields() {
            log('开始测试下拉字段编辑...');

            const editableDropdownFields = ['vehicleType', 'drivingRegion'];
            const readonlyFields = ['otaChannel'];
            let testResults = [];

            // 测试可编辑的下拉字段
            editableDropdownFields.forEach(fieldName => {
                try {
                    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
                    if (fieldElement) {
                        log(`测试可编辑字段: ${fieldName}`);

                        // 模拟点击编辑
                        if (window.OTA && window.OTA.multiOrderManager) {
                            window.OTA.multiOrderManager.editField(0, fieldName);
                            testResults.push(`✅ ${fieldName}: 编辑器创建成功`);
                        } else {
                            testResults.push(`❌ ${fieldName}: 多订单管理器未加载`);
                        }
                    } else {
                        testResults.push(`❌ ${fieldName}: 字段元素未找到`);
                    }
                } catch (error) {
                    testResults.push(`❌ ${fieldName}: ${error.message}`);
                    log(`字段 ${fieldName} 测试失败: ${error.message}`, 'error');
                }
            });

            // 测试只读字段
            readonlyFields.forEach(fieldName => {
                try {
                    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
                    if (fieldElement) {
                        log(`测试只读字段: ${fieldName}`);

                        // 模拟点击编辑，应该被阻止
                        if (window.OTA && window.OTA.multiOrderManager) {
                            window.OTA.multiOrderManager.editField(0, fieldName);
                            testResults.push(`✅ ${fieldName}: 正确阻止编辑（只读字段）`);
                        } else {
                            testResults.push(`❌ ${fieldName}: 多订单管理器未加载`);
                        }
                    } else {
                        testResults.push(`❌ ${fieldName}: 字段元素未找到`);
                    }
                } catch (error) {
                    testResults.push(`❌ ${fieldName}: ${error.message}`);
                    log(`字段 ${fieldName} 测试失败: ${error.message}`, 'error');
                }
            });

            showResult('下拉字段测试结果:<br>' + testResults.join('<br>'), 'info');
            log('下拉字段编辑测试完成');
        }

        // 测试文本字段编辑
        function testTextFields() {
            log('开始测试文本字段编辑...');
            
            const textFields = ['customerName', 'customerContact', 'price'];
            let testResults = [];
            
            textFields.forEach(fieldName => {
                try {
                    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
                    if (fieldElement) {
                        log(`测试字段: ${fieldName}`);
                        
                        // 模拟点击编辑
                        if (window.OTA && window.OTA.multiOrderManager) {
                            window.OTA.multiOrderManager.editField(0, fieldName);
                            testResults.push(`✅ ${fieldName}: 编辑器创建成功`);
                        } else {
                            testResults.push(`❌ ${fieldName}: 多订单管理器未加载`);
                        }
                    } else {
                        testResults.push(`❌ ${fieldName}: 字段元素未找到`);
                    }
                } catch (error) {
                    testResults.push(`❌ ${fieldName}: ${error.message}`);
                    log(`字段 ${fieldName} 测试失败: ${error.message}`, 'error');
                }
            });
            
            showResult('文本字段测试结果:<br>' + testResults.join('<br>'), 'info');
            log('文本字段编辑测试完成');
        }

        // 重置测试数据
        function resetTestData() {
            log('重置测试数据...');
            
            // 模拟订单数据
            if (window.OTA && window.OTA.multiOrderManager) {
                window.OTA.multiOrderManager.state = window.OTA.multiOrderManager.state || {};
                window.OTA.multiOrderManager.state.parsedOrders = [{
                    customer_name: '张三',
                    customer_contact: '12345678901',
                    _otaChannel: 'agoda',
                    vehicle_type: '5 Seater',
                    driving_region: '市区',
                    ota_price: 150
                }];
                
                log('✅ 测试数据已重置');
                showResult('✅ 测试数据已重置', 'success');
            } else {
                log('❌ 多订单管理器未加载', 'error');
                showResult('❌ 多订单管理器未加载', 'error');
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('测试页面加载完成');
            log('开始初始化测试环境...');
            
            // 初始化必要的全局对象
            if (!window.OTA) {
                window.OTA = {};
            }
            
            // 等待脚本加载
            setTimeout(() => {
                if (window.OTA.multiOrderManager) {
                    log('✅ 多订单管理器已加载');
                    resetTestData();
                } else {
                    log('⚠️ 多订单管理器未加载，某些测试可能失败', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
