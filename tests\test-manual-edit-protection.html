<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动编辑保护机制测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .price-input-group { margin: 15px 0; }
        input, select { margin: 5px; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        button { margin: 5px; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; }
        .primary { background: #007bff; color: white; }
        .secondary { background: #6c757d; color: white; }
        
        /* 价格转换显示样式 */
        .price-conversion-display {
            margin-top: 10px;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }
        .price-conversion-display.manual-edit-mode {
            background: #fff3cd; 
            border-color: #ffc107;
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
        }
        .conversion-info { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
        .conversion-arrow { color: #6c757d; font-weight: bold; }
        .conversion-rate { color: #6c757d; font-size: 12px; margin-bottom: 8px; }
        .conversion-controls { text-align: right; }
        .reset-exchange-btn {
            background: #17a2b8; color: white; border: none;
            padding: 4px 12px; border-radius: 4px; font-size: 12px;
            cursor: pointer; transition: background-color 0.2s;
        }
        .reset-exchange-btn:hover { background: #138496; }
        .manual-edit-indicator {
            position: absolute; top: -8px; right: -8px;
            background: #ffc107; color: #000; font-size: 10px;
            padding: 2px 6px; border-radius: 8px; z-index: 10;
            pointer-events: none; animation: pulse 1.5s infinite;
        }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
    </style>
</head>
<body>
    <h1>手动编辑保护机制测试</h1>
    <p>测试价格字段的手动编辑检测和汇率转换保护功能</p>

    <!-- 模拟表单 -->
    <div class="test-case">
        <h3>测试表单</h3>
        <div class="price-input-group" id="otaPriceGroup" style="position: relative;">
            <label>OTA价格:</label>
            <input type="number" id="otaPrice" step="0.01" placeholder="输入价格">
            
            <label>货币:</label>
            <select id="currency">
                <option value="MYR">MYR - 马来西亚令吉</option>
                <option value="USD">USD - 美元</option>
                <option value="SGD">SGD - 新币</option>
                <option value="CNY">CNY - 人民币</option>
            </select>
        </div>

        <div class="test-controls">
            <button class="primary" onclick="simulateGeminiInput()">模拟Gemini填充 (USD 100)</button>
            <button class="secondary" onclick="simulateGeminiInput2()">模拟Gemini填充 (CNY 500)</button>
            <button class="secondary" onclick="clearFields()">清空字段</button>
        </div>
    </div>

    <div class="test-case">
        <h3>测试步骤</h3>
        <ol>
            <li>点击"模拟Gemini填充"按钮，观察汇率转换显示</li>
            <li>手动编辑价格字段，观察手动编辑指示器和转换显示变化</li>
            <li>等待2秒后失焦，观察是否自动退出手动编辑模式</li>
            <li>在手动编辑模式下，点击"重置汇率转换"按钮</li>
            <li>切换货币类型，测试不同场景下的行为</li>
        </ol>
    </div>

    <button onclick="runAutomatedTest()">运行自动化测试</button>
    <div id="results"></div>

    <script>
        // 模拟日志记录器
        function getLogger() {
            return {
                log: (message, level, data) => {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                },
                logError: (message, error) => {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        }

        // 模拟货币转换器
        class MockCurrencyConverter {
            constructor() {
                this.exchangeRates = {
                    'CNY': 0.615,
                    'USD': 4.3,
                    'SGD': 3.4,
                    'MYR': 1.0
                };
            }

            convertToMYR(amount, fromCurrency) {
                const rate = this.exchangeRates[fromCurrency.toUpperCase()] || 1;
                const convertedAmount = Math.round(amount * rate * 100) / 100;
                const needsConversion = fromCurrency.toUpperCase() !== 'MYR';

                return {
                    originalAmount: amount,
                    originalCurrency: fromCurrency.toUpperCase(),
                    convertedAmount: convertedAmount,
                    convertedCurrency: 'MYR',
                    exchangeRate: rate,
                    needsConversion: needsConversion
                };
            }

            formatPrice(amount, currency = 'MYR') {
                const symbols = { 'MYR': 'RM', 'CNY': '￥', 'USD': '$', 'SGD': 'S$' };
                const symbol = symbols[currency] || currency;
                return `${symbol} ${amount.toFixed(2)}`;
            }
        }

        // 模拟价格管理器
        class MockPriceManager {
            constructor() {
                this.elements = {
                    otaPrice: document.getElementById('otaPrice'),
                    currency: document.getElementById('currency')
                };
                this.currencyConverter = new MockCurrencyConverter();
                this.manualEditMode = false;
                this.isAutoFilling = false;
                this.editTimeout = null;
                this.lastManualValue = null;

                this.init();
            }

            init() {
                this.createPriceConversionDisplay();
                this.setupPriceValidation();
            }

            createPriceConversionDisplay() {
                const priceGroup = document.querySelector('.price-input-group');
                if (!priceGroup || priceGroup.querySelector('.price-conversion-display')) {
                    return;
                }

                const conversionDisplay = document.createElement('div');
                conversionDisplay.className = 'price-conversion-display';
                conversionDisplay.innerHTML = `
                    <div class="conversion-info">
                        <div class="original-price"></div>
                        <div class="conversion-arrow">→</div>
                        <div class="converted-price"></div>
                    </div>
                    <div class="conversion-rate"></div>
                    <div class="conversion-controls">
                        <button type="button" class="reset-exchange-btn" onclick="priceManager.resetManualEditModeAndUpdateConversion()">
                            重置汇率转换
                        </button>
                    </div>
                `;
                conversionDisplay.style.display = 'none';
                priceGroup.appendChild(conversionDisplay);
            }

            enterManualEditMode() {
                if (!this.manualEditMode) {
                    this.manualEditMode = true;
                    this.lastManualValue = this.elements.otaPrice?.value || null;
                    this.addManualEditIndicator();
                    getLogger().log('进入手动编辑模式', 'info', { currentValue: this.lastManualValue });
                }
            }

            exitManualEditMode() {
                if (this.manualEditMode) {
                    this.manualEditMode = false;
                    this.lastManualValue = null;
                    if (this.editTimeout) {
                        clearTimeout(this.editTimeout);
                        this.editTimeout = null;
                    }
                    this.removeManualEditIndicator();
                    getLogger().log('退出手动编辑模式', 'info');
                }
            }

            setAutoFillingState(isAutoFilling) {
                this.isAutoFilling = isAutoFilling;
                getLogger().log(`自动填充状态: ${isAutoFilling}`, 'debug');
            }

            addManualEditIndicator() {
                const priceGroup = document.querySelector('#otaPriceGroup');
                if (priceGroup && !priceGroup.querySelector('.manual-edit-indicator')) {
                    const indicator = document.createElement('div');
                    indicator.className = 'manual-edit-indicator';
                    indicator.innerHTML = '✏️ 手动编辑中';
                    priceGroup.appendChild(indicator);
                }
            }

            removeManualEditIndicator() {
                const indicator = document.querySelector('.manual-edit-indicator');
                if (indicator) indicator.remove();
            }

            resetManualEditModeAndUpdateConversion() {
                if (this.manualEditMode) {
                    this.exitManualEditMode();
                    this.updatePriceConversion(true);
                    getLogger().log('用户手动重置编辑模式并恢复汇率转换', 'info');
                }
            }

            updatePriceConversion(force = false) {
                if (!force && this.manualEditMode && !this.isAutoFilling) {
                    getLogger().log('手动编辑模式：跳过汇率转换显示更新', 'debug');
                    return;
                }

                const priceInput = this.elements.otaPrice;
                const currencySelect = this.elements.currency;
                const conversionDisplay = document.querySelector('.price-conversion-display');

                if (!priceInput || !currencySelect || !conversionDisplay) return;

                const amount = parseFloat(priceInput.value);
                const fromCurrency = currencySelect.value;

                if (!amount || amount <= 0 || !fromCurrency) {
                    conversionDisplay.style.display = 'none';
                    return;
                }

                const conversionResult = this.currencyConverter.convertToMYR(amount, fromCurrency);

                if (conversionResult.needsConversion) {
                    const originalPrice = this.currencyConverter.formatPrice(amount, fromCurrency);
                    const convertedPrice = this.currencyConverter.formatPrice(conversionResult.convertedAmount, 'MYR');

                    conversionDisplay.querySelector('.original-price').textContent = `原价 ${originalPrice}`;
                    conversionDisplay.querySelector('.converted-price').textContent = `转换后价格 ${convertedPrice}`;
                    conversionDisplay.querySelector('.conversion-rate').textContent = `汇率: 1 ${fromCurrency} = ${conversionResult.exchangeRate} MYR`;

                    const resetButton = conversionDisplay.querySelector('.reset-exchange-btn');
                    if (resetButton) {
                        resetButton.style.display = this.manualEditMode ? 'inline-block' : 'none';
                    }

                    if (this.manualEditMode) {
                        conversionDisplay.classList.add('manual-edit-mode');
                    } else {
                        conversionDisplay.classList.remove('manual-edit-mode');
                    }

                    conversionDisplay.style.display = 'block';
                } else {
                    conversionDisplay.style.display = 'none';
                }
            }

            setupPriceValidation() {
                if (this.elements.otaPrice) {
                    this.elements.otaPrice.addEventListener('focus', () => {
                        if (!this.isAutoFilling) {
                            setTimeout(() => {
                                if (!this.isAutoFilling) {
                                    this.enterManualEditMode();
                                }
                            }, 100);
                        }
                    });

                    this.elements.otaPrice.addEventListener('keydown', (event) => {
                        if (!this.isAutoFilling) {
                            const inputKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
                            const isDigitKey = /^[0-9]$/.test(event.key);
                            const isSpecialKey = ['.', ',', '-'].includes(event.key);
                            
                            if (isDigitKey || isSpecialKey || inputKeys.includes(event.key)) {
                                this.enterManualEditMode();
                            }
                        }
                    });

                    this.elements.otaPrice.addEventListener('paste', () => {
                        if (!this.isAutoFilling) {
                            this.enterManualEditMode();
                        }
                    });

                    this.elements.otaPrice.addEventListener('input', () => {
                        if (!this.manualEditMode || this.isAutoFilling) {
                            this.updatePriceConversion();
                        }
                    });

                    this.elements.otaPrice.addEventListener('blur', () => {
                        if (this.manualEditMode) {
                            this.editTimeout = setTimeout(() => {
                                this.exitManualEditMode();
                                this.updatePriceConversion(true);
                            }, 2000);
                        }
                    });
                }

                if (this.elements.currency) {
                    this.elements.currency.addEventListener('change', () => {
                        if (!this.manualEditMode) {
                            this.updatePriceConversion();
                        }
                    });
                }
            }

            simulateAutoFill(price, currency) {
                this.setAutoFillingState(true);
                this.elements.otaPrice.value = price;
                this.elements.currency.value = currency;
                this.updatePriceConversion();
                setTimeout(() => this.setAutoFillingState(false), 50);
            }
        }

        // 创建价格管理器实例
        let priceManager;
        document.addEventListener('DOMContentLoaded', () => {
            priceManager = new MockPriceManager();
        });

        // 测试功能
        function simulateGeminiInput() {
            if (priceManager) {
                priceManager.simulateAutoFill(100, 'USD');
            }
        }

        function simulateGeminiInput2() {
            if (priceManager) {
                priceManager.simulateAutoFill(500, 'CNY');
            }
        }

        function clearFields() {
            document.getElementById('otaPrice').value = '';
            document.getElementById('currency').value = 'MYR';
            const conversionDisplay = document.querySelector('.price-conversion-display');
            if (conversionDisplay) {
                conversionDisplay.style.display = 'none';
            }
            if (priceManager) {
                priceManager.exitManualEditMode();
            }
        }

        function runAutomatedTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>自动化测试结果</h2>';

            const testResults = [];

            // 测试1：自动填充不应触发手动编辑模式
            clearFields();
            setTimeout(() => {
                simulateGeminiInput();
                setTimeout(() => {
                    const manualIndicator = document.querySelector('.manual-edit-indicator');
                    if (!manualIndicator) {
                        testResults.push({
                            name: "自动填充测试",
                            status: "success",
                            message: "✅ 自动填充未触发手动编辑模式"
                        });
                    } else {
                        testResults.push({
                            name: "自动填充测试",
                            status: "error", 
                            message: "❌ 自动填充错误触发了手动编辑模式"
                        });
                    }

                    // 测试2：手动输入应触发手动编辑模式
                    const priceInput = document.getElementById('otaPrice');
                    priceInput.focus();
                    priceInput.value = '150';
                    priceInput.dispatchEvent(new Event('input'));

                    setTimeout(() => {
                        const manualIndicator2 = document.querySelector('.manual-edit-indicator');
                        if (manualIndicator2) {
                            testResults.push({
                                name: "手动编辑检测",
                                status: "success",
                                message: "✅ 手动编辑成功触发编辑模式"
                            });
                        } else {
                            testResults.push({
                                name: "手动编辑检测",
                                status: "error",
                                message: "❌ 手动编辑未能触发编辑模式"
                            });
                        }

                        // 显示结果
                        testResults.forEach(result => {
                            const div = document.createElement('div');
                            div.className = `test-case ${result.status}`;
                            div.innerHTML = `<h3>${result.name}</h3><p>${result.message}</p>`;
                            resultsDiv.appendChild(div);
                        });
                    }, 200);
                }, 200);
            }, 100);
        }
    </script>
</body>
</html>