<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店知识库调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .log-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔍 酒店知识库调试测试</h1>
    
    <div class="test-container">
        <h2>测试控制</h2>
        <button onclick="testJSONLoad()">测试JSON文件加载</button>
        <button onclick="testKnowledgeBase()">测试知识库功能</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="status" class="status info">
            准备就绪，点击按钮开始测试
        </div>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <div id="logOutput" class="log-output">等待测试开始...</div>
    </div>

    <script>
        let logOutput = document.getElementById('logOutput');
        let statusDiv = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            logOutput.textContent += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function clearLog() {
            logOutput.textContent = '';
            setStatus('日志已清空', 'info');
        }

        async function testJSONLoad() {
            log('开始测试JSON文件加载...', 'info');
            setStatus('正在测试JSON文件加载...', 'info');
            
            try {
                const response = await fetch('./hotels_by_region.json');
                log(`HTTP响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('JSON文件解析成功', 'success');
                log(`总酒店数: ${data.metadata?.total_hotels}`, 'info');
                log(`总区域数: ${data.metadata?.total_regions}`, 'info');
                log(`区域列表: ${Object.keys(data.hotels_by_region || {}).join(', ')}`, 'info');
                
                // 检查第一个区域的数据
                const firstRegionKey = Object.keys(data.hotels_by_region || {})[0];
                if (firstRegionKey) {
                    const firstRegionHotels = data.hotels_by_region[firstRegionKey];
                    log(`${firstRegionKey}区域酒店数量: ${firstRegionHotels.length}`, 'info');
                    
                    if (firstRegionHotels.length > 0) {
                        const firstHotel = firstRegionHotels[0];
                        log(`第一个酒店: ${firstHotel.chinese_name} → ${firstHotel.english_name}`, 'info');
                    }
                }
                
                setStatus('JSON文件加载测试完成', 'success');
                
            } catch (error) {
                log(`JSON文件加载失败: ${error.message}`, 'error');
                setStatus('JSON文件加载失败', 'error');
            }
        }

        async function testKnowledgeBase() {
            log('开始测试酒店知识库功能...', 'info');
            setStatus('正在测试酒店知识库功能...', 'info');
            
            try {
                // 模拟知识库构建
                log('加载JSON数据...', 'info');
                const response = await fetch('./hotels_by_region.json');
                const data = await response.json();
                
                log('构建搜索索引...', 'info');
                const chineseToEnglishMap = new Map();
                let processedCount = 0;
                
                Object.values(data.hotels_by_region || {}).forEach(hotels => {
                    hotels.forEach(hotel => {
                        const chineseName = hotel.chinese_name;
                        const englishName = hotel.english_name;
                        const region = hotel.region;
                        
                        if (chineseName && englishName) {
                            chineseToEnglishMap.set(chineseName, {
                                english: englishName,
                                region: region,
                                source: 'knowledge_base'
                            });
                            processedCount++;
                        }
                    });
                });
                
                log(`索引构建完成，处理了 ${processedCount} 个酒店`, 'success');
                log(`索引大小: ${chineseToEnglishMap.size}`, 'info');
                
                // 测试查询功能
                log('测试查询功能...', 'info');
                const testQueries = ['1 City酒店', '香格里拉酒店', '希尔顿酒店', '万豪酒店', '不存在的酒店'];
                
                testQueries.forEach(query => {
                    const result = chineseToEnglishMap.get(query);
                    if (result) {
                        log(`✓ 查询 "${query}": ${result.english} (${result.region})`, 'success');
                    } else {
                        log(`✗ 查询 "${query}": 未找到`, 'warning');
                    }
                });
                
                setStatus('酒店知识库功能测试完成', 'success');
                
            } catch (error) {
                log(`知识库测试失败: ${error.message}`, 'error');
                setStatus('知识库测试失败', 'error');
            }
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            log('页面加载完成，准备开始测试', 'info');
            setTimeout(() => {
                testJSONLoad();
            }, 1000);
        });
    </script>
</body>
</html>
