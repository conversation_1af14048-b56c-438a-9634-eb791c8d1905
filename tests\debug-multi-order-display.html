<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单显示字段调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .field-check {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
            border-left: 4px solid #007AFF;
        }
        .field-check.error {
            border-left-color: #F44336;
            background: #FFE8E8;
        }
        .field-check.success {
            border-left-color: #4CAF50;
            background: #E8F5E8;
        }
        pre {
            background: #f1f1f1;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 多订单显示字段调试工具</h1>
    
    <div class="debug-container">
        <h3>📊 字段映射检查</h3>
        <p>这个工具帮助调试多订单模式中字段名不匹配的问题</p>
        
        <div class="field-check">
            <strong>问题描述:</strong> 多订单卡片显示"未指定下车地址"，但编辑时能看到正确内容
        </div>
        
        <div class="field-check">
            <strong>原因分析:</strong> UI模板使用 <code>order.destination</code>，但实际数据字段是 <code>order.dropoff</code>
        </div>
        
        <div class="field-check success">
            <strong>修复方案:</strong> 已修改 js/multi-order-manager-v2.js 第987行，将 <code>order.destination</code> 改为 <code>order.dropoff</code>
        </div>
    </div>

    <div class="debug-container">
        <h3>🧪 模拟订单数据测试</h3>
        <button onclick="testFieldMapping()" style="padding: 12px 24px; background: #007AFF; color: white; border: none; border-radius: 6px; cursor: pointer;">测试字段映射</button>
        <div id="testResult"></div>
    </div>

    <div class="debug-container">
        <h3>📋 修复位置清单</h3>
        
        <div class="field-check success">
            ✅ <strong>位置1:</strong> js/multi-order-manager-v2.js:987
            <br>修复: <code>order.destination</code> → <code>order.dropoff</code>
        </div>
        
        <div class="field-check success">
            ✅ <strong>位置2:</strong> js/multi-order-manager-v2.js:1683  
            <br>修复: 验证逻辑中的字段名统一
        </div>
        
        <div class="field-check">
            📝 <strong>位置3:</strong> js/multi-order-manager-v2.js:2190
            <br>保留: 兼容性处理，先检查destination再检查dropoff
        </div>
        
        <div class="field-check">
            📝 <strong>位置4:</strong> js/multi-order-manager-v2.js:2227
            <br>保留: 编辑时同时更新两个字段，确保兼容性
        </div>
    </div>

    <script>
        function testFieldMapping() {
            const resultDiv = document.getElementById('testResult');
            
            // 模拟机场自动补全后的订单数据
            const testOrder = {
                customer_name: "测试客户",
                pickup: "吉隆坡国际机场KLIA",  // 自动补全的机场
                dropoff: "Shangri-La Hotel Kuala Lumpur",  // 正确的字段名
                pickup_date: "2024-01-15",
                pickup_time: "14:30",
                sub_category_id: 2,
                ota_price: 150
            };
            
            // 模拟UI渲染逻辑
            const displayLogic = {
                pickup: testOrder.pickup || '未指定上车地点',
                dropoff_old: testOrder.destination || '未指定下车地点', // 旧逻辑
                dropoff_new: testOrder.dropoff || '未指定下车地点'  // 修复后的逻辑
            };
            
            let output = '<h4>🧪 测试结果:</h4>\n\n';
            output += '<strong>模拟订单数据:</strong>\n';
            output += JSON.stringify(testOrder, null, 2) + '\n\n';
            
            output += '<strong>UI显示逻辑对比:</strong>\n';
            output += `上车地点: "${displayLogic.pickup}"\n`;
            output += `下车地点 (修复前): "${displayLogic.dropoff_old}" ❌\n`;
            output += `下车地点 (修复后): "${displayLogic.dropoff_new}" ✅\n\n`;
            
            if (displayLogic.dropoff_new !== '未指定下车地点') {
                output += '✅ 修复成功！现在能正确显示下车地点信息了\n';
            } else {
                output += '❌ 仍然存在问题，需要进一步检查\n';
            }
            
            resultDiv.innerHTML = '<pre>' + output + '</pre>';
        }
        
        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testFieldMapping();
        });
    </script>
</body>
</html>