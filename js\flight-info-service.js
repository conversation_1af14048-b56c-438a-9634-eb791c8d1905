/**
 * @OTA_CORE 航班信息服务
 * 🏷️ 标签: @OTA_FLIGHT_INFO_SERVICE
 * 📝 功能: 提供航班信息查询和验证服务
 * 🔗 集成: 与Netlify Functions配合使用
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 航班信息服务类
     */
    class FlightInfoService {
        constructor() {
            this.baseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
                ? 'http://localhost:3000' 
                : '';
            this.logger = window.getLogger?.() || console;
            this.initialized = false;
            this.init();
        }

        /**
         * 初始化服务
         */
        init() {
            try {
                this.initialized = true;
                this.logger.log('✈️ 航班信息服务已初始化', 'info', {
                    baseUrl: this.baseUrl,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                this.logger.logError('❌ 航班信息服务初始化失败:', error);
            }
        }

        /**
         * 查询航班信息
         * @param {string} flightNumber - 航班号
         * @param {string} date - 日期 (YYYY-MM-DD)
         * @returns {Promise<Object>} 航班信息
         */
        async getFlightInfo(flightNumber, date) {
            try {
                if (!flightNumber || !date) {
                    throw new Error('航班号和日期是必需的');
                }

                this.logger.log('🔍 查询航班信息', 'info', { flightNumber, date });

                const response = await fetch(`${this.baseUrl}/.netlify/functions/flight-info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        flight_number: flightNumber,
                        date: date
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                this.logger.log('✅ 航班信息查询成功', 'info', data);
                return data;

            } catch (error) {
                this.logger.logError('❌ 航班信息查询失败:', error);
                // 返回默认结构，避免破坏系统
                return {
                    success: false,
                    error: error.message,
                    data: null
                };
            }
        }

        /**
         * 验证航班号格式
         * @param {string} flightNumber - 航班号
         * @returns {boolean} 是否有效
         */
        validateFlightNumber(flightNumber) {
            if (!flightNumber || typeof flightNumber !== 'string') {
                return false;
            }

            // 基本的航班号格式验证 (航空公司代码 + 数字)
            const flightPattern = /^[A-Z]{2,3}[0-9]{1,4}$/i;
            return flightPattern.test(flightNumber.replace(/\s+/g, ''));
        }

        /**
         * 解析航班信息文本
         * @param {string} text - 包含航班信息的文本
         * @returns {Array} 解析出的航班信息数组
         */
        parseFlightInfoFromText(text) {
            if (!text || typeof text !== 'string') {
                return [];
            }

            try {
                // 航班号匹配模式
                const flightPattern = /([A-Z]{2,3}[0-9]{1,4})/gi;
                const flights = text.match(flightPattern) || [];

                // 时间匹配模式 (HH:MM)
                const timePattern = /(\d{1,2}:\d{2})/g;
                const times = text.match(timePattern) || [];

                // 日期匹配模式 (YYYY-MM-DD)
                const datePattern = /(\d{4}-\d{2}-\d{2})/g;
                const dates = text.match(datePattern) || [];

                return flights.map((flight, index) => ({
                    flightNumber: flight.toUpperCase(),
                    time: times[index] || null,
                    date: dates[index] || null,
                    isValid: this.validateFlightNumber(flight)
                }));

            } catch (error) {
                this.logger.logError('❌ 航班信息解析失败:', error);
                return [];
            }
        }

        /**
         * 获取服务状态
         * @returns {Object} 服务状态信息
         */
        getStatus() {
            return {
                initialized: this.initialized,
                baseUrl: this.baseUrl,
                timestamp: new Date().toISOString()
            };
        }
    }

    // 创建全局实例
    const flightInfoService = new FlightInfoService();

    // 导出到全局作用域
    window.getFlightInfoService = function() {
        return flightInfoService;
    };

    // 注册到OTA命名空间
    window.OTA.flightInfoService = flightInfoService;

    // 兼容性方法
    window.FlightInfoService = FlightInfoService;

    // 依赖容器注册
    if (window.DependencyContainer) {
        window.DependencyContainer.register('flightInfoService', () => flightInfoService);
    }

})();
