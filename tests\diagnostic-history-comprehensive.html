<!DOCTYPE html>
<html>
<head>
    <title>历史订单问题诊断报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .success { color: #28a745; background: #d4edda; border-color: #c3e6cb; }
        .error { color: #dc3545; background: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; border-radius: 4px; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        button:hover { background: #0056b3; }
        .diagnostic-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        h1, h2, h3 { color: #333; }
        .counter { font-weight: bold; color: #007bff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px 12px; text-align: left; border: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .code-block { background: #272822; color: #f8f8f2; padding: 15px; border-radius: 5px; overflow-x: auto; font-family: 'Courier New', monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 历史订单模块诊断报告</h1>
        <p>完整检查历史订单模块的显示和持久化问题</p>
        
        <div class="diagnostic-grid">
            <div class="section">
                <h2>📊 快速状态概览</h2>
                <div id="quickStatus">正在检查...</div>
            </div>
            
            <div class="section">
                <h2>🎯 操作面板</h2>
                <button onclick="runComprehensiveDiagnosis()">🔍 运行完整诊断</button>
                <button onclick="fixHistoryIssues()">🔧 修复问题</button>
                <button onclick="clearAnalysisResults()">🗑️ 清除结果</button>
                <button onclick="exportDiagnosticReport()">📋 导出报告</button>
            </div>
        </div>

        <div class="section">
            <h2>1. 💾 LocalStorage 数据检查</h2>
            <div id="storageAnalysis">等待检查...</div>
        </div>

        <div class="section">
            <h2>2. 🏗️ DOM 结构检查</h2>
            <div id="domAnalysis">等待检查...</div>
        </div>

        <div class="section">
            <h2>3. ⚙️ JavaScript 模块检查</h2>
            <div id="moduleAnalysis">等待检查...</div>
        </div>

        <div class="section">
            <h2>4. 🔄 功能测试</h2>
            <div id="functionalTest">等待检查...</div>
        </div>

        <div class="section">
            <h2>5. 🎨 样式和显示检查</h2>
            <div id="styleAnalysis">等待检查...</div>
        </div>

        <div class="section">
            <h2>6. 📋 详细报告</h2>
            <div id="detailedReport">等待生成...</div>
        </div>
    </div>

    <script>
        let diagnosticResults = {};
        
        // 快速状态检查
        function quickStatusCheck() {
            const issues = [];
            const checks = [];
            
            // 检查历史订单管理器
            if (typeof getOrderHistoryManager === 'function') {
                checks.push('✅ getOrderHistoryManager 函数存在');
            } else {
                issues.push('❌ getOrderHistoryManager 函数不存在');
            }
            
            // 检查 DOM 元素
            const historyPanel = document.getElementById('historyPanel');
            if (historyPanel) {
                checks.push('✅ historyPanel DOM 元素存在');
            } else {
                issues.push('❌ historyPanel DOM 元素不存在');
            }
            
            // 检查 localStorage
            const historyData = localStorage.getItem('ota_order_history');
            if (historyData) {
                checks.push('✅ localStorage 数据存在');
            } else {
                issues.push('⚠️ localStorage 数据为空');
            }
            
            const statusDiv = document.getElementById('quickStatus');
            let html = '<h3>概览状态</h3>';
            
            if (issues.length === 0) {
                html += '<div class="success"><span class="status-indicator status-ok"></span>系统状态正常</div>';
            } else {
                html += '<div class="error"><span class="status-indicator status-error"></span>发现问题</div>';
            }
            
            html += '<h4>检查项目:</h4><ul>';
            checks.forEach(check => html += `<li>${check}</li>`);
            html += '</ul>';
            
            if (issues.length > 0) {
                html += '<h4>问题项目:</h4><ul>';
                issues.forEach(issue => html += `<li>${issue}</li>`);
                html += '</ul>';
            }
            
            statusDiv.innerHTML = html;
        }

        // 1. LocalStorage 数据分析
        function analyzeStorage() {
            const container = document.getElementById('storageAnalysis');
            let html = '<h3>LocalStorage 分析结果</h3>';
            
            try {
                const historyData = localStorage.getItem('ota_order_history');
                
                if (!historyData) {
                    html += '<div class="warning">⚠️ 未找到历史订单数据 (key: ota_order_history)</div>';
                    html += '<p><strong>可能原因:</strong></p><ul>';
                    html += '<li>从未创建过订单</li>';
                    html += '<li>数据被清除</li>';
                    html += '<li>存储键名错误</li>';
                    html += '</ul>';
                } else {
                    try {
                        const parsed = JSON.parse(historyData);
                        html += '<div class="success">✅ 找到历史订单数据</div>';
                        
                        // 分析数据结构
                        if (Array.isArray(parsed)) {
                            html += '<div class="info">📋 数据格式: 旧版数组格式</div>';
                            html += `<p><strong>订单数量:</strong> ${parsed.length}</p>`;
                        } else if (typeof parsed === 'object') {
                            html += '<div class="info">📋 数据格式: 新版按用户分组格式</div>';
                            const userCount = Object.keys(parsed).length;
                            let totalOrders = 0;
                            Object.values(parsed).forEach(userOrders => {
                                if (Array.isArray(userOrders)) {
                                    totalOrders += userOrders.length;
                                }
                            });
                            html += `<p><strong>用户数量:</strong> ${userCount}</p>`;
                            html += `<p><strong>总订单数:</strong> ${totalOrders}</p>`;
                            
                            // 显示用户详情
                            html += '<h4>用户详情:</h4><table>';
                            html += '<tr><th>用户邮箱</th><th>订单数</th><th>最后订单时间</th></tr>';
                            for (const [email, orders] of Object.entries(parsed)) {
                                if (Array.isArray(orders)) {
                                    const lastOrder = orders[0]; // 最新的订单
                                    const lastTime = lastOrder ? new Date(lastOrder.timestamp).toLocaleString() : 'N/A';
                                    html += `<tr><td>${email}</td><td>${orders.length}</td><td>${lastTime}</td></tr>`;
                                }
                            }
                            html += '</table>';
                        }
                        
                        // 显示原始数据（截取前1000字符）
                        const dataPreview = historyData.substring(0, 1000);
                        html += '<h4>数据预览:</h4>';
                        html += `<pre>${dataPreview}${historyData.length > 1000 ? '...' : ''}</pre>`;
                        html += `<p><strong>数据大小:</strong> ${Math.round(historyData.length / 1024)} KB</p>`;
                        
                    } catch (parseError) {
                        html += '<div class="error">❌ 数据解析失败</div>';
                        html += `<p><strong>错误:</strong> ${parseError.message}</p>`;
                        html += '<h4>原始数据:</h4>';
                        html += `<pre>${historyData}</pre>`;
                    }
                }
                
                // 检查其他相关的localStorage项
                html += '<h4>其他 LocalStorage 项:</h4><ul>';
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.includes('ota') || key.includes('order') || key.includes('history')) {
                        html += `<li><strong>${key}:</strong> ${localStorage.getItem(key).substring(0, 100)}...</li>`;
                    }
                }
                html += '</ul>';
                
            } catch (error) {
                html += `<div class="error">❌ 分析过程出错: ${error.message}</div>`;
            }
            
            container.innerHTML = html;
            diagnosticResults.storage = html;
        }

        // 2. DOM 结构分析
        function analyzeDOM() {
            const container = document.getElementById('domAnalysis');
            let html = '<h3>DOM 结构分析结果</h3>';
            
            const requiredElements = [
                'historyPanel',
                'historyListContainer',
                'closeHistoryBtn',
                'clearHistoryBtn',
                'exportHistoryBtn',
                'searchHistoryBtn',
                'resetSearchBtn',
                'listCount',
                'statTotal',
                'statToday',
                'statWeek',
                'statMonth'
            ];
            
            let foundCount = 0;
            html += '<table>';
            html += '<tr><th>元素ID</th><th>状态</th><th>可见性</th><th>位置</th></tr>';
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    foundCount++;
                    const computedStyle = window.getComputedStyle(element);
                    const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
                    const rect = element.getBoundingClientRect();
                    
                    html += `<tr>`;
                    html += `<td>${id}</td>`;
                    html += `<td><span class="status-indicator status-ok"></span>存在</td>`;
                    html += `<td>${isVisible ? '✅ 可见' : '❌ 隐藏'}</td>`;
                    html += `<td>${rect.width}x${rect.height} at (${Math.round(rect.left)}, ${Math.round(rect.top)})</td>`;
                    html += `</tr>`;
                } else {
                    html += `<tr>`;
                    html += `<td>${id}</td>`;
                    html += `<td><span class="status-indicator status-error"></span>缺失</td>`;
                    html += `<td>N/A</td>`;
                    html += `<td>N/A</td>`;
                    html += `</tr>`;
                }
            });
            html += '</table>';
            
            const successRate = Math.round((foundCount / requiredElements.length) * 100);
            if (successRate === 100) {
                html += '<div class="success">✅ 所有必需的DOM元素都存在</div>';
            } else {
                html += `<div class="error">❌ DOM元素完整性: ${successRate}% (${foundCount}/${requiredElements.length})</div>`;
            }
            
            // 检查历史面板的详细状态
            const historyPanel = document.getElementById('historyPanel');
            if (historyPanel) {
                html += '<h4>historyPanel 详细状态:</h4>';
                html += '<ul>';
                html += `<li><strong>类名:</strong> ${historyPanel.className}</li>`;
                html += `<li><strong>样式display:</strong> ${historyPanel.style.display}</li>`;
                html += `<li><strong>计算样式display:</strong> ${window.getComputedStyle(historyPanel).display}</li>`;
                html += `<li><strong>z-index:</strong> ${window.getComputedStyle(historyPanel).zIndex}</li>`;
                html += `<li><strong>位置:</strong> ${window.getComputedStyle(historyPanel).position}</li>`;
                html += '</ul>';
            }
            
            container.innerHTML = html;
            diagnosticResults.dom = html;
        }

        // 3. JavaScript 模块分析
        function analyzeModules() {
            const container = document.getElementById('moduleAnalysis');
            let html = '<h3>JavaScript 模块分析结果</h3>';
            
            const moduleChecks = [
                { name: 'getOrderHistoryManager', path: 'window.getOrderHistoryManager', type: 'function' },
                { name: 'OrderHistoryManager', path: 'window.OrderHistoryManager', type: 'function' },
                { name: 'OTA.orderHistoryManager', path: 'window.OTA?.orderHistoryManager', type: 'object' },
                { name: 'orderHistoryManager', path: 'window.orderHistoryManager', type: 'object' },
                { name: 'getLogger', path: 'window.getLogger', type: 'function' },
                { name: 'getAppState', path: 'window.getAppState', type: 'function' }
            ];
            
            html += '<table>';
            html += '<tr><th>模块/函数</th><th>状态</th><th>类型</th><th>详情</th></tr>';
            
            moduleChecks.forEach(check => {
                try {
                    const value = eval(check.path);
                    const actualType = typeof value;
                    
                    if (value) {
                        html += `<tr>`;
                        html += `<td>${check.name}</td>`;
                        html += `<td><span class="status-indicator status-ok"></span>存在</td>`;
                        html += `<td>${actualType}</td>`;
                        
                        if (actualType === 'function') {
                            html += `<td>函数长度: ${value.length} 参数</td>`;
                        } else if (actualType === 'object' && value.constructor) {
                            html += `<td>构造函数: ${value.constructor.name}</td>`;
                        } else {
                            html += `<td>${String(value).substring(0, 50)}...</td>`;
                        }
                        html += `</tr>`;
                    } else {
                        html += `<tr>`;
                        html += `<td>${check.name}</td>`;
                        html += `<td><span class="status-indicator status-warning"></span>null/undefined</td>`;
                        html += `<td>${actualType}</td>`;
                        html += `<td>-</td>`;
                        html += `</tr>`;
                    }
                } catch (error) {
                    html += `<tr>`;
                    html += `<td>${check.name}</td>`;
                    html += `<td><span class="status-indicator status-error"></span>错误</td>`;
                    html += `<td>error</td>`;
                    html += `<td>${error.message}</td>`;
                    html += `</tr>`;
                }
            });
            html += '</table>';
            
            // 测试历史管理器实例化
            try {
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    html += '<h4>历史管理器实例测试:</h4>';
                    html += '<ul>';
                    html += `<li><strong>实例类型:</strong> ${manager.constructor.name}</li>`;
                    html += `<li><strong>当前用户:</strong> ${manager.getCurrentUser ? manager.getCurrentUser() : 'N/A'}</li>`;
                    
                    if (manager.getHistory) {
                        const history = manager.getHistory();
                        html += `<li><strong>历史记录数:</strong> ${Array.isArray(history) ? history.length : 'N/A'}</li>`;
                    }
                    
                    html += '</ul>';
                }
            } catch (error) {
                html += `<div class="error">❌ 历史管理器实例化失败: ${error.message}</div>`;
            }
            
            container.innerHTML = html;
            diagnosticResults.modules = html;
        }

        // 4. 功能测试
        function functionalTest() {
            const container = document.getElementById('functionalTest');
            let html = '<h3>功能测试结果</h3>';
            
            const tests = [];
            
            // 测试1: 获取历史数据
            try {
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    const history = manager.getHistory();
                    tests.push({
                        name: '获取历史数据',
                        status: 'success',
                        result: `成功获取 ${history.length} 条记录`
                    });
                } else {
                    tests.push({
                        name: '获取历史数据',
                        status: 'error',
                        result: 'getOrderHistoryManager 函数不存在'
                    });
                }
            } catch (error) {
                tests.push({
                    name: '获取历史数据',
                    status: 'error',
                    result: error.message
                });
            }
            
            // 测试2: 渲染历史面板
            try {
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    if (manager.renderHistory) {
                        const history = manager.getHistory();
                        manager.renderHistory(history);
                        tests.push({
                            name: '渲染历史列表',
                            status: 'success',
                            result: '渲染函数执行成功'
                        });
                    } else {
                        tests.push({
                            name: '渲染历史列表',
                            status: 'error',
                            result: 'renderHistory 方法不存在'
                        });
                    }
                } else {
                    tests.push({
                        name: '渲染历史列表',
                        status: 'error',
                        result: 'getOrderHistoryManager 函数不存在'
                    });
                }
            } catch (error) {
                tests.push({
                    name: '渲染历史列表',
                    status: 'error',
                    result: error.message
                });
            }
            
            // 测试3: 显示历史面板
            try {
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    if (manager.showHistoryPanel) {
                        // 不实际显示，只检查方法存在
                        tests.push({
                            name: '显示历史面板方法',
                            status: 'success',
                            result: 'showHistoryPanel 方法存在'
                        });
                    } else {
                        tests.push({
                            name: '显示历史面板方法',
                            status: 'error',
                            result: 'showHistoryPanel 方法不存在'
                        });
                    }
                }
            } catch (error) {
                tests.push({
                    name: '显示历史面板方法',
                    status: 'error',
                    result: error.message
                });
            }
            
            // 测试4: 添加测试订单
            try {
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    if (manager.addOrder) {
                        const testOrderData = {
                            customerName: '诊断测试客户',
                            customerContact: '+60123456789',
                            pickup: '测试上车点',
                            destination: '测试目的地',
                            otaReferenceNumber: 'DIAG_' + Date.now()
                        };
                        
                        const orderId = 'DIAGNOSTIC_' + Date.now();
                        manager.addOrder(testOrderData, orderId, {
                            success: true,
                            message: '诊断测试订单'
                        });
                        
                        tests.push({
                            name: '添加测试订单',
                            status: 'success',
                            result: `成功添加测试订单: ${orderId}`
                        });
                    } else {
                        tests.push({
                            name: '添加测试订单',
                            status: 'error',
                            result: 'addOrder 方法不存在'
                        });
                    }
                }
            } catch (error) {
                tests.push({
                    name: '添加测试订单',
                    status: 'error',
                    result: error.message
                });
            }
            
            // 渲染测试结果
            html += '<table>';
            html += '<tr><th>测试项目</th><th>状态</th><th>结果</th></tr>';
            tests.forEach(test => {
                const statusIcon = test.status === 'success' ? 'status-ok' : 'status-error';
                const statusText = test.status === 'success' ? '✅ 通过' : '❌ 失败';
                html += `<tr>`;
                html += `<td>${test.name}</td>`;
                html += `<td><span class="status-indicator ${statusIcon}"></span>${statusText}</td>`;
                html += `<td>${test.result}</td>`;
                html += `</tr>`;
            });
            html += '</table>';
            
            const passedTests = tests.filter(t => t.status === 'success').length;
            const successRate = Math.round((passedTests / tests.length) * 100);
            
            if (successRate === 100) {
                html += '<div class="success">✅ 所有功能测试通过</div>';
            } else {
                html += `<div class="error">❌ 功能测试通过率: ${successRate}% (${passedTests}/${tests.length})</div>`;
            }
            
            container.innerHTML = html;
            diagnosticResults.functional = html;
        }

        // 5. 样式和显示分析
        function analyzeStyles() {
            const container = document.getElementById('styleAnalysis');
            let html = '<h3>样式和显示分析结果</h3>';
            
            const historyPanel = document.getElementById('historyPanel');
            if (!historyPanel) {
                html += '<div class="error">❌ historyPanel 元素不存在，无法分析样式</div>';
                container.innerHTML = html;
                return;
            }
            
            const computedStyle = window.getComputedStyle(historyPanel);
            const rect = historyPanel.getBoundingClientRect();
            
            html += '<h4>historyPanel 样式分析:</h4>';
            html += '<table>';
            html += '<tr><th>属性</th><th>值</th><th>分析</th></tr>';
            
            const styleChecks = [
                { prop: 'display', value: computedStyle.display, analysis: computedStyle.display === 'none' ? '❌ 隐藏' : '✅ 显示' },
                { prop: 'visibility', value: computedStyle.visibility, analysis: computedStyle.visibility === 'hidden' ? '❌ 不可见' : '✅ 可见' },
                { prop: 'opacity', value: computedStyle.opacity, analysis: parseFloat(computedStyle.opacity) === 0 ? '❌ 透明' : '✅ 不透明' },
                { prop: 'z-index', value: computedStyle.zIndex, analysis: computedStyle.zIndex === 'auto' ? '⚠️ 自动' : `✅ ${computedStyle.zIndex}` },
                { prop: 'position', value: computedStyle.position, analysis: computedStyle.position },
                { prop: 'width', value: `${rect.width}px`, analysis: rect.width > 0 ? '✅ 有宽度' : '❌ 无宽度' },
                { prop: 'height', value: `${rect.height}px`, analysis: rect.height > 0 ? '✅ 有高度' : '❌ 无高度' }
            ];
            
            styleChecks.forEach(check => {
                html += `<tr><td>${check.prop}</td><td>${check.value}</td><td>${check.analysis}</td></tr>`;
            });
            html += '</table>';
            
            // 检查CSS类
            html += '<h4>CSS 类分析:</h4>';
            html += `<p><strong>类列表:</strong> ${historyPanel.className || '(无)'}</p>`;
            
            if (historyPanel.classList.contains('hidden')) {
                html += '<div class="warning">⚠️ 面板包含 "hidden" 类</div>';
            }
            
            // 检查父元素
            html += '<h4>父元素检查:</h4>';
            let parent = historyPanel.parentElement;
            let level = 1;
            while (parent && level <= 3) {
                const parentStyle = window.getComputedStyle(parent);
                html += `<p><strong>级别 ${level}:</strong> ${parent.tagName} - display: ${parentStyle.display}, overflow: ${parentStyle.overflow}</p>`;
                parent = parent.parentElement;
                level++;
            }
            
            container.innerHTML = html;
            diagnosticResults.styles = html;
        }

        // 运行完整诊断
        function runComprehensiveDiagnosis() {
            document.getElementById('quickStatus').innerHTML = '<div class="info">🔍 正在运行完整诊断...</div>';
            
            setTimeout(() => {
                quickStatusCheck();
                analyzeStorage();
                analyzeDOM();
                analyzeModules();
                functionalTest();
                analyzeStyles();
                generateDetailedReport();
            }, 100);
        }

        // 生成详细报告
        function generateDetailedReport() {
            const container = document.getElementById('detailedReport');
            let html = '<h3>详细诊断报告</h3>';
            
            // 计算总体健康度
            const issues = [];
            const recommendations = [];
            
            // 检查localStorage
            const historyData = localStorage.getItem('ota_order_history');
            if (!historyData) {
                issues.push('LocalStorage 中没有历史订单数据');
                recommendations.push('创建一个测试订单来初始化历史数据');
            }
            
            // 检查DOM
            const historyPanel = document.getElementById('historyPanel');
            if (!historyPanel) {
                issues.push('historyPanel DOM 元素缺失');
                recommendations.push('检查 index.html 文件中的历史面板HTML结构');
            }
            
            // 检查JavaScript
            if (typeof getOrderHistoryManager !== 'function') {
                issues.push('getOrderHistoryManager 函数不存在');
                recommendations.push('检查 order-history-manager.js 是否正确加载');
            }
            
            // 生成报告
            if (issues.length === 0) {
                html += '<div class="success">🎉 系统状态良好，未发现主要问题</div>';
            } else {
                html += `<div class="error">❌ 发现 ${issues.length} 个问题</div>`;
                
                html += '<h4>问题列表:</h4><ol>';
                issues.forEach(issue => html += `<li>${issue}</li>`);
                html += '</ol>';
                
                html += '<h4>建议修复步骤:</h4><ol>';
                recommendations.forEach(rec => html += `<li>${rec}</li>`);
                html += '</ol>';
            }
            
            // 系统信息
            html += '<h4>系统信息:</h4>';
            html += '<ul>';
            html += `<li><strong>浏览器:</strong> ${navigator.userAgent}</li>`;
            html += `<li><strong>当前时间:</strong> ${new Date().toLocaleString()}</li>`;
            html += `<li><strong>页面URL:</strong> ${window.location.href}</li>`;
            html += '</ul>';
            
            container.innerHTML = html;
        }

        // 修复问题
        function fixHistoryIssues() {
            let fixes = [];
            
            try {
                // 修复1: 初始化localStorage
                const historyData = localStorage.getItem('ota_order_history');
                if (!historyData) {
                    localStorage.setItem('ota_order_history', JSON.stringify({}));
                    fixes.push('✅ 初始化了 localStorage 历史数据');
                }
                
                // 修复2: 确保历史管理器实例存在
                if (typeof getOrderHistoryManager === 'function') {
                    const manager = getOrderHistoryManager();
                    fixes.push('✅ 历史管理器实例已创建');
                    
                    // 修复3: 添加一个示例订单（如果没有数据的话）
                    const history = manager.getHistory();
                    if (history.length === 0) {
                        const sampleOrder = {
                            customerName: '示例客户',
                            customerContact: '+60123456789',
                            pickup: '吉隆坡国际机场',
                            destination: '市中心酒店',
                            otaReferenceNumber: 'SAMPLE_' + Date.now(),
                            date: new Date().toISOString().split('T')[0],
                            time: '14:00'
                        };
                        
                        manager.addOrder(sampleOrder, 'SAMPLE_' + Date.now(), {
                            success: true,
                            message: '示例订单创建成功'
                        });
                        fixes.push('✅ 添加了示例订单数据');
                    }
                }
                
                // 修复4: 确保面板可见性
                const historyPanel = document.getElementById('historyPanel');
                if (historyPanel) {
                    historyPanel.style.zIndex = '9999';
                    fixes.push('✅ 设置了面板z-index');
                }
                
                alert('修复完成:\n\n' + fixes.join('\n'));
                
                // 重新运行诊断
                setTimeout(runComprehensiveDiagnosis, 500);
                
            } catch (error) {
                alert('修复过程中出现错误: ' + error.message);
            }
        }

        // 清除分析结果
        function clearAnalysisResults() {
            document.getElementById('storageAnalysis').innerHTML = '等待检查...';
            document.getElementById('domAnalysis').innerHTML = '等待检查...';
            document.getElementById('moduleAnalysis').innerHTML = '等待检查...';
            document.getElementById('functionalTest').innerHTML = '等待检查...';
            document.getElementById('styleAnalysis').innerHTML = '等待检查...';
            document.getElementById('detailedReport').innerHTML = '等待生成...';
            diagnosticResults = {};
        }

        // 导出诊断报告
        function exportDiagnosticReport() {
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                diagnosticResults: diagnosticResults
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `history-diagnostic-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载时运行快速检查
        document.addEventListener('DOMContentLoaded', quickStatusCheck);
        setTimeout(quickStatusCheck, 1000); // 确保其他脚本已加载
    </script>
</body>
</html>
