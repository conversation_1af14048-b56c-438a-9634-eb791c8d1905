/**
 * 工作区页面样式
 * 包含主要的工作区布局和状态栏
 */

/* =================================
   主要内容区
   ================================= */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
  padding-bottom: 80px; /* 为固定在底部的按键留出空间 */
}

/* =================================
   工作区
   ================================= */
.workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-4);
  min-height: 0;
  overflow: hidden;
}

/* =================================
   登录面板
   ================================= */
.login-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-8);
  border: 1px solid var(--border-color);
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-6);
  color: var(--text-primary);
  font-size: var(--font-size-2xl);
  font-weight: 700;
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-top: var(--spacing-6);
}

/* =================================
   状态栏
   ================================= */
.status-bar {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-2) var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  position: sticky;
  bottom: 0;
  z-index: var(--z-sticky);
}

.status-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.status-item.connected {
  color: var(--color-success);
}

.status-item.disconnected {
  color: var(--color-error);
}

.status-item.processing {
  color: var(--color-warning);
}

/* =================================
   模态框
   ================================= */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
  backdrop-filter: var(--blur-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  padding: var(--spacing-4);
}

.modal-content {
  background: var(--bg-tertiary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  padding: var(--spacing-6);
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

/* =================================
   切换开关
   ================================= */
.toggle-switch {
  position: relative;
  display: inline-block;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  width: 40px;
  height: 20px;
  background: var(--color-gray-300);
  border-radius: 20px;
  position: relative;
  transition: background var(--transition-normal);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-white);
  top: 2px;
  left: 2px;
  transition: transform var(--transition-normal);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

/* =================================
   加载状态
   ================================= */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

/* =================================
   移动端优化
   ================================= */
@media (max-width: 768px) {
  .main-content {
    padding-bottom: 90px; /* 移动端需要更多空间 */
  }
  
  .workspace {
    padding: var(--spacing-2);
  }
  
  .login-panel {
    padding: var(--spacing-4);
  }
  
  .login-card {
    padding: var(--spacing-6);
    max-width: none;
  }
  
  .status-bar {
    padding: var(--spacing-1) var(--spacing-2);
    flex-direction: column;
    gap: var(--spacing-1);
  }
  
  .status-info {
    flex-wrap: wrap;
    gap: var(--spacing-2);
    justify-content: center;
  }
  
  .modal {
    padding: var(--spacing-2);
  }
  
  .modal-content {
    max-width: none;
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-footer {
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .modal-body {
    padding: var(--spacing-4);
  }
  
  .modal-footer {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .workspace {
    padding: var(--mobile-ultra-md);
  }
  
  .status-bar {
    padding: var(--mobile-ultra-sm);
  }
  
  .status-info {
    gap: var(--mobile-ultra-md);
  }
  
  .status-item {
    font-size: var(--mobile-compact-xs);
  }
}

/* 
 * 动画定义已移至 components/buttons.css 
 * 避免重复定义
 */