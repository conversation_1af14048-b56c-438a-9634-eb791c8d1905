<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航班信息查询服务测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: #27ae60;
        }
        
        .btn-success:hover {
            background-color: #229954;
        }
        
        .btn-warning {
            background-color: #f39c12;
        }
        
        .btn-warning:hover {
            background-color: #e67e22;
        }
        
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .flight-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .flight-example {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .flight-example:hover {
            background: #d1ecf1;
        }
        
        .flight-info-container {
            margin-top: 8px;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f9f9f9;
            font-size: 13px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛫 航班信息查询服务测试</h1>
        
        <div id="serviceStatus" class="status info">
            正在初始化航班信息服务...
        </div>
        
        <!-- 基础功能测试 -->
        <div class="test-section">
            <h3>📋 基础功能测试</h3>
            <div class="input-group">
                <label for="flightInput">输入航班号：</label>
                <input type="text" id="flightInput" placeholder="例如：MH370, AK6285, CZ3021" />
            </div>
            
            <div class="flight-examples">
                <div class="flight-example" onclick="testFlight('MH370')">MH370</div>
                <div class="flight-example" onclick="testFlight('AK6285')">AK6285</div>
                <div class="flight-example" onclick="testFlight('CZ3021')">CZ3021</div>
                <div class="flight-example" onclick="testFlight('9W123')">9W123</div>
                <div class="flight-example" onclick="testFlight('SQ001')">SQ001</div>
                <div class="flight-example" onclick="testFlight('INVALID')">INVALID</div>
            </div>
            
            <button class="btn" onclick="manualQuery()">手动查询</button>
            <button class="btn btn-success" onclick="testValidation()">测试验证</button>
            <button class="btn btn-warning" onclick="clearCache()">清理缓存</button>
        </div>
        
        <!-- 多订单管理器集成测试 -->
        <div class="test-section">
            <h3>🔗 多订单管理器集成测试</h3>
            <div class="input-group">
                <label for="multiOrderFlightInput">航班号输入框（模拟多订单场景）：</label>
                <input type="text" id="multiOrderFlightInput" name="flight_number" placeholder="输入航班号测试自动查询功能" />
            </div>
            
            <div class="input-group">
                <label for="flightInfoInput">航班信息输入框：</label>
                <input type="text" id="flightInfoInput" placeholder="Flight information" />
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <h3>📊 测试日志</h3>
            <button class="btn" onclick="clearLog()">清理日志</button>
            <div id="testLog" class="log">测试日志将在这里显示...\n</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/flight-info-service.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>
    
    <script>
        // 测试脚本
        let testLog = document.getElementById('testLog');
        let serviceStatus = document.getElementById('serviceStatus');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(`[FLIGHT-TEST] ${message}`);
        }
        
        function clearLog() {
            testLog.textContent = '测试日志已清理...\n';
        }
        
        function updateServiceStatus(message, type = 'info') {
            serviceStatus.textContent = message;
            serviceStatus.className = `status ${type}`;
        }
        
        // 初始化检查
        function checkServiceAvailability() {
            log('🔍 检查航班信息服务可用性...');
            
            if (window.OTA && window.OTA.flightInfoService) {
                updateServiceStatus('✅ 航班信息服务已就绪', 'success');
                log('✅ FlightInfoService 服务可用');
                return true;
            } else {
                updateServiceStatus('❌ 航班信息服务不可用', 'error');
                log('❌ FlightInfoService 服务不可用');
                return false;
            }
        }
        
        // 测试航班号
        function testFlight(flightNumber) {
            document.getElementById('flightInput').value = flightNumber;
            manualQuery();
        }
        
        // 手动查询
        async function manualQuery() {
            const flightNumber = document.getElementById('flightInput').value.trim();
            if (!flightNumber) {
                log('⚠️ 请输入航班号');
                return;
            }
            
            log(`🔍 开始查询航班: ${flightNumber}`);
            
            if (!checkServiceAvailability()) {
                return;
            }
            
            try {
                const result = await window.OTA.flightInfoService.queryFlightInfo(flightNumber);
                log(`✅ 查询成功: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`❌ 查询失败: ${error.message}`);
            }
        }
        
        // 测试验证功能
        function testValidation() {
            log('🧪 开始验证功能测试...');
            
            const testCases = [
                { input: 'MH370', expected: true, desc: '标准格式' },
                { input: 'AK6285', expected: true, desc: '标准格式' },
                { input: 'CZ3021', expected: true, desc: '标准格式' },
                { input: '9W123', expected: true, desc: '数字开头' },
                { input: 'MH370/1', expected: true, desc: '分段航班' },
                { input: 'INVALID', expected: false, desc: '无效格式' },
                { input: '123', expected: false, desc: '纯数字' },
                { input: '', expected: false, desc: '空字符串' }
            ];
            
            if (!checkServiceAvailability()) {
                return;
            }
            
            testCases.forEach(testCase => {
                const result = window.OTA.flightInfoService.validateFlightNumber(testCase.input);
                const status = result === testCase.expected ? '✅' : '❌';
                log(`${status} ${testCase.desc}: "${testCase.input}" -> ${result} (期望: ${testCase.expected})`);
            });
            
            log('🧪 验证功能测试完成');
        }
        
        // 清理缓存
        function clearCache() {
            if (!checkServiceAvailability()) {
                return;
            }
            
            window.OTA.flightInfoService.clearCache();
            log('🗑️ 缓存已清理');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始初始化测试环境...');
            
            // 延迟检查服务可用性，确保所有脚本都已加载
            setTimeout(() => {
                checkServiceAvailability();
                
                // 测试多订单管理器集成
                if (window.OTA && window.OTA.multiOrderManager) {
                    log('✅ 多订单管理器可用，航班信息查询功能已集成');
                } else {
                    log('⚠️ 多订单管理器不可用');
                }
            }, 1000);
        });
    </script>
</body>
</html>
