/**
 * ============================================================================
 * 🚀 核心业务流程 - 历史管理器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 历史管理器 - 子层实现
 * @description 负责订单历史的保存和管理，从multi-order-manager-v2.js拆分而来
 * 
 * @businessFlow 订单历史管理
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理 → 订单管理 → API调用
 *     ↓
 * 【当前文件职责】保存到本地历史订单并持久化 - 本地处理
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：订单历史管理的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供历史管理服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/order-management-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 订单历史保存和持久化
 * - 🟢 历史数据查询和检索
 * - 🟢 数据备份和恢复
 * - 🟢 历史统计和分析
 * - 🟢 数据清理和维护
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地存储模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有历史数据格式
 * - 兼容现有的存储机制
 * - 保持向后兼容的查询接口
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程API（严格本地存储）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持数据完整性
 * - ✅ 保持现有的存储格式
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 历史管理器 - 子层实现
     */
    class HistoryManager {
        constructor() {
            this.logger = this.getLogger();
            
            // 存储配置
            this.config = {
                storageKey: 'ota_order_history',
                maxHistoryItems: 1000,
                autoCleanup: true,
                cleanupThreshold: 30, // 30天
                backupInterval: 24 * 60 * 60 * 1000 // 24小时
            };

            // 历史数据缓存
            this.historyCache = null;
            this.lastBackupTime = 0;
            
            // 初始化历史管理器
            this.initializeHistory();
            
            this.logger.log('历史管理器已初始化', 'info');
        }

        /**
         * 初始化历史管理器
         */
        initializeHistory() {
            try {
                // 加载现有历史数据
                this.loadHistoryFromStorage();
                
                // 设置自动清理
                if (this.config.autoCleanup) {
                    this.scheduleAutoCleanup();
                }
                
                this.logger.log('历史管理器初始化完成', 'success', { 
                    historyCount: this.historyCache?.length || 0 
                });

            } catch (error) {
                this.logger.log('历史管理器初始化失败', 'error', { error: error.message });
                this.historyCache = [];
            }
        }

        /**
         * 保存单个订单到历史
         * @param {object} order - 订单数据
         * @param {object} apiResult - API调用结果
         * @returns {Promise<object>} 保存结果
         */
        async saveOrder(order, apiResult = null) {
            try {
                this.logger.log('保存订单到历史', 'info', { 
                    customerName: order.customer_name 
                });

                const historyItem = this.createHistoryItem(order, apiResult, 'single');
                
                // 添加到历史缓存
                this.historyCache.unshift(historyItem);
                
                // 限制历史数量
                if (this.historyCache.length > this.config.maxHistoryItems) {
                    this.historyCache = this.historyCache.slice(0, this.config.maxHistoryItems);
                }

                // 保存到存储
                await this.saveHistoryToStorage();

                this.logger.log('订单历史保存成功', 'success', { 
                    historyId: historyItem.id 
                });

                return {
                    success: true,
                    historyId: historyItem.id,
                    historyItem: historyItem
                };

            } catch (error) {
                this.logger.log('订单历史保存失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 保存多个订单到历史
         * @param {array} orders - 订单数组
         * @param {object} batchResult - 批量处理结果
         * @returns {Promise<object>} 保存结果
         */
        async saveMultipleOrders(orders, batchResult = null) {
            try {
                this.logger.log('保存多个订单到历史', 'info', { 
                    orderCount: orders.length 
                });

                const historyItems = [];

                // 为每个订单创建历史项
                for (let i = 0; i < orders.length; i++) {
                    const order = orders[i];
                    const orderResult = batchResult?.results?.[i] || null;
                    
                    const historyItem = this.createHistoryItem(order, orderResult, 'multi', {
                        batchId: batchResult?.batchId || this.generateId(),
                        orderIndex: i,
                        totalOrders: orders.length
                    });
                    
                    historyItems.push(historyItem);
                }

                // 批量添加到历史缓存
                this.historyCache.unshift(...historyItems);
                
                // 限制历史数量
                if (this.historyCache.length > this.config.maxHistoryItems) {
                    this.historyCache = this.historyCache.slice(0, this.config.maxHistoryItems);
                }

                // 保存到存储
                await this.saveHistoryToStorage();

                this.logger.log('多订单历史保存成功', 'success', { 
                    savedCount: historyItems.length 
                });

                return {
                    success: true,
                    savedCount: historyItems.length,
                    historyItems: historyItems
                };

            } catch (error) {
                this.logger.log('多订单历史保存失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 创建历史项
         * @param {object} order - 订单数据
         * @param {object} apiResult - API结果
         * @param {string} type - 类型
         * @param {object} metadata - 元数据
         * @returns {object} 历史项
         */
        createHistoryItem(order, apiResult, type, metadata = {}) {
            return {
                id: this.generateId(),
                type: type,
                order: order,
                apiResult: apiResult,
                metadata: {
                    ...metadata,
                    createdAt: new Date().toISOString(),
                    channel: order.ota || 'unknown',
                    success: apiResult?.success || false
                },
                timestamp: Date.now()
            };
        }

        /**
         * 从存储加载历史数据
         */
        loadHistoryFromStorage() {
            try {
                const stored = localStorage.getItem(this.config.storageKey);
                if (stored) {
                    this.historyCache = JSON.parse(stored);
                    this.logger.log('历史数据加载成功', 'success', { 
                        count: this.historyCache.length 
                    });
                } else {
                    this.historyCache = [];
                    this.logger.log('没有找到历史数据，初始化空历史', 'info');
                }
            } catch (error) {
                this.logger.log('历史数据加载失败', 'error', { error: error.message });
                this.historyCache = [];
            }
        }

        /**
         * 保存历史数据到存储
         */
        async saveHistoryToStorage() {
            try {
                const dataToSave = JSON.stringify(this.historyCache);
                localStorage.setItem(this.config.storageKey, dataToSave);
                
                this.logger.log('历史数据保存成功', 'success', { 
                    count: this.historyCache.length 
                });

            } catch (error) {
                this.logger.log('历史数据保存失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 查询历史订单
         * @param {object} criteria - 查询条件
         * @returns {array} 匹配的历史订单
         */
        queryHistory(criteria = {}) {
            try {
                let results = [...this.historyCache];

                // 按类型过滤
                if (criteria.type) {
                    results = results.filter(item => item.type === criteria.type);
                }

                // 按渠道过滤
                if (criteria.channel) {
                    results = results.filter(item => item.metadata.channel === criteria.channel);
                }

                // 按时间范围过滤
                if (criteria.startDate || criteria.endDate) {
                    const startTime = criteria.startDate ? new Date(criteria.startDate).getTime() : 0;
                    const endTime = criteria.endDate ? new Date(criteria.endDate).getTime() : Date.now();
                    
                    results = results.filter(item => 
                        item.timestamp >= startTime && item.timestamp <= endTime
                    );
                }

                // 按客户名称搜索
                if (criteria.customerName) {
                    const searchTerm = criteria.customerName.toLowerCase();
                    results = results.filter(item => 
                        item.order.customer_name?.toLowerCase().includes(searchTerm)
                    );
                }

                // 限制结果数量
                if (criteria.limit && criteria.limit > 0) {
                    results = results.slice(0, criteria.limit);
                }

                this.logger.log('历史查询完成', 'info', { 
                    criteria,
                    resultCount: results.length 
                });

                return results;

            } catch (error) {
                this.logger.log('历史查询失败', 'error', { error: error.message });
                return [];
            }
        }

        /**
         * 清理过期历史数据
         */
        cleanupExpiredHistory() {
            try {
                const cutoffTime = Date.now() - (this.config.cleanupThreshold * 24 * 60 * 60 * 1000);
                const originalCount = this.historyCache.length;
                
                this.historyCache = this.historyCache.filter(item => item.timestamp > cutoffTime);
                
                const removedCount = originalCount - this.historyCache.length;
                
                if (removedCount > 0) {
                    this.saveHistoryToStorage();
                    this.logger.log('过期历史数据清理完成', 'info', { 
                        removedCount,
                        remainingCount: this.historyCache.length 
                    });
                }

            } catch (error) {
                this.logger.log('历史数据清理失败', 'error', { error: error.message });
            }
        }

        /**
         * 安排自动清理
         */
        scheduleAutoCleanup() {
            // 每24小时执行一次清理
            setInterval(() => {
                this.cleanupExpiredHistory();
            }, 24 * 60 * 60 * 1000);
        }

        /**
         * 生成唯一ID
         * @returns {string} 唯一ID
         */
        generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        /**
         * 获取历史统计信息
         * @returns {object} 统计信息
         */
        getHistoryStats() {
            const stats = {
                totalItems: this.historyCache.length,
                singleOrders: 0,
                multiOrders: 0,
                successfulOrders: 0,
                failedOrders: 0,
                channelStats: {},
                oldestItem: null,
                newestItem: null
            };

            if (this.historyCache.length > 0) {
                // 计算统计信息
                this.historyCache.forEach(item => {
                    if (item.type === 'single') stats.singleOrders++;
                    if (item.type === 'multi') stats.multiOrders++;
                    if (item.metadata.success) stats.successfulOrders++;
                    else stats.failedOrders++;

                    // 渠道统计
                    const channel = item.metadata.channel;
                    stats.channelStats[channel] = (stats.channelStats[channel] || 0) + 1;
                });

                // 最新和最旧的项目
                stats.newestItem = this.historyCache[0];
                stats.oldestItem = this.historyCache[this.historyCache.length - 1];
            }

            return stats;
        }

        /**
         * 导出历史数据
         * @param {object} options - 导出选项
         * @returns {string} 导出的JSON字符串
         */
        exportHistory(options = {}) {
            try {
                let dataToExport = this.historyCache;

                // 应用过滤条件
                if (options.startDate || options.endDate || options.channel) {
                    dataToExport = this.queryHistory(options);
                }

                const exportData = {
                    exportedAt: new Date().toISOString(),
                    version: '2.0.0',
                    totalItems: dataToExport.length,
                    data: dataToExport
                };

                this.logger.log('历史数据导出完成', 'success', { 
                    itemCount: dataToExport.length 
                });

                return JSON.stringify(exportData, null, 2);

            } catch (error) {
                this.logger.log('历史数据导出失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 导入历史数据
         * @param {string} jsonData - JSON格式的历史数据
         * @param {object} options - 导入选项
         * @returns {Promise<object>} 导入结果
         */
        async importHistory(jsonData, options = {}) {
            try {
                const importData = JSON.parse(jsonData);
                
                if (!importData.data || !Array.isArray(importData.data)) {
                    throw new Error('无效的导入数据格式');
                }

                const importCount = importData.data.length;
                
                if (options.merge) {
                    // 合并模式：添加到现有历史
                    this.historyCache.unshift(...importData.data);
                } else {
                    // 替换模式：替换现有历史
                    this.historyCache = importData.data;
                }

                // 限制历史数量
                if (this.historyCache.length > this.config.maxHistoryItems) {
                    this.historyCache = this.historyCache.slice(0, this.config.maxHistoryItems);
                }

                // 保存到存储
                await this.saveHistoryToStorage();

                this.logger.log('历史数据导入成功', 'success', { 
                    importCount,
                    totalCount: this.historyCache.length 
                });

                return {
                    success: true,
                    importCount: importCount,
                    totalCount: this.historyCache.length
                };

            } catch (error) {
                this.logger.log('历史数据导入失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 清空历史数据
         * @param {object} options - 清空选项
         * @returns {Promise<object>} 清空结果
         */
        async clearHistory(options = {}) {
            try {
                const originalCount = this.historyCache.length;

                if (options.backup) {
                    // 备份后清空
                    const backupData = this.exportHistory();
                    const backupKey = `${this.config.storageKey}_backup_${Date.now()}`;
                    localStorage.setItem(backupKey, backupData);
                    
                    this.logger.log('历史数据已备份', 'info', { backupKey });
                }

                this.historyCache = [];
                await this.saveHistoryToStorage();

                this.logger.log('历史数据清空完成', 'success', { 
                    clearedCount: originalCount 
                });

                return {
                    success: true,
                    clearedCount: originalCount
                };

            } catch (error) {
                this.logger.log('历史数据清空失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 获取历史数据
         * @param {object} options - 获取选项
         * @returns {array} 历史数据数组
         */
        getHistory(options = {}) {
            if (options.criteria) {
                return this.queryHistory(options.criteria);
            }
            
            const limit = options.limit || 50;
            return this.historyCache.slice(0, limit);
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const historyManager = new HistoryManager();

    // 导出到全局作用域
    window.HistoryManager = HistoryManager;
    window.OTA.HistoryManager = HistoryManager;
    window.OTA.historyManager = historyManager;

    console.log('✅ HistoryManager (子层实现) 已加载');

})();
