<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射测试</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
</head>
<body>
    <div class="container">
        <h1>字段映射测试</h1>
        
        <button id="testFieldMapping" onclick="testFieldMapping()">测试字段映射</button>
        
        <div id="testResults"></div>
        
        <!-- 多订单面板 -->
        <div id="multiOrderPanel" class="multi-order-panel hidden">
            <div class="multi-order-header">
                <h3>多订单处理</h3>
                <button class="close-btn" onclick="window.OTA.multiOrderManager.hideMultiOrderPanel()">×</button>
            </div>
            
            <div class="multi-order-content">
                <div id="multiOrderList" class="order-list"></div>
            </div>
        </div>
    </div>

    <script src="js/multi-order-manager-v2.js"></script>
    <script>
        // 初始化OTA对象
        window.OTA = window.OTA || {};
        window.OTA.multiOrderManager = new MultiOrderManager();
        
        function testFieldMapping() {
            console.log('🧪 开始测试字段映射');
            
            // 创建测试订单数据
            const testOrder = {
                customerName: '张三',
                customerContact: '13800138000',
                customerEmail: '<EMAIL>',
                pickup: 'Kuala Lumpur International Airport (KLIA1)',
                dropoff: 'THE FACE Style Hotel',
                pickupDate: '2024-12-25',
                pickupTime: '14:30',
                price: '150.00',
                ota: 'Fliggy',
                otaReferenceNumber: 'FG123456789',
                vehicleType: '5 Seater',
                drivingRegion: 'Kl/selangor',
                passengerCount: '3',
                luggageCount: '2',
                meetAndGreet: true,
                babyChair: false,
                tourGuide: false,
                wheelchairAccessible: false,
                flightInfo: 'MH370',
                preferredLanguage: 'Chinese',
                subCategoryId: '2',
                extraRequirement: '需要婴儿椅',
                hotelName: 'The Face Hotel',
                airportTerminal: 'Terminal 1',
                gateNumber: 'A12',
                bookingReference: 'BOOK123456'
            };
            
            console.log('📋 测试订单数据:', testOrder);
            
            // 使用多订单管理器测试字段映射
            window.OTA.multiOrderManager.showMultiOrderPanel([testOrder]);
            
            // 延迟检查结果
            setTimeout(() => {
                console.log('🔍 检查生成的HTML');
                const orderList = document.getElementById('multiOrderList');
                if (orderList) {
                    console.log('✅ 生成的订单列表HTML:', orderList.innerHTML);
                } else {
                    console.log('❌ 未找到订单列表元素');
                }
            }, 100);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，初始化多订单管理器');
        });
    </script>
</body>
</html>
