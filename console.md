---
type: "manual"
---

script-manifest.js:145 ✅ Script manifest ready
script-loader.js:164 ✅ ScriptLoader ready
script-loader.js:121 🔧 Loading phase: core (20 scripts)
dependency-container.js:298 ✅ 依赖容器已初始化
service-locator.js:90 ✅ 服务定位器已初始化
service-locator.js:417 ✅ 服务定位器已加载
service-locator.js:337 [INFO] 🚀 OTA注册中心已初始化 {version: '1.0.0', features: Array(3)}
application-bootstrap.js:482 ✅ 应用启动协调器已加载
dependency-container.js:233 [DependencyContainer] 已注册服务: eventCoordinator
dependency-container.js:233 [DependencyContainer] 已注册服务: lifecycleManager
dependency-container.js:233 [DependencyContainer] 已注册服务: dataManager
service-locator.js:337 [INFO] [EventCoordinator] 已注册组件: vehicleConfigManager {options: {…}}
dependency-container.js:233 [DependencyContainer] 已注册服务: vehicleConfigManager
service-locator.js:337 [DEBUG] [VehicleConfigManager] 已注册到依赖容器 null
service-locator.js:337 [INFO] [VehicleConfigManager] 车辆配置管理器已初始化 {defaultCarTypeId: 5, recommendationRules: 8}
vehicle-configuration-manager.js:420 ✅ 车辆配置管理器已加载
vehicle-config-integration.js:353 ✅ 车型配置集成验证器已加载
global-field-standardization-layer.js:768 [GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化
base-manager-adapter.js:283 ✅ BaseManager适配器已加载，BaseManager全局类已可用
ota-manager-decorator.js:394 ✅ OTAManager装饰器已加载
service-locator.js:339 [INFO] OTAManager工厂已初始化
ota-manager-factory.js:348 ✅ OTAManager工厂已加载
service-locator.js:339 [INFO] 特性开关管理器已初始化
feature-toggle.js:364 ✅ 特性开关机制已加载
service-locator.js:339 [INFO] OTA Bootstrap集成器已初始化
ota-bootstrap-integration.js:396 ✅ OTA Bootstrap集成配置已加载
script-loader.js:124 ✅ Phase complete: core in 83.5ms
script-loader.js:121 🔧 Loading phase: base-utils (5 scripts)
logger.js:472 [7:28:41 PM] [INFO] 🔍 初始化基础监控系统... {type: 'monitoring_init'}
logger.js:472 [7:28:41 PM] [INFO] 全局错误处理已设置 {type: 'global_error_handler_init'}
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 基础监控系统初始化完成 {type: 'monitoring_ready'}
logger.js:472 [7:28:41 PM] [INFO] OTA订单处理系统启动 {type: 'system_start', userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36', timestamp: '2025-08-11T11:28:41.299Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'logger', tag: '@OTA_LOGGER_SERVICE', timestamp: '2025-08-11T11:28:41.301Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: FACTORY {name: 'getLogger', tag: '@OTA_LOGGER_FACTORY', timestamp: '2025-08-11T11:28:41.304Z'}
logger.js:472 [7:28:41 PM] [INFO] ✅ 完整酒店数据模块已加载 
logger.js:198 ✅ 完整酒店数据模块已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ 完整酒店数据初始化完成，共 87 家酒店 
logger.js:198 ✅ 完整酒店数据初始化完成，共 87 家酒店
logger.js:472 [7:28:41 PM] [INFO] 📊 数据统计: 87 家酒店，15 个区域 
logger.js:198 📊 数据统计: 87 家酒店，15 个区域
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: base-utils in 46.7ms 
logger.js:198 ✅ Phase complete: base-utils in 46.7ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: strategies (2 scripts) 
logger.js:198 🔧 Loading phase: strategies (2 scripts)
logger.js:472 [7:28:41 PM] [INFO] ✅ FliggyOTAStrategy (重构版) 已加载 
logger.js:198 ✅ FliggyOTAStrategy (重构版) 已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ JingGeOTAStrategy (重构版) 已加载 
logger.js:198 ✅ JingGeOTAStrategy (重构版) 已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: strategies in 8.2ms 
logger.js:198 ✅ Phase complete: strategies in 8.2ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: new-architecture (15 scripts) 
logger.js:198 🔧 Loading phase: new-architecture (15 scripts)
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化... 
logger.js:198 [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化...
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) [object Object] 
logger.js:198 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) {hasAdapter: false, hasCaller: false, adapterHasParseOrder: false, adapterHasLegacyParse: false, callerHasParseAPIResponse: false}
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截 
logger.js:198 [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复） 
logger.js:198 [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复）
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成 
logger.js:198 [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成
logger.js:472 [7:28:41 PM] [INFO] ✅ ChannelDetector (子层实现) 已加载 
logger.js:198 ✅ ChannelDetector (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 🌐 开始初始化统一语言检测器... 
logger.js:198 🌐 开始初始化统一语言检测器...
logger.js:472 [7:28:41 PM] [INFO] 🌐 初始化统一语言检测器... 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已绑定字段事件: #customerName 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已绑定字段事件: #pickup 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已绑定字段事件: #dropoff 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已绑定字段事件: #extraRequirement 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已绑定字段事件: #flightInfo 
logger.js:472 [7:28:41 PM] [INFO] 📝 已绑定 5 个字段的语言检测事件 
logger.js:472 [7:28:41 PM] [INFO] ✅ 已设置默认语言（英文） 
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 统一语言检测器初始化完成 
logger.js:472 [7:28:41 PM] [INFO] ✅ 统一语言检测器初始化成功 
logger.js:198 ✅ 统一语言检测器初始化成功
logger.js:472 [7:28:41 PM] [INFO] 提示词构建器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ PromptBuilder (子层实现) 已加载 
logger.js:198 ✅ PromptBuilder (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] Gemini API调用器已初始化 {model: 'gemini-2.5-flash'}
logger.js:472 [7:28:41 PM] [INFO] ✅ GeminiCaller (子层实现) 已加载 
logger.js:198 ✅ GeminiCaller (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 结果处理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ ResultProcessor (子层实现) 已加载 
logger.js:198 ✅ ResultProcessor (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 订单解析器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ OrderParser (子层实现) 已加载 
logger.js:198 ✅ OrderParser (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 开始初始化知识库... 
logger.js:472 [7:28:41 PM] [INFO] 开始加载酒店知识库... 
logger.js:469 [7:28:41 PM] [SUCCESS] 酒店数据处理完成 {totalHotels: 87, mappings: 87}
logger.js:469 [7:28:41 PM] [SUCCESS] 使用完整内联酒店数据 {totalHotels: 87, source: 'complete_inline_data'}
logger.js:469 [7:28:41 PM] [SUCCESS] 机场数据加载完成 {totalAirports: 3}
logger.js:472 [7:28:41 PM] [INFO] 知识库管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ KnowledgeBase (子层实现) 已加载 
logger.js:198 ✅ KnowledgeBase (子层实现) 已加载
logger.js:469 [7:28:41 PM] [SUCCESS] 知识库初始化完成 {hotels: 87, airports: 3}
logger.js:472 [7:28:41 PM] [INFO] 地址翻译器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ AddressTranslator (子层实现) 已加载 
logger.js:198 ✅ AddressTranslator (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 多订单处理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ MultiOrderHandler (子层实现) 已加载 
logger.js:198 ✅ MultiOrderHandler (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] API调用器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ APICaller (子层实现) 已加载 
logger.js:198 ✅ APICaller (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (2/150) [object Object] 
logger.js:198 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (2/150) {hasAdapter: false, hasCaller: true, adapterHasParseOrder: false, adapterHasLegacyParse: false, callerHasParseAPIResponse: true}
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse) 
logger.js:198 [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse)
logger.js:469 [7:28:41 PM] [SUCCESS] 历史数据加载成功 {}
logger.js:469 [7:28:41 PM] [SUCCESS] 历史管理器初始化完成 {historyCount: 0}
logger.js:472 [7:28:41 PM] [INFO] 历史管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ HistoryManager (子层实现) 已加载 
logger.js:198 ✅ HistoryManager (子层实现) 已加载
logger.js:472 [7:28:41 PM] [INFO] 渠道检测器已初始化 
logger.js:472 [7:28:41 PM] [INFO] 业务流程控制器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ BusinessFlowController (母层控制器) 已加载 
logger.js:198 ✅ BusinessFlowController (母层控制器) 已加载
logger.js:472 [7:28:41 PM] [INFO] 订单管理控制器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ OrderManagementController (母层控制器) 已加载 
logger.js:198 ✅ OrderManagementController (母层控制器) 已加载
logger.js:472 [7:28:41 PM] [INFO] Gemini服务适配器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✅ GeminiServiceAdapter (兼容性适配器) 已加载 
logger.js:198 ✅ GeminiServiceAdapter (兼容性适配器) 已加载
logger.js:472 [7:28:41 PM] [INFO] 🔄 MultiOrderManagerAdapter 初始化开始 
logger.js:472 [7:28:41 PM] [INFO] ✅ MultiOrderManagerAdapter 已加载并注册 
logger.js:198 ✅ MultiOrderManagerAdapter 已加载并注册
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ MultiOrderManagerAdapter 初始化完成 
logger.js:472 [7:28:41 PM] [INFO] 🔄 UIManagerAdapter 初始化开始 
logger.js:472 [7:28:41 PM] [INFO] ✅ UIManagerAdapter 已加载并注册 
logger.js:198 ✅ UIManagerAdapter 已加载并注册
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ UIManagerAdapter 初始化完成 
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: new-architecture in 161.0ms 
logger.js:198 ✅ Phase complete: new-architecture in 161.0ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: services (9 scripts) 
logger.js:198 🔧 Loading phase: services (9 scripts)
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'appState', tag: '@OTA_APP_STATE_SERVICE', timestamp: '2025-08-11T11:28:41.499Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: FACTORY {name: 'getAppState', tag: '@OTA_APP_STATE_FACTORY', timestamp: '2025-08-11T11:28:41.501Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: UTIL {name: 'getOtaConfigForUser', tag: '@OTA_UTIL_OTA_CONFIG', timestamp: '2025-08-11T11:28:41.503Z'}
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装 
logger.js:198 [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'apiService', tag: '@OTA_API_SERVICE', timestamp: '2025-08-11T11:28:41.559Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: FACTORY {name: 'getAPIService', tag: '@OTA_API_SERVICE_FACTORY', timestamp: '2025-08-11T11:28:41.562Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: FACTORY {name: 'getApiService', tag: '@OTA_API_SERVICE_FACTORY', timestamp: '2025-08-11T11:28:41.564Z'}
logger.js:466 [7:28:41 PM] [WARNING] 部分子层模块未加载，将使用降级方案 
outputToConsole @ logger.js:466
log @ logger.js:383
(anonymous) @ order-management-controller.js:96
setTimeout
initializeChildLayers @ order-management-controller.js:89
OrderManagementController @ order-management-controller.js:78
(anonymous) @ order-management-controller.js:330
(anonymous) @ order-management-controller.js:343
logger.js:469 [7:28:41 PM] [SUCCESS] 新架构组件连接成功 
logger.js:472 [7:28:41 PM] [INFO] ✅ 内联酒店数据模块已加载 
logger.js:198 ✅ 内联酒店数据模块已加载
logger.js:472 [7:28:41 PM] [INFO] 历史订单关闭按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单清空按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单导出按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单搜索按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单重置按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单管理器已初始化（按账号存储） 
logger.js:469 [7:28:41 PM] [SUCCESS] 图片上传按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 图片上传管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] [GlobalFieldStandardization] ✅ API服务拦截器已安装 
logger.js:198 [GlobalFieldStandardization] ✅ API服务拦截器已安装
logger.js:472 [7:28:41 PM] [INFO] 货币转换管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] ✈️ 航班信息服务已初始化 {baseUrl: '', timestamp: '2025-08-11T11:28:41.663Z'}
logger.js:472 [7:28:41 PM] [INFO] 地址翻译服务初始化 
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'addressTranslationService', tag: '@ADDRESS_TRANSLATION_SERVICE', timestamp: '2025-08-11T11:28:41.673Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: FACTORY {name: 'getAddressTranslationService', tag: '@ADDRESS_TRANSLATION_SERVICE_FACTORY', timestamp: '2025-08-11T11:28:41.675Z'}
logger.js:472 [7:28:41 PM] [INFO] 地址翻译服务已注册到OTA系统 
logger.js:198 地址翻译服务已注册到OTA系统
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: services in 185.0ms 
logger.js:198 ✅ Phase complete: services in 185.0ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: multi-order (11 scripts) 
logger.js:198 🔧 Loading phase: multi-order (11 scripts)
logger.js:472 [7:28:41 PM] [INFO] ✅ 字段映射配置已加载 [object Object] 
logger.js:198 ✅ 字段映射配置已加载 {aiToFrontendMappings: 22, frontendToApiMappings: 28, alternativeFields: 14}
logger.js:472 [7:28:41 PM] [INFO] ✅ 字段映射验证器已加载 
logger.js:198 ✅ 字段映射验证器已加载
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'multiOrderDetector', tag: '@OTA_MULTI_ORDER_DETECTOR', timestamp: '2025-08-11T11:28:41.694Z'}
logger.js:472 [7:28:41 PM] [INFO] ✅ 多订单检测服务已加载 
logger.js:198 ✅ 多订单检测服务已加载
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'multiOrderRenderer', tag: '@OTA_MULTI_ORDER_RENDERER', timestamp: '2025-08-11T11:28:41.700Z'}
logger.js:472 [7:28:41 PM] [INFO] ✅ 多订单UI渲染器已加载 
logger.js:198 ✅ 多订单UI渲染器已加载
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'multiOrderProcessor', tag: '@OTA_MULTI_ORDER_PROCESSOR', timestamp: '2025-08-11T11:28:41.708Z'}
logger.js:472 [7:28:41 PM] [INFO] ✅ 多订单批量处理器已加载 
logger.js:198 ✅ 多订单批量处理器已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ 多订单数据转换器已加载 
logger.js:198 ✅ 多订单数据转换器已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ 字段映射测试套件已加载 
logger.js:198 ✅ 字段映射测试套件已加载
logger.js:472 [7:28:41 PM] [INFO] 💡 使用 runFieldMappingTests() 运行完整测试 
logger.js:198 💡 使用 runFieldMappingTests() 运行完整测试
logger.js:472 [7:28:41 PM] [INFO] 💡 使用 runQuickFieldMappingTests() 运行快速测试 
logger.js:198 💡 使用 runQuickFieldMappingTests() 运行快速测试
logger.js:472 [7:28:41 PM] [INFO] 状态已从本地存储加载 
logger.js:472 [7:28:41 PM] [INFO] 多订单状态管理器初始化完成 
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'multiOrderStateManager', tag: '@OTA_MULTI_ORDER_STATE_MANAGER', timestamp: '2025-08-11T11:28:41.732Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'batchProcessor', tag: '@OTA_BATCH_PROCESSOR', timestamp: '2025-08-11T11:28:41.739Z'}
logger.js:463 [7:28:41 PM] [ERROR] 模块初始化失败 {error: 'window.OTA.Registry.getService is not a function'}
outputToConsole @ logger.js:463
log @ logger.js:383
initializeModules @ multi-order-coordinator.js:112
MultiOrderCoordinator @ multi-order-coordinator.js:75
(anonymous) @ multi-order-coordinator.js:488
logger.js:466 [7:28:41 PM] [WARNING] 使用降级方法初始化协调器 
outputToConsole @ logger.js:466
log @ logger.js:383
initializeFallbackMethods @ multi-order-coordinator.js:121
initializeModules @ multi-order-coordinator.js:113
MultiOrderCoordinator @ multi-order-coordinator.js:75
(anonymous) @ multi-order-coordinator.js:488
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'multiOrderCoordinator', tag: '@OTA_MULTI_ORDER_COORDINATOR', timestamp: '2025-08-11T11:28:41.752Z'}
logger.js:472 [7:28:41 PM] [INFO] 📋 OTA注册: SERVICE {name: 'systemIntegrityChecker', tag: '@OTA_SYSTEM_INTEGRITY_CHECKER', timestamp: '2025-08-11T11:28:41.760Z'}
logger.js:472 [7:28:41 PM] [INFO] ✅ 系统完整性检查器已加载 
logger.js:198 ✅ 系统完整性检查器已加载
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: multi-order in 84.7ms 
logger.js:198 ✅ Phase complete: multi-order in 84.7ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: ui-deps (11 scripts) 
logger.js:198 🔧 Loading phase: ui-deps (11 scripts)
logger.js:472 [7:28:41 PM] [INFO] 举牌服务管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] 🔧 自适应高度管理器初始化中... 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: extraRequirement 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: pickup 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: dropoff 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: customerName 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: flightInfo 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: otaReferenceNumber 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: orderInput 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: searchOrderId 
logger.js:472 [7:28:41 PM] [INFO] 📏 已添加自适应高度管理: searchCustomer 
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 自适应高度管理器初始化完成 
logger.js:472 [7:28:41 PM] [INFO] 🎬 初始化动画管理器... 
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 按钮动画处理器初始化完成 
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 动画管理器初始化完成 {enabled: true, config: {…}}
logger.js:472 [7:28:41 PM] [INFO] ✅ 动画管理器已加载并初始化 
logger.js:198 ✅ 动画管理器已加载并初始化
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: ui-deps in 107.3ms 
logger.js:198 ✅ Phase complete: ui-deps in 107.3ms
logger.js:472 [7:28:41 PM] [INFO] 🔧 Loading phase: ui (2 scripts) 
logger.js:198 🔧 Loading phase: ui (2 scripts)
logger.js:472 [7:28:41 PM] [INFO] 🚀 开始启动OTA订单处理系统... 
logger.js:198 🚀 开始启动OTA订单处理系统...
logger.js:472 [7:28:41 PM] [INFO] 🚀 开始启动OTA订单处理系统... 
logger.js:198 🚀 开始启动OTA订单处理系统...
logger.js:472 [7:28:41 PM] [INFO] 📋 执行启动阶段: dependencies (1/5) 
logger.js:198 📋 执行启动阶段: dependencies (1/5)
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: appState 
logger.js:198 [DependencyContainer] 已注册服务: appState
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: logger 
logger.js:198 [DependencyContainer] 已注册服务: logger
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: utils 
logger.js:198 [DependencyContainer] 已注册服务: utils
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖 
logger.js:198 [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: eventCoordinator 
logger.js:198 [DependencyContainer] 已注册服务: eventCoordinator
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: stateManagerAdapter 
logger.js:198 [DependencyContainer] 已注册服务: stateManagerAdapter
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: apiService 
logger.js:198 [DependencyContainer] 已注册服务: apiService
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: geminiService 
logger.js:198 [DependencyContainer] 已注册服务: geminiService
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: i18nManager 
logger.js:198 [DependencyContainer] 已注册服务: i18nManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: imageUploadManager 
logger.js:198 [DependencyContainer] 已注册服务: imageUploadManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: currencyConverter 
logger.js:198 [DependencyContainer] 已注册服务: currencyConverter
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: multiOrderManager 
logger.js:198 [DependencyContainer] 已注册服务: multiOrderManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: orderHistoryManager 
logger.js:198 [DependencyContainer] 已注册服务: orderHistoryManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: pagingServiceManager 
logger.js:198 [DependencyContainer] 已注册服务: pagingServiceManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: uiManager 
logger.js:198 [DependencyContainer] 已注册服务: uiManager
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已注册服务: channelDetector 
logger.js:198 [DependencyContainer] 已注册服务: channelDetector
logger.js:472 [7:28:41 PM] [INFO] 📦 已注册 15 个依赖 
logger.js:198 📦 已注册 15 个依赖
logger.js:472 [7:28:41 PM] [INFO] 📋 执行启动阶段: services (2/5) 
logger.js:198 📋 执行启动阶段: services (2/5)
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: appState 
logger.js:198 [DependencyContainer] 已创建服务实例: appState
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: logger 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: utils 
logger.js:472 [7:28:41 PM] [INFO] [INFO] [EventCoordinator] 全局事件监听器已设置  
logger.js:203 [INFO] [EventCoordinator] 全局事件监听器已设置 null
logger.js:472 [7:28:41 PM] [INFO] [INFO] [EventCoordinator] 全局事件协调器已初始化  
logger.js:203 [INFO] [EventCoordinator] 全局事件协调器已初始化 null
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: eventCoordinator 
logger.js:466 [7:28:41 PM] [WARNING] [WARNING] [EventCoordinator] 全局事件协调器已经初始化  
outputToConsole @ logger.js:466
log @ logger.js:383
console.warn @ logger.js:207
log @ service-locator.js:337
log @ global-event-coordinator.js:331
init @ global-event-coordinator.js:29
initializeServices @ application-bootstrap.js:233
executePhase @ application-bootstrap.js:113
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:208 [WARNING] [EventCoordinator] 全局事件协调器已经初始化 null
console.warn @ logger.js:208
log @ service-locator.js:337
log @ global-event-coordinator.js:331
init @ global-event-coordinator.js:29
initializeServices @ application-bootstrap.js:233
executePhase @ application-bootstrap.js:113
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: apiService 
logger.js:472 [7:28:41 PM] [INFO] [ApiService] 开始初始化... 
logger.js:472 [7:28:41 PM] [INFO] [ApiService] AppState中已有系统数据，跳过初始化 
logger.js:469 [7:28:41 PM] [SUCCESS] [ApiService] 初始化完成 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: geminiService 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: channelDetector 
logger.js:472 [7:28:41 PM] [INFO] ⚙️ 已初始化 6 个核心服务 
logger.js:198 ⚙️ 已初始化 6 个核心服务
logger.js:472 [7:28:41 PM] [INFO] 📋 执行启动阶段: managers (3/5) 
logger.js:198 📋 执行启动阶段: managers (3/5)
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: imageUploadManager 
logger.js:469 [7:28:41 PM] [SUCCESS] 图片上传按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 图片上传管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: currencyConverter 
logger.js:472 [7:28:41 PM] [INFO] 货币转换管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: multiOrderManager 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: orderHistoryManager 
logger.js:472 [7:28:41 PM] [INFO] 历史订单关闭按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单清空按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单导出按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单搜索按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单重置按钮事件已绑定 
logger.js:472 [7:28:41 PM] [INFO] 历史订单管理器已初始化（按账号存储） 
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: pagingServiceManager 
logger.js:472 [7:28:41 PM] [INFO] 举牌服务管理器已初始化 
logger.js:472 [7:28:41 PM] [INFO] 🎛️ 已处理 5 个管理器 
logger.js:198 🎛️ 已处理 5 个管理器
logger.js:472 [7:28:41 PM] [INFO] 📋 执行启动阶段: ui (4/5) 
logger.js:198 📋 执行启动阶段: ui (4/5)
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: i18nManager 
logger.js:472 [7:28:41 PM] [INFO] 国际化管理器已初始化，当前语言: zh 
logger.js:472 [7:28:41 PM] [INFO] ✅ Phase complete: ui in 75.7ms 
logger.js:198 ✅ Phase complete: ui in 75.7ms
logger.js:472 [7:28:41 PM] [INFO] 🚀 All scripts loaded in 754.4ms 
logger.js:198 🚀 All scripts loaded in 754.4ms
logger.js:472 [7:28:41 PM] [INFO] [DependencyContainer] 已创建服务实例: uiManager 
logger.js:472 [7:28:41 PM] [INFO] UIManager 开始初始化... 
logger.js:472 [7:28:41 PM] [INFO] DOM元素缓存完成 
logger.js:472 [7:28:41 PM] [INFO] DOM元素已缓存 
logger.js:472 [7:28:41 PM] [INFO] 显示主工作区 
logger.js:472 [7:28:41 PM] [INFO] 系统数据已存在，无需重新初始化 {languagesCount: 11}
logger.js:469 [7:28:41 PM] [SUCCESS] ✅ 表单管理器动画集成成功 
logger.js:472 [7:28:41 PM] [INFO] 价格转换监听器初始化完成（货币切换逻辑由PriceManager处理） 
logger.js:469 [7:28:42 PM] [SUCCESS] 自适应高度输入框初始化完成 
logger.js:469 [7:28:42 PM] [SUCCESS] 表单管理器初始化完成 
logger.js:472 [7:28:42 PM] [INFO] 货币转换管理器已初始化 
logger.js:469 [7:28:42 PM] [SUCCESS] 价格管理器初始化完成 
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ UI状态管理器动画集成成功 
logger.js:472 [7:28:42 PM] [INFO] 状态监听器设置完成 
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectServiceType 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:348
updateLoginUI @ ui-state-manager.js:183
updateUI @ ui-state-manager.js:131
init @ ui-state-manager.js:35
initializeManagers @ ui-manager.js:276
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectCarType 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:349
updateLoginUI @ ui-state-manager.js:183
updateUI @ ui-state-manager.js:131
init @ ui-state-manager.js:35
initializeManagers @ ui-manager.js:276
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:350
updateLoginUI @ ui-state-manager.js:183
updateUI @ ui-state-manager.js:131
init @ ui-state-manager.js:35
initializeManagers @ ui-manager.js:276
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectDrivingRegion 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:351
updateLoginUI @ ui-state-manager.js:183
updateUI @ ui-state-manager.js:131
init @ ui-state-manager.js:35
initializeManagers @ ui-manager.js:276
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [7:28:42 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [7:28:42 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [7:28:42 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [7:28:42 PM] [INFO] 自动分析已启用 
logger.js:472 [7:28:42 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:469 [7:28:42 PM] [SUCCESS] 表单选项填充完成 
logger.js:472 [7:28:42 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [7:28:42 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [7:28:42 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [7:28:42 PM] [INFO] 自动分析已启用 
logger.js:472 [7:28:42 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:472 [7:28:42 PM] [INFO] UI已切换到工作区 
logger.js:469 [7:28:42 PM] [SUCCESS] 状态管理器初始化完成 
logger.js:472 [7:28:42 PM] [INFO] 开始绑定事件监听器... 
logger.js:469 [7:28:42 PM] [SUCCESS] 所有事件监听器绑定完成 
logger.js:469 [7:28:42 PM] [SUCCESS] 事件管理器初始化完成 
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ 实时分析管理器动画集成成功 
logger.js:472 [7:28:42 PM] [INFO] 实时分析功能已配置（性能优化版） {debounceDelay: 500, minInputLength: 10}
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ 订单输入框事件绑定成功 
logger.js:472 [7:28:42 PM] [INFO] 🌐 使用统一语言检测器，跳过重复绑定 
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ 实时分析事件绑定验证完成 
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ 实时分析管理器初始化完成 
logger.js:469 [7:28:42 PM] [SUCCESS] 管理器模块初始化完成 
logger.js:469 [7:28:42 PM] [SUCCESS] 图片上传按钮事件已重新绑定 
logger.js:469 [7:28:42 PM] [SUCCESS] UI管理器初始化完成 
logger.js:472 [7:28:42 PM] [INFO] 🎨 用户界面初始化完成 
logger.js:198 🎨 用户界面初始化完成
logger.js:472 [7:28:42 PM] [INFO] 📋 执行启动阶段: finalization (5/5) 
logger.js:198 📋 执行启动阶段: finalization (5/5)
logger.js:472 [7:28:42 PM] [INFO] [ApplicationBootstrap] 字段标准化层状态检查 [object Object] 
logger.js:198 [ApplicationBootstrap] 字段标准化层状态检查 {layerExists: true, featureEnabled: true, initialized: true}
logger.js:472 [7:28:42 PM] [INFO] [ApplicationBootstrap] ✅ 字段标准化层已初始化 
logger.js:198 [ApplicationBootstrap] ✅ 字段标准化层已初始化
logger.js:472 [7:28:42 PM] [INFO] 🏁 系统启动完成 
logger.js:198 🏁 系统启动完成
logger.js:472 [7:28:42 PM] [INFO] ✅ OTA系统启动完成，总耗时: 240.70ms 
logger.js:198 ✅ OTA系统启动完成，总耗时: 240.70ms
application-bootstrap.js:446 📊 启动报告
logger.js:472 [7:28:42 PM] [INFO] ✅ dependencies: 2.40ms 
logger.js:198 ✅ dependencies: 2.40ms
logger.js:472 [7:28:42 PM] [INFO]    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: stateManagerAdapter, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector 
logger.js:198    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: stateManagerAdapter, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector
logger.js:472 [7:28:42 PM] [INFO] ✅ services: 20.00ms 
logger.js:198 ✅ services: 20.00ms
logger.js:472 [7:28:42 PM] [INFO]    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService 
logger.js:198    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService
logger.js:472 [7:28:42 PM] [INFO] ✅ managers: 35.50ms 
logger.js:198 ✅ managers: 35.50ms
logger.js:472 [7:28:42 PM] [INFO]    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager 
logger.js:198    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager
logger.js:472 [7:28:42 PM] [INFO] ✅ ui: 179.90ms 
logger.js:198 ✅ ui: 179.90ms
logger.js:472 [7:28:42 PM] [INFO]    详情: 国际化管理器已初始化, UI管理器已初始化 
logger.js:198    详情: 国际化管理器已初始化, UI管理器已初始化
logger.js:472 [7:28:42 PM] [INFO] ✅ finalization: 1.90ms 
logger.js:198 ✅ finalization: 1.90ms
logger.js:472 [7:28:42 PM] [INFO]    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成 
logger.js:198    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成
logger.js:472 [7:28:42 PM] [INFO] ✅ OTA系统启动成功，耗时: 240.70ms 
logger.js:198 ✅ OTA系统启动成功，耗时: 240.70ms
logger.js:472 [7:28:42 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [7:28:42 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [7:28:42 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [7:28:42 PM] [INFO] 自动分析已启用 
logger.js:472 [7:28:42 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:472 [7:28:42 PM] [INFO] 语言切换后重新填充下拉菜单选项 
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectServiceType 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:348
(anonymous) @ form-manager.js:41
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectCarType 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:349
(anonymous) @ form-manager.js:41
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:350
(anonymous) @ form-manager.js:41
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] ✅ 使用i18n翻译占位符: form.selectDrivingRegion 
logger.js:463 [7:28:42 PM] [ERROR] populateSelect: 添加选项失败 {type: 'error', error: 'populateSelect: 添加选项失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
populateSelect @ form-manager.js:510
populateFormOptions @ form-manager.js:351
(anonymous) @ form-manager.js:41
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [7:28:42 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [7:28:42 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [7:28:42 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [7:28:42 PM] [INFO] 自动分析已启用 
logger.js:472 [7:28:42 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:469 [7:28:42 PM] [SUCCESS] 表单选项填充完成 
logger.js:472 [7:28:42 PM] [INFO] 🔧 [FormManager] 初始化原生语言选择器 
logger.js:463 [7:28:42 PM] [ERROR] 💥 [FormManager] 初始化语言选择器失败 {type: 'error', error: '💥 [FormManager] 初始化语言选择器失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
initLanguagesDropdown @ form-manager.js:239
initCustomComponents @ form-manager.js:85
(anonymous) @ form-manager.js:43
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:298
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [7:28:42 PM] [INFO] 检测到用户已登录，应用权限控制 {email: '<EMAIL>'}
logger.js:472 [7:28:42 PM] [INFO] 价格字段权限检查 {email: '<EMAIL>', permissions: {…}}
logger.js:472 [7:28:42 PM] [INFO] 🔐 开始应用价格字段权限控制 {canViewOtaPrice: true, canViewDriverFee: true}
logger.js:472 [7:28:42 PM] [INFO] 💰 价格信息面板显示状态: block 
logger.js:472 [7:28:42 PM] [INFO] OTA价格字段显示状态: flex 
logger.js:472 [7:28:42 PM] [INFO] 司机费用字段显示状态: flex 
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ 价格字段权限控制应用完成 {canViewOtaPrice: true, canViewDriverFee: true, hasAnyPricePermission: true, priceInfoPanelVisible: true}
logger.js:472 [7:28:42 PM] [INFO] 语言选项权限检查 {email: '<EMAIL>', permissions: {…}}
logger.js:472 [7:28:42 PM] [INFO] 🔐 开始应用语言选项权限控制 {canUsePaging: true}
logger.js:469 [7:28:42 PM] [SUCCESS] ✅ Paging选项已启用（显示） 
logger.js:469 [7:28:42 PM] [SUCCESS] 语言选项权限控制初始化完成 {canUsePaging: true}
form-manager.js:40 [Violation] 'setTimeout' handler took 74ms
logger.js:472 [7:28:42 PM] [INFO] [INFO] 开始集成OTAManager到ApplicationBootstrap... 
logger.js:203 [INFO] 开始集成OTAManager到ApplicationBootstrap...
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ 前置条件检查通过: OTA命名空间 
logger.js:203 [INFO] ✅ 前置条件检查通过: OTA命名空间
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ 前置条件检查通过: OTAManager工厂 
logger.js:203 [INFO] ✅ 前置条件检查通过: OTAManager工厂
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ 前置条件检查通过: BaseManager适配器 
logger.js:203 [INFO] ✅ 前置条件检查通过: BaseManager适配器
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ 前置条件检查通过: OTAManager装饰器 
logger.js:203 [INFO] ✅ 前置条件检查通过: OTAManager装饰器
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ 前置条件检查通过: DependencyContainer 
logger.js:203 [INFO] ✅ 前置条件检查通过: DependencyContainer
logger.js:472 [7:28:42 PM] [INFO] [INFO] 原始配置备份完成（运行时集成模式） 
logger.js:203 [INFO] 原始配置备份完成（运行时集成模式）
logger.js:472 [7:28:42 PM] [INFO] [DependencyContainer] 已注册服务: otaManager 
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ OTAManager已注册到DependencyContainer 
logger.js:203 [INFO] ✅ OTAManager已注册到DependencyContainer
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ OTAManager已添加到ApplicationBootstrap启动流程 
logger.js:203 [INFO] ✅ OTAManager已添加到ApplicationBootstrap启动流程
logger.js:472 [7:28:42 PM] [INFO] [INFO] 开始验证OTAManager集成结果... 
logger.js:203 [INFO] 开始验证OTAManager集成结果...
logger.js:472 [7:28:42 PM] [INFO] [INFO] 创建OTAManager实例（通过DependencyContainer） 
logger.js:203 [INFO] 创建OTAManager实例（通过DependencyContainer）
logger.js:472 [7:28:42 PM] [INFO] [INFO] 开始创建OTAManager实例... 
logger.js:203 [INFO] 开始创建OTAManager实例...
logger.js:472 [7:28:42 PM] [INFO] OTAManagerDecorator: 所有适配器方法注入成功 
logger.js:198 OTAManagerDecorator: 所有适配器方法注入成功
logger.js:472 [7:28:42 PM] [INFO] 🔧 OTAManager装饰器已创建 
logger.js:198 🔧 OTAManager装饰器已创建
logger.js:472 [7:28:42 PM] [INFO] [INFO] OTAManager已通过装饰器增强 
logger.js:203 [INFO] OTAManager已通过装饰器增强
logger.js:472 [7:28:42 PM] [INFO] [INFO] OTAManager配置完成 [object Object] 
logger.js:203 [INFO] OTAManager配置完成 {singleton: true}
logger.js:472 [7:28:42 PM] [INFO] [INFO] OTAManager实例创建成功 
logger.js:203 [INFO] OTAManager实例创建成功
logger.js:472 [7:28:42 PM] [INFO] [DependencyContainer] 已创建服务实例: otaManager 
logger.js:472 [7:28:42 PM] [INFO] 🔧 开始初始化OTAManager（通过装饰器）... 
logger.js:198 🔧 开始初始化OTAManager（通过装饰器）...
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] Initializing OTAManager... 
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] Strategy registered for channel: default 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted via local listeners: strategy-registered 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted: strategy-registered {channel: 'default', strategy: {…}, source: 'OTAManager', timestamp: 1754911722333}
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] Strategy registered for channel: fliggy 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted via local listeners: strategy-registered 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted: strategy-registered {channel: 'fliggy', strategy: {…}, source: 'OTAManager', timestamp: 1754911722339}
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] Default strategies registered 
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] Switched to strategy for channel: default 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted via local listeners: channel-switched 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted: channel-switched {from: 'default', to: 'default', confidence: 1, source: 'OTAManager', timestamp: 1754911722348}
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] OTAManager initialized successfully 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted via local listeners: ota-manager-initialized 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted: ota-manager-initialized {source: 'OTAManager', timestamp: 1754911722355}
logger.js:472 [7:28:42 PM] [INFO] [OTAManager] OTAManager initialized successfully via decorator 
logger.js:472 [7:28:42 PM] [INFO] 📋 OTA注册: SERVICE {name: 'otaManager', tag: '@OTA_MANAGER', timestamp: '2025-08-11T11:28:42.364Z'}
logger.js:472 [7:28:42 PM] [INFO] ✅ OTAManager已注册到OTA命名空间 
logger.js:198 ✅ OTAManager已注册到OTA命名空间
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted via local listeners: ota-manager-decorator-initialized 
logger.js:472 [7:28:42 PM] [DEBUG] [OTAManager] Event emitted: ota-manager-decorator-initialized {timestamp: 1754911722366, managerName: 'OTAManager', source: 'OTAManager'}
logger.js:472 [7:28:42 PM] [INFO] ✅ OTAManager初始化完成（通过装饰器） 
logger.js:198 ✅ OTAManager初始化完成（通过装饰器）
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ OTAManager集成验证通过 
logger.js:203 [INFO] ✅ OTAManager集成验证通过
logger.js:472 [7:28:42 PM] [INFO] [INFO] ✅ OTAManager集成到ApplicationBootstrap成功 
logger.js:203 [INFO] ✅ OTAManager集成到ApplicationBootstrap成功
logger.js:472 [7:28:42 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [7:28:42 PM] [INFO] 已设置默认负责人 {userId: 310}
logger.js:472 [7:28:42 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [7:28:42 PM] [INFO] 已设置默认负责人 {userId: 310}
logger.js:472 [7:28:43 PM] [INFO] [VehicleConfigIntegration] ✅ 默认车型ID验证通过: 5 
logger.js:472 [7:28:43 PM] [INFO] [DEBUG] [VehicleConfigManager] 车型推荐完成 [object Object] 
logger.js:198 [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 2, otaChannel: null, recommendedCarTypeId: 5, recommendedName: '5 Seater'}
logger.js:472 [7:28:43 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 2, recommendedCarTypeId: 5, recommendedName: '5 Seater', description: '推荐给2位乘客的5 Seater'}
logger.js:472 [7:28:43 PM] [INFO] [VehicleConfigIntegration] ✅ ApiService 集成验证通过 
logger.js:472 [7:28:43 PM] [INFO] [VehicleConfigIntegration] ✅ MultiOrderTransformer 集成验证通过 
logger.js:472 [7:28:43 PM] [INFO] [VehicleConfigIntegration] 🔍 车型配置集成报告 {timestamp: '2025-08-11T11:28:43.241Z', status: 'ISSUES_FOUND', summary: {…}, issues: Array(1), recommendations: Array(2)}
logger.js:466 [7:28:43 PM] [WARNING] [VehicleConfigIntegration] ⚠️ 发现 1 个集成问题 {issues: Array(1)}
outputToConsole @ logger.js:466
log @ logger.js:383
log @ vehicle-config-integration.js:326
generateIntegrationReport @ vehicle-config-integration.js:183
validateIntegration @ vehicle-config-integration.js:86
init @ vehicle-config-integration.js:28
(anonymous) @ vehicle-config-integration.js:347
setTimeout
(anonymous) @ vehicle-config-integration.js:345
(anonymous) @ vehicle-config-integration.js:355
logger.js:472 [7:28:43 PM] [INFO] [VehicleConfigIntegration] 车型配置集成验证器已初始化 
logger.js:472 [7:28:43 PM] [INFO] 🔍 开始系统完整性检查 
logger.js:472 [7:28:43 PM] [INFO] 🔗 检查依赖关系... 
logger.js:472 [7:28:43 PM] [INFO] 📋 检查服务注册... 
logger.js:472 [7:28:43 PM] [INFO] 🎭 检查事件系统... 
logger.js:472 [7:28:43 PM] [INFO] 🚀 检查初始化逻辑... 
logger.js:472 [7:28:43 PM] [INFO] 🔄 检查向后兼容性... 
logger.js:472 [7:28:43 PM] [INFO] 🏁 系统完整性检查完成 {overall: 'ISSUES_FOUND', summary: {…}, categories: {…}, failedTests: Array(4), results: Array(25)}
logger.js:472 [7:28:44 PM] [INFO] [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止 
logger.js:198 [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止
logger.js:472 [7:28:46 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:28:47 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: 'unknown', textLength: 243, hasChinese: true, languageIds: Array(1)}
logger.js:466 [7:28:47 PM] [WARNING] OTA渠道下拉框未找到 {searchedSelectors: Array(5)}
outputToConsole @ logger.js:466
log @ logger.js:383
log @ language-detector.js:107
setOTAChannelSelection @ language-detector.js:672
detectAndApplyChannelOnly @ language-detector.js:465
await in detectAndApplyChannelOnly
detectAndApply @ language-detector.js:306
await in detectAndApply
detectAndSetChineseLanguage @ realtime-analysis-manager.js:820
handleRealtimeInput @ realtime-analysis-manager.js:391
logger.js:472 [7:28:47 PM] [INFO] 渠道检测结果已存储到AppState {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
logger.js:472 [7:28:47 PM] [INFO] 🚀 渠道检测完成并已存储结果 {channel: 'Fliggy', confidence: 0.95, sourceField: 'unknown', stored: true}
logger.js:472 [7:28:47 PM] [INFO] ⚠️ 检测到重复输入调用，跳过处理 {timeDiff: 97, isPasteEvent: true}
realtime-analysis-manager.js:415 🔍 多订单数据流追踪 - 第1步：实时分析触发
logger.js:472 [7:28:47 PM] [INFO] 输入文本长度: 243 
logger.js:198 输入文本长度: 243
logger.js:472 [7:28:47 PM] [INFO] 输入文本预览: 订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08:51:26
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡市中豪亚酒店

[抵达]樟宜机场T1

约26.4公里

2025-08-11 14:10:00

联系人许丽俊

真实号：13916811351

---
4成人0儿童

司机姓名：kk

司机电话：17605088... 
logger.js:198 输入文本预览: 订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08:51:26
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡市中豪亚酒店

[抵达]樟宜机场T1

约26.4公里

2025-08-11 14:10:00

联系人许丽俊

真实号：13916811351

---
4成人0儿童

司机姓名：kk

司机电话：17605088...
logger.js:472 [7:28:47 PM] [INFO] 🔍 获取到的渠道检测结果: [object Object] 
logger.js:198 🔍 获取到的渠道检测结果: {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
realtime-analysis-manager.js:438 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js:472 [7:28:47 PM] [INFO] 🔄 开始实时订单解析... 
logger.js:472 [7:28:47 PM] [INFO] 适配器：解析订单文本 {textLength: 243, isRealtime: true, hasChannelInfo: true}
logger.js:472 [7:28:47 PM] [INFO] 开始处理输入 {type: 'text', inputLength: 243, autoTriggered: false, sourceField: 'unknown'}
logger.js:472 [7:28:47 PM] [INFO] 🚀 使用预检测的渠道结果 {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern'}
logger.js:472 [7:28:47 PM] [DEBUG] 🔧 [调试] 开始构建提示词 {channel: 'Fliggy', confidence: 0.95, hasStrategy: true}
logger.js:472 [7:28:47 PM] [INFO] 开始构建提示词 {channel: 'Fliggy', inputLength: 243, autoTriggered: false, sourceField: 'unknown'}
logger.js:472 [7:28:47 PM] [DEBUG] 🔧 [调试] 获取渠道策略 {channel: 'Fliggy'}
logger.js:472 [7:28:47 PM] [DEBUG] 🔧 [调试] 渠道策略找到，获取字段片段 {strategyType: 'FliggyOTAStrategy', hasGetFieldPromptSnippets: true}
logger.js:469 [7:28:47 PM] [SUCCESS] 成功获取字段提示词片段 {snippetCount: 7}
logger.js:472 [7:28:47 PM] [DEBUG] 🔧 [调试] 字段片段获取完成 {snippetCount: 7, snippetKeys: '[FILTERED]'}
logger.js:469 [7:28:47 PM] [SUCCESS] 提示词构建完成 {channel: 'Fliggy', promptLength: 5407, autoTriggered: false}
logger.js:472 [7:28:47 PM] [DEBUG] 🔧 [调试] 提示词构建完成 {promptLength: 5407, containsChannelInfo: true}
logger.js:472 [7:28:47 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 5407, options: {…}}
logger.js:472 [7:28:51 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:28:56 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:29:01 PM] [DEBUG] 状态已保存到本地存储 
logger.js:466 [7:29:02 PM] [WARNING] API请求失败 (尝试 1/2) {error: 'signal timed out'}error: "signal timed out"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
outputToConsole @ logger.js:466
log @ logger.js:383
makeAPIRequest @ gemini-caller.js:256
await in makeAPIRequest
callTextAPI @ gemini-caller.js:168
callAPI @ gemini-caller.js:124
callGeminiAPI @ business-flow-controller.js:271
processInput @ business-flow-controller.js:189
await in processInput
parseOrder @ gemini-service-adapter.js:337
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:472 [7:29:06 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:29:11 PM] [INFO] [GlobalFieldStandardization] 🔄 执行延迟重试拦截器安装... 
logger.js:198 [GlobalFieldStandardization] 🔄 执行延迟重试拦截器安装...
logger.js:472 [7:29:11 PM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) [object Object] 
logger.js:198 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) {hasAdapter: true, hasCaller: true, adapterHasParseOrder: true, adapterHasLegacyParse: false, callerHasParseAPIResponse: true}adapterHasLegacyParse: falseadapterHasParseOrder: truecallerHasParseAPIResponse: truehasAdapter: truehasCaller: true[[Prototype]]: Object
logger.js:472 [7:29:11 PM] [INFO] [GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrder) 
logger.js:198 [GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrder)
logger.js:472 [7:29:11 PM] [INFO] [GlobalFieldStandardization] ✅ 延迟重试完成（已简化） 
logger.js:198 [GlobalFieldStandardization] ✅ 延迟重试完成（已简化）
logger.js:472 [7:29:11 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:29:12 PM] [INFO] 🚀 Gemini API调用耗时: 9319.40ms {attempt: 2, timeout: 15000, model: 'gemini-2.5-flash'}
logger.js:472 [7:29:12 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"许丽俊\",\n  \"customer_contact\": \"13916811351\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2872865460249204057\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-08-11\",\n  \"time\": \"14:10\",\n  \"pickup\": \"新加坡市中豪亚酒店\",\n  \"destination\": \"樟宜机场T1\",\n  \"passenger_number\": 4,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 35,\n  \"driving_region_id\": 5,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 57.96,\n  \"currency\": \"CNY\",\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2666,
    "candidatesTokenCount": 303,
    "totalTokenCount": 4173,
    "cachedContentTokenCount": 1981,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2666
      }
    ],
    "cacheTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 1981
      }
    ],
    "thoughtsTokenCount": 1204
  },
  "modelVersion": "gemini-2.5-flash",
  "responseId": "CdSZaLy2H7q1qtsPnKyooAo"
} 
logger.js:198 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"许丽俊\",\n  \"customer_contact\": \"13916811351\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2872865460249204057\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-08-11\",\n  \"time\": \"14:10\",\n  \"pickup\": \"新加坡市中豪亚酒店\",\n  \"destination\": \"樟宜机场T1\",\n  \"passenger_number\": 4,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 35,\n  \"driving_region_id\": 5,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 57.96,\n  \"currency\": \"CNY\",\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2666,
    "candidatesTokenCount": 303,
    "totalTokenCount": 4173,
    "cachedContentTokenCount": 1981,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2666
      }
    ],
    "cacheTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 1981
      }
    ],
    "thoughtsTokenCount": 1204
  },
  "modelVersion": "gemini-2.5-flash",
  "responseId": "CdSZaLy2H7q1qtsPnKyooAo"
}
logger.js:472 [7:29:12 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"许丽俊\",\n  \"customer_contact\": \"13916811351\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2872865460249204057\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-08-11\",\n  \"time\": \"14:10\",\n  \"pickup\": \"新加坡市中豪亚酒店\",\n  \"destination\": \"樟宜机场T1\",\n  \"passenger_number\": 4,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 35,\n  \"driving_region_id\": 5,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 57.96,\n  \"currency\": \"CNY\",\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js:198 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"许丽俊\",\n  \"customer_contact\": \"13916811351\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2872865460249204057\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-08-11\",\n  \"time\": \"14:10\",\n  \"pickup\": \"新加坡市中豪亚酒店\",\n  \"destination\": \"樟宜机场T1\",\n  \"passenger_number\": 4,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 35,\n  \"driving_region_id\": 5,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 57.96,\n  \"currency\": \"CNY\",\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js:469 [7:29:12 PM] [SUCCESS] Gemini API调用成功 {type: 'text', hasResult: true}
logger.js:472 [7:29:12 PM] [INFO] 开始处理结果 {hasGeminiResult: true, channel: 'Fliggy'}
logger.js:466 [7:29:12 PM] [WARNING] 单订单数据验证失败 {valid: false, errors: Array(2), warnings: Array(0)}
outputToConsole @ logger.js:466
log @ logger.js:383
handleSingleOrder @ result-processor.js:169
processResult @ result-processor.js:109
processResult @ business-flow-controller.js:297
processInput @ business-flow-controller.js:192
await in processInput
parseOrder @ gemini-service-adapter.js:337
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:469 [7:29:12 PM] [SUCCESS] 结果处理完成 {type: 'single-order', orderCount: 1}
logger.js:469 [7:29:12 PM] [SUCCESS] 输入处理完成 {channel: 'Fliggy', resultType: 'single-order', autoTriggered: false}
logger.js:472 [7:29:12 PM] [INFO] [GeminiServiceAdapter] 🔧 业务流控制器原始结果: [object Object] 
logger.js:198 [GeminiServiceAdapter] 🔧 业务流控制器原始结果: {type: 'single-order', order: {…}, orders: Array(1), channel: 'Fliggy', confidence: 0.8, …}
gemini-service-adapter.js:218 🤖 Gemini parseOrder 返回
logger.js:472 [7:29:12 PM] [INFO] meta: [object Object] 
logger.js:198 meta: {isRealtime: true}
logger.js:472 [7:29:12 PM] [INFO] data (object): [object Object] 
logger.js:198 data (object): [{…}]
logger.js:472 [7:29:12 PM] [INFO] data (json):
[
  {
    "customer_name": "许丽俊",
    "customer_contact": "13916811351",
    "customer_email": null,
    "ota": "Fliggy",
    "ota_reference_number": "2872865460249204057",
    "flight_info": null,
    "departure_time": null,
    "arrival_time": null,
    "flight_type": "Departure",
    "date": "2025-08-11",
    "time": "14:10",
    "pickup": "新加坡市中豪亚酒店",
    "destination": "樟宜机场T1",
    "passenger_number": 4,
    "luggage_number": null,
    "sub_category_id": 3,
    "car_type_id": 35,
    "driving_region_id": 5,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "needs_paging_service": null,
    "ota_price": 57.96,
    "currency": "CNY",
    "extra_requirement": null,
    "orderIndex": 0,
    "processedAt": "2025-08-11T11:29:12.604Z"
  }
] 
logger.js:198 data (json):
[
  {
    "customer_name": "许丽俊",
    "customer_contact": "13916811351",
    "customer_email": null,
    "ota": "Fliggy",
    "ota_reference_number": "2872865460249204057",
    "flight_info": null,
    "departure_time": null,
    "arrival_time": null,
    "flight_type": "Departure",
    "date": "2025-08-11",
    "time": "14:10",
    "pickup": "新加坡市中豪亚酒店",
    "destination": "樟宜机场T1",
    "passenger_number": 4,
    "luggage_number": null,
    "sub_category_id": 3,
    "car_type_id": 35,
    "driving_region_id": 5,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "needs_paging_service": null,
    "ota_price": 57.96,
    "currency": "CNY",
    "extra_requirement": null,
    "orderIndex": 0,
    "processedAt": "2025-08-11T11:29:12.604Z"
  }
]
logger.js:472 [7:29:12 PM] [INFO] Gemini parseResult: [object Object] 
logger.js:198 Gemini parseResult: [{…}]
logger.js:472 [7:29:12 PM] [INFO] 🔍 parseOrder返回结果调试 {parseResult: Array(1), isArray: true, length: 1, type: 'object'}
logger.js:469 [7:29:12 PM] [SUCCESS] ✅ 解析完成，检测到 1 个订单 
realtime-analysis-manager.js:465 🔍 多订单数据流追踪 - 第3步：解析结果处理
logger.js:472 [7:29:12 PM] [INFO] 🎯 根据解析结果数量决定处理方式 {orderCount: 1, willTriggerMultiOrder: false}
logger.js:472 [7:29:12 PM] [INFO] 解析结果数量: 1 
logger.js:198 解析结果数量: 1
logger.js:472 [7:29:12 PM] [INFO] 是否触发多订单: false 
logger.js:198 是否触发多订单: false
realtime-analysis-manager.js:521 🔍 单订单数据流追踪 - 第4步：简化渠道检测
logger.js:472 [7:29:12 PM] [INFO] 🔍 开始单订单渠道检测 {orderData: {…}, originalText: '订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08…605088835\n\n---\n\n总价格：345元\n\n用户实付：345.00元\n\n商家实收：345元'}
logger.js:472 [7:29:12 PM] [INFO] 开始渠道检测 {inputLength: 243}
logger.js:469 [7:29:12 PM] [SUCCESS] 检测到Fliggy渠道 {channel: 'fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号+19位数字'}
logger.js:469 [7:29:12 PM] [SUCCESS] ✅ 统一渠道检测完成 {}
logger.js:472 [7:29:12 PM] [INFO] 开始从当前订单状态更新表单 
logger.js:472 [7:29:12 PM] [INFO] 🚀 启用实时填充模式（禁用动画） 
logger.js:472 [7:29:12 PM] [INFO] 开始填充表单数据 {dataKeys: '[FILTERED]'}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: customer_name → customerName {value: '许丽俊', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: customer_contact → customerContact {value: '13916811351', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: customer_email → customerEmail {value: null, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 特殊处理OTA渠道字段: ota {value: 'Fliggy'}
logger.js:472 [7:29:12 PM] [INFO] OTA渠道设置成功(下拉框): Fliggy 
logger.js:472 [7:29:12 PM] [INFO] 字段映射: ota_reference_number → otaReferenceNumber {value: '2872865460249204057', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: departure_time → pickupTime {value: null, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: arrival_time → pickupTime {value: null, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: date → pickupDate {value: '2025-08-11', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: time → pickupTime {value: '14:10', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: pickup → pickup {value: '新加坡市中豪亚酒店', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: destination → dropoff {value: '樟宜机场T1', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: passenger_number → passengerCount {value: 4, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: luggage_number → luggageCount {value: null, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: sub_category_id → subCategoryId {value: 3, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: car_type_id → carTypeId {value: 35, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] [DEBUG] [VehicleConfigManager] 车型推荐完成 [object Object] 
logger.js:198 [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 4, otaChannel: null, recommendedCarTypeId: 37, recommendedName: 'Extended 5'}
logger.js:472 [7:29:12 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 4, recommendedCarTypeId: 37, recommendedName: 'Extended 5', description: '推荐给4位乘客的Extended 5'}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: driving_region_id → drivingRegionId {value: 5, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: ota_price → otaPrice {value: 57.96, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: currency → currency {value: 'CNY', hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 字段映射: extra_requirement → extraRequirement {value: null, hasElement: true}
logger.js:472 [7:29:12 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: inchargeByBackendUserId = 310 
logger.js:472 [7:29:12 PM] [INFO] 已应用默认负责人: 310 
logger.js:472 [7:29:12 PM] [INFO] 🚀 实时填充模式已完成 
logger.js:469 [7:29:12 PM] [SUCCESS] 表单数据填充完成 {fieldsProcessed: 27, realtimeMode: false}
logger.js:463 [7:29:12 PM] [ERROR] 实时订单解析失败 {type: 'error', error: '实时订单解析失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
triggerRealtimeAnalysis @ realtime-analysis-manager.js:590
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [7:29:12 PM] [ERROR] 实时分析失败 {error: 'getFormManager is not defined'}
outputToConsole @ logger.js:463
log @ logger.js:383
handleAnalysisError @ realtime-analysis-manager.js:719
triggerRealtimeAnalysis @ realtime-analysis-manager.js:591
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:472 [7:29:12 PM] [INFO] 🔄 实时分析处理完成 
logger.js:472 [7:29:12 PM] [INFO] 🚀 开始批量执行19个DOM更新操作 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: customerName = 许丽俊 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: customerContact = 13916811351 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: customerEmail = null 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: otaReferenceNumber = 2872865460249204057 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: flightInfo = null 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: pickupDate = 2025-08-11 
logger.js:472 [7:29:12 PM] [INFO] 日期字段填充: pickupDate {original: '2025-08-11', formatted: '2025-08-11'}
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: pickupTime = 14:10 
logger.js:472 [7:29:12 PM] [INFO] 时间字段填充: pickupTime {original: '14:10', formatted: '14:10', primary: false}
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: pickup = 新加坡市中豪亚酒店 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: dropoff = 樟宜机场T1 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: passengerCount = 4 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: luggageCount = null 
logger.js:472 [7:29:12 PM] [INFO] 下拉框设置成功: subCategoryId = 3 
logger.js:472 [7:29:12 PM] [INFO] 下拉框设置成功: carTypeId = 35 
logger.js:472 [7:29:12 PM] [INFO] 下拉框设置成功: drivingRegionId = 5 
logger.js:472 [7:29:12 PM] [DEBUG] 自动填充状态: true 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: otaPrice = 57.96 
logger.js:472 [7:29:12 PM] [INFO] 下拉框设置成功: currency = CNY 
logger.js:472 [7:29:12 PM] [INFO] 字段设置成功: extraRequirement = null 
logger.js:469 [7:29:12 PM] [SUCCESS] 🚀 批量DOM更新完成 
form-manager.js:708 [Violation] 'requestAnimationFrame' handler took 81ms
logger.js:472 [7:29:12 PM] [DEBUG] 自动填充状态: false 
logger.js:472 [7:29:16 PM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [7:29:21 PM] [DEBUG] 状态已保存到本地存储 
