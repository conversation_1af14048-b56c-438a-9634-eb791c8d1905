/**
 * 实时分析管理器模块 - 简化架构版本
 * 负责实时订单分析、进度显示和结果处理
 * 
 * 🚀 架构重构说明（2025-07-18）：
 * - 新架构：直接使用 parseOrder() 方法，根据返回数组长度决定单/多订单处理
 * - 自动触发 & 手动解析 现在使用完全相同的代码路径
 * - 废弃了复杂的 detectAndSplitMultiOrdersWithVerification 预检测流程
 * 
 * ⚠️ 重要提醒：
 * - 请勿恢复使用 detectAndSplitMultiOrdersWithVerification 方法
 * - 请勿恢复使用 validateMultiOrderResult 和 analyzeTextFeatures 方法
 * - 新架构更简单、更可靠、更一致
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 实时分析管理器类
     * 负责实时分析相关的所有操作
     */
    class RealtimeAnalysisManager {
        constructor(elements, uiManager) {
            this.elements = elements;
            this.uiManager = uiManager;
            
            // 实时分析相关状态（🔧 修复：移除独立的isAnalyzing状态，使用Gemini服务的统一状态）
            this.realtimeAnalysis = {
                enabled: true,
                debounceTimer: null,
                lastAnalysisTime: 0,
                progressIndicator: null
            };

            // 绑定方法上下文
            this.handleRealtimeInput = this.handleRealtimeInput.bind(this);
        }

        /**
         * 初始化实时分析管理器
         * 🔧 修复：添加依赖检查和降级处理
         */
        init() {
            try {
                // 检查关键依赖
                const geminiService = window.getGeminiService?.();
                if (!geminiService) {
                    getLogger().log('⚠️ Gemini服务不可用，实时分析功能将受限', 'warning');
                }

                // 🎬 集成动画管理器
                this.initializeAnimationManager();

                this.setupRealtimeAnalysis();
                this.createProgressIndicator();
                this.bindInputEvents();

                // 🔧 修复：确保事件绑定成功
                this.verifyEventBinding();

                getLogger().log('✅ 实时分析管理器初始化完成', 'success');
            } catch (error) {
                getLogger().logError('实时分析管理器初始化失败', error);
                // 即使初始化失败，也尝试绑定基本事件
                this.bindInputEvents();
            }
        }

        /**
         * 初始化动画管理器集成
         * 🎬 新增：集成动画管理器，提供实时分析动画支持
         * 🔧 修复：添加延迟重试机制，确保动画管理器可用
         */
        initializeAnimationManager() {
            try {
                // 获取动画管理器实例
                this.animationManager = window.OTA?.animationManager || window.animationManager;

                if (this.animationManager) {
                    getLogger().log('✅ 实时分析管理器动画集成成功', 'success');
                } else {
                    // 🔧 修复：延迟重试，动画管理器可能还在初始化
                    setTimeout(() => {
                        this.animationManager = window.OTA?.animationManager || window.animationManager;
                        if (this.animationManager) {
                            getLogger().log('✅ 实时分析管理器动画集成成功（延迟）', 'success');
                        } else {
                            getLogger().log('⚠️ 动画管理器不可用，实时分析将使用降级方案', 'warning');
                        }
                    }, 1000);
                }
            } catch (error) {
                getLogger().log('实时分析管理器动画集成失败，使用降级方案', 'warning', { error: error.message });
                this.animationManager = null;
            }
        }

        /**
         * 验证事件绑定状态
         * 🔧 新增：确保事件监听器正确绑定
         */
        verifyEventBinding() {
            if (this.elements.orderInput) {
                // 检查是否已有事件监听器
                const hasListener = this.elements.orderInput._realtimeAnalysisListener;

                if (!hasListener) {
                    getLogger().log('🔧 重新绑定实时分析事件监听器', 'info');
                    this.elements.orderInput.addEventListener('input', this.handleRealtimeInput);
                    this.elements.orderInput._realtimeAnalysisListener = true;
                }

                getLogger().log('✅ 实时分析事件绑定验证完成', 'success');
            } else {
                getLogger().log('⚠️ orderInput元素不存在，无法绑定事件', 'warning');
            }
        }

        /**
         * 设置实时分析功能
         * 🚀 新架构说明：使用简化的parseOrder方法，根据返回数组长度决定处理方式
         * 🚀 性能优化：减少防抖延迟和最小输入长度要求
         */
        setupRealtimeAnalysis() {
            // 🚀 性能优化：存储配置参数到实例变量
            this.realtimeAnalysis.debounceDelay = 500; // 从1500ms优化到500ms
            this.realtimeAnalysis.minInputLength = 10; // 从15优化到10

            // 配置实时分析参数（保持向后兼容）
            if (getGeminiService().configureRealtimeAnalysis) {
                getGeminiService().configureRealtimeAnalysis({
                    enabled: true,
                    debounceDelay: this.realtimeAnalysis.debounceDelay,
                    minInputLength: this.realtimeAnalysis.minInputLength,
                    onProgress: (message, progress) => this.handleAnalysisProgress(message, progress),
                    onResult: (result) => this.handleAnalysisResult(result),
                    onError: (error) => this.handleAnalysisError(error)
                });
            }

            getLogger().log('实时分析功能已配置（性能优化版）', 'info', {
                debounceDelay: this.realtimeAnalysis.debounceDelay,
                minInputLength: this.realtimeAnalysis.minInputLength
            });
        }

        /**
         * 创建进度指示器
         * 🎬 增强：添加动画支持
         */
        createProgressIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'realtime-progress';
            indicator.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-text">分析中...</div>
            `;
            indicator.style.display = 'none';

            // 添加样式
            if (!document.getElementById('realtime-progress-styles')) {
                const style = document.createElement('style');
                style.id = 'realtime-progress-styles';
                style.textContent = `
                    .realtime-progress {
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: var(--color-surface);
                        border: 1px solid var(--color-border);
                        border-top: none;
                        border-radius: 0 0 8px 8px;
                        padding: 12px;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }
                    .progress-bar {
                        width: 100%;
                        height: 4px;
                        background: var(--color-border);
                        border-radius: 2px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    }
                    .progress-fill {
                        height: 100%;
                        background: var(--color-primary);
                        border-radius: 2px;
                        transition: width 0.3s ease;
                        width: 0%;
                    }
                    .progress-text {
                        font-size: 12px;
                        color: var(--color-text-secondary);
                        text-align: center;
                    }
                `;
                document.head.appendChild(style);
            }

            // 添加到输入容器
            const inputContainer = this.elements.orderInput?.parentElement;
            if (inputContainer) {
                inputContainer.style.position = 'relative';
                inputContainer.appendChild(indicator);

                // 🎬 添加动画类，确保CSS动画生效
                indicator.classList.add('fade-in');
            }

            this.realtimeAnalysis.progressIndicator = indicator;
        }

        /**
         * 绑定输入事件
         * 🔧 修复：添加更强的错误处理和重试机制
         */
        bindInputEvents() {
            try {
                if (this.elements.orderInput) {
                    // 移除可能存在的旧监听器
                    this.elements.orderInput.removeEventListener('input', this.handleRealtimeInput);

                    // 绑定新监听器
                    this.elements.orderInput.addEventListener('input', this.handleRealtimeInput);
                    this.elements.orderInput.addEventListener('paste', (e) => {
                        // 🚀 修复：标记paste事件，避免重复处理
                        setTimeout(() => {
                            const syntheticEvent = {
                                target: e.target,
                                isTrusted: true,
                                _isPasteEvent: true  // 标记为paste事件
                            };
                            this.handleRealtimeInput(syntheticEvent);
                        }, 100);
                    });

                    // 标记已绑定
                    this.elements.orderInput._realtimeAnalysisListener = true;

                    getLogger().log('✅ 订单输入框事件绑定成功', 'success');
                } else {
                    getLogger().log('⚠️ orderInput元素不存在，无法绑定事件', 'warning');

                    // 延迟重试
                    setTimeout(() => {
                        const orderInput = document.getElementById('orderInput');
                        if (orderInput) {
                            this.elements.orderInput = orderInput;
                            this.bindInputEvents();
                        }
                    }, 1000);
                }

                // 🔧 修复：为所有相关字段绑定语言检测事件
                this.bindLanguageDetectionEvents();
            } catch (error) {
                getLogger().logError('绑定输入事件失败', error);
            }
        }

        /**
         * 绑定语言检测事件到所有相关字段
         * 🔧 重构：使用统一语言检测器，移除重复的事件绑定逻辑
         */
        bindLanguageDetectionEvents() {
            // 检查统一语言检测器是否可用
            if (window.OTA && window.OTA.unifiedLanguageDetector) {
                getLogger().log('🌐 使用统一语言检测器，跳过重复绑定', 'info');
                return;
            }

            // 如果统一检测器不可用，等待其初始化
            const waitForDetector = () => {
                if (window.OTA && window.OTA.unifiedLanguageDetector) {
                    getLogger().log('🌐 统一语言检测器已就绪', 'success');
                } else {
                    setTimeout(waitForDetector, 100);
                }
            };

            waitForDetector();
            getLogger().log('🌐 语言检测事件绑定已委托给统一检测器', 'info');
        }

        /**
         * 处理语言检测输入事件
         * 🔧 重构：已委托给统一语言检测器，此方法保留用于兼容性
         * @param {Event} event - 输入事件
         * @param {boolean} immediate - 是否立即执行（不使用防抖）
         */
        handleLanguageDetectionInput(event, immediate = false) {
            // 委托给统一语言检测器
            if (window.OTA && window.OTA.unifiedLanguageDetector) {
                const text = event.target.value;
                const fieldId = event.target.id || event.target.name || 'unknown';

                if (immediate) {
                    window.OTA.unifiedLanguageDetector.detectAndApply(text, fieldId);
                } else {
                    window.OTA.unifiedLanguageDetector.detectLanguageWithDebounce(text, fieldId);
                }
            } else {
                getLogger().log('⚠️ 统一语言检测器不可用', 'warning');
            }
        }

        /**
         * 设置默认语言选择（英文）
         * 🔧 重构：委托给统一语言检测器
         */
        setDefaultLanguageSelection() {
            // 委托给统一语言检测器
            if (window.OTA && window.OTA.unifiedLanguageDetector) {
                window.OTA.unifiedLanguageDetector.setDefaultLanguage();
            } else {
                getLogger().log('⚠️ 统一语言检测器不可用，无法设置默认语言', 'warning');
            }
        }

        /**
         * 获取当前语言选择
         * @returns {Array} 当前选中的语言ID数组
         */
        getCurrentLanguageSelection() {
            const selectedLanguages = [];
            const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]:checked');

            checkboxes.forEach(checkbox => {
                const value = parseInt(checkbox.value);
                if (!isNaN(value)) {
                    selectedLanguages.push(value);
                }
            });

            return selectedLanguages;
        }

        /**
         * 处理实时输入
         * @param {Event} event - 输入事件
         */
        handleRealtimeInput(event) {
            // 🔧 修复：防止事件递归 - 忽略程序触发的事件
            if (event.isTrusted === false && event._programmaticTrigger) {
                getLogger().log('⚠️ 忽略程序触发的input事件，防止递归', 'info');
                return;
            }

            // 🚀 修复：防止重复调用，特别是paste事件的重复处理
            const currentTime = Date.now();
            const inputText = event.target.value;

            // 检查是否是重复的相同内容调用（100ms内）
            if (this.realtimeAnalysis.lastInputText === inputText &&
                (currentTime - this.realtimeAnalysis.lastInputTime) < 100) {
                getLogger().log('⚠️ 检测到重复输入调用，跳过处理', 'info', {
                    timeDiff: currentTime - this.realtimeAnalysis.lastInputTime,
                    isPasteEvent: event._isPasteEvent
                });
                return;
            }

            // 记录当前输入状态
            this.realtimeAnalysis.lastInputText = inputText;
            this.realtimeAnalysis.lastInputTime = currentTime;
            
            // 清除之前的防抖定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
            }

            // 如果输入为空，清除分析状态
            if (!inputText.trim()) {
                this.clearRealtimeAnalysis();
                this.updateGeminiStatus('请输入订单描述');
                return;
            }

            // 自动检测中文并选择语言（默认英文，含中文时选择中文）
            this.detectAndSetChineseLanguage(inputText);

            // 🚀 性能优化：使用配置的最小输入长度
            const minLength = this.realtimeAnalysis.minInputLength || 10;
            if (inputText.trim().length < minLength) {
                this.updateGeminiStatus(`请继续输入... (${inputText.trim().length}/${minLength})`);
                return;
            }

            // 🚀 性能优化：使用配置的防抖延迟
            const debounceDelay = this.realtimeAnalysis.debounceDelay || 500;
            this.realtimeAnalysis.debounceTimer = setTimeout(() => {
                this.triggerRealtimeAnalysis(inputText);
            }, debounceDelay);

            this.updateGeminiStatus('准备分析...');
        }

        /**
         * 触发实时分析 - 架构修复版
         * 🚀 架构修复：获取之前的渠道检测结果，传递给API调用
         * @param {string} orderText - 订单文本
         */
        async triggerRealtimeAnalysis(orderText) {
            console.group('🔍 多订单数据流追踪 - 第1步：实时分析触发');
            console.log('输入文本长度:', orderText?.length);
            console.log('输入文本预览:', orderText?.substring(0, 200) + '...');
            console.groupEnd();

            if (!orderText || !getGeminiService().isAvailable()) {
                console.warn('❌ 输入验证失败或Gemini服务不可用');
                return;
            }

            // 防止重复分析
            if (getGeminiService().getStatus().isAnalyzing) {
                console.warn('⚠️ 分析正在进行中，跳过');
                return;
            }
            this.realtimeAnalysis.lastAnalysisTime = Date.now();
            this.showProgressIndicator();

            // 🚀 架构修复：获取之前的渠道检测结果
            const channelDetectionResult = this.getStoredChannelDetectionResult();
            console.log('🔍 获取到的渠道检测结果:', channelDetectionResult);

            try {
                console.group('🔍 多订单数据流追踪 - 第2步：调用Gemini解析');
                getLogger().log('🔄 开始实时订单解析...', 'info');
                this.updateGeminiStatus('🤖 AI 解析订单内容...');
                
                // 🚀 架构修复：传递渠道检测结果给parseOrder方法
                const parseOptions = {
                    isRealtime: true,
                    channelDetectionResult: channelDetectionResult
                };
                const parseResult = await getGeminiService().parseOrder(orderText, parseOptions);
                console.log('Gemini parseResult:', parseResult);
                console.groupEnd();
                
                // 添加详细的调试日志
                getLogger().log('🔍 parseOrder返回结果调试', 'info', {
                    parseResult: parseResult,
                    isArray: Array.isArray(parseResult),
                    length: parseResult?.length,
                    type: typeof parseResult
                });
                
                if (!parseResult || !Array.isArray(parseResult) || parseResult.length === 0) {
                    throw new Error('解析失败或无有效订单数据');
                }

                getLogger().log(`✅ 解析完成，检测到 ${parseResult.length} 个订单`, 'success');

                console.group('🔍 多订单数据流追踪 - 第3步：解析结果处理');
                // 🎯 根据解析结果数量决定处理方式
                getLogger().log(`🎯 根据解析结果数量决定处理方式`, 'info', {
                    orderCount: parseResult.length,
                    willTriggerMultiOrder: parseResult.length > 1
                });
                console.log('解析结果数量:', parseResult.length);
                console.log('是否触发多订单:', parseResult.length > 1);
                
                if (parseResult.length > 1) {
                    console.group('🔍 多订单数据流追踪 - 第4步：多订单处理');
                    // 多订单：触发多订单面板
                    this.updateGeminiStatus(`✅ 检测到 ${parseResult.length} 个订单`);
                    getLogger().log(`✅ 检测到 ${parseResult.length} 个订单`, 'success');
                    
                    // 构造与多订单管理器兼容的结果格式
                    const multiOrderResult = {
                        isMultiOrder: true,
                        orderCount: parseResult.length,
                        orders: parseResult,
                        confidence: this.calculateAverageConfidence(parseResult),
                        analysis: `检测到${parseResult.length}个订单`
                    };
                    console.log('构造的multiOrderResult:', multiOrderResult);
                    
                    getLogger().log('🔧 构造多订单结果格式', 'info', {
                        multiOrderResult: multiOrderResult,
                        ordersData: parseResult
                    });
                    
                    // 触发多订单事件
                    const event = new CustomEvent('multiOrderDetected', {
                        detail: {
                            multiOrderResult: multiOrderResult,
                            orderText: orderText
                        }
                    });
                    console.log('创建的event:', event);
                    console.log('event.detail:', event.detail);
                    
                    getLogger().log('🎉 即将触发多订单事件', 'info', {
                        eventType: 'multiOrderDetected',
                        orderCount: multiOrderResult.orderCount,
                        confidence: multiOrderResult.confidence
                    });
                    
                    console.log('即将dispatch事件...');
                    document.dispatchEvent(event);
                    console.log('✅ 事件已dispatch');
                    console.groupEnd();
                    
                    this.hideProgressIndicator();
                    getLogger().log('🎉 多订单事件已触发', 'success');
                    
                } else {
                    // 单订单：使用简化的渠道检测
                    console.group('🔍 单订单数据流追踪 - 第4步：简化渠道检测');
                    this.updateGeminiStatus('✅ 解析完成，正在进行渠道检测...');
                    
                    const orderData = parseResult[0];
                    getLogger().log('🔍 开始单订单渠道检测', 'info', {
                        orderData: orderData,
                        originalText: orderText
                    });

                    // 统一渠道检测 - 使用与多订单相同的检测机制
                    try {
                        if (window.OTA && window.OTA.channelDetector && typeof window.OTA.channelDetector.detectChannel === 'function') {
                            const referenceNumber = orderData.ota_reference_number || '';
                            const channelResult = window.OTA.channelDetector.detectChannel(
                                orderText,
                                referenceNumber,
                                { isSingleOrder: true }
                            );

                            // 将渠道信息添加到订单数据中
                            if (channelResult?.detectedChannel) {
                                const detected = channelResult.detectedChannel; // 小写标准键，如 'fliggy' | 'jingge'
                                orderData._otaChannel = detected;
                                orderData._channelConfidence = channelResult.confidence;

                                // 统一写回到表单下拉，映射成展示名（如 jingge -> Jing Ge）
                                try {
                                    const uiManager = window.OTA?.uiManager;
                                    const formManager = uiManager?.getManager?.('form') || window.OTA?.formManager || window.formManager;
                                    if (formManager && typeof formManager.fillOtaChannelField === 'function') {
                                        formManager.fillOtaChannelField(detected);
                                    }
                                } catch (_) { /* 忽略UI写回失败 */ }

                                // 触发策略切换（通过统一OTAManager入口）
                                try {
                                    const otaManager = window.OTA?.getOTAManager?.() || window.OTA?.serviceLocator?.getService?.('OTAManager') || window.OTA?.serviceLocator?.getService?.('otaManager');
                                    if (otaManager && typeof otaManager.switchToStrategy === 'function') {
                                        otaManager.switchToStrategy(detected);
                                    } else if (window.OTA?.SimpleOTAManager?.switchToStrategy) {
                                        // 回退：兼容旧的SimpleOTAManager
                                        window.OTA.SimpleOTAManager.switchToStrategy(detected);
                                    }
                                } catch (_) { /* 忽略策略切换失败 */ }
                            }

                            getLogger().log('✅ 统一渠道检测完成', 'success', channelResult);
                        } else {
                            getLogger().log('⚠️ 统一渠道检测器未找到', 'warn');
                        }

                    } catch (channelError) {
                        getLogger().log('⚠️ 渠道检测失败，继续处理订单', 'warn', channelError);
                    }

                    // 处理单订单结果
                    this.handleAnalysisResult({
                        success: true,
                        data: orderData,
                        confidence: this.calculateDataConfidence(orderData),
                        timestamp: Date.now(),
                        source: 'parseOrder-realtime'
                    });

                    console.groupEnd();
                    getLogger().log('✅ 单订单处理完成（简化渠道检测）', 'success');
                }
                
            } catch (error) {
                getLogger().logError('实时订单解析失败', error);
                this.handleAnalysisError(error);
            } finally {
                this.realtimeAnalysis.lastAnalysisTime = Date.now();
                getLogger().log('🔄 实时分析处理完成', 'info');
            }
        }

        /**
         * 计算订单数组的平均置信度
         * @param {Array} orders - 订单数组
         * @returns {number} 平均置信度
         */
        calculateAverageConfidence(orders) {
            if (!orders || orders.length === 0) return 0;
            
            const confidences = orders.map(order => this.calculateDataConfidence(order));
            return Math.round(confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length);
        }

        /**
         * ⚠️ 【已废弃】验证多订单检测结果的准确性（故障保险机制）
         * 🚫 此方法已废弃，不再使用！新架构直接使用parseOrder的返回结果
         * 
         * @deprecated 此方法属于过度设计的复杂流程，已被简化架构取代
         * @param {object} result - Gemini返回的原始结果
         * @param {string} orderText - 原始订单文本
         * @returns {object} - 验证后的结果
         */
        validateMultiOrderResult_DEPRECATED(result, orderText) {
            getLogger().log('⚠️ 警告：调用了已废弃的validateMultiOrderResult方法', 'warning');
            // 直接返回原始结果，不进行复杂验证
            return result;
        }

        /**
         * ⚠️ 【已废弃】分析文本特征以验证多订单检测
         * 🚫 此方法已废弃，不再使用！新架构依赖parseOrder的智能解析
         * 
         * @deprecated 此方法属于过度设计的复杂流程，已被简化架构取代
         * @param {string} text - 订单文本
         * @returns {object} - 文本分析结果
         */
        analyzeTextFeatures_DEPRECATED(text) {
            getLogger().log('⚠️ 警告：调用了已废弃的analyzeTextFeatures方法', 'warning');
            // 返回默认结果，不进行复杂分析
            return {
                shouldBeMultiOrder: false,
                estimatedOrderCount: 1,
                hasPagingService: false,
                hasMultipleReferences: false,
                hasMultipleDates: false,
                hasMultipleFlights: false
            };
        }

        /**
         * 处理分析进度
         * @param {string} message - 进度消息
         * @param {number} progress - 进度百分比
         */
        handleAnalysisProgress(message, progress) {
            this.updateGeminiStatus(message);
            this.updateProgressIndicator(progress);
        }

        /**
         * 处理分析结果
         * @param {object} result - 分析结果
         */
        handleAnalysisResult(result) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置
            this.hideProgressIndicator();

            if (result.success && result.data) {
                // 更新应用状态
                getAppState().setCurrentOrder({
                    rawText: this.elements.orderInput.value,
                    parsedData: result.data,
                    confidence: result.confidence || this.calculateDataConfidence(result.data),
                    timestamp: result.timestamp || Date.now(),
                    source: 'realtime'
                });

                // 更新状态显示
                const confidence = result.confidence || this.calculateDataConfidence(result.data);
                this.updateGeminiStatus(`✅ 分析完成 (置信度: ${confidence}%)`);

                // 🚀 性能优化：直接填充表单，跳过预览模态框延迟
                const formManager = getFormManager();
                if (formManager && result.data) {
                    formManager.fillFormFromData(result.data, { isRealtime: true });
                    getLogger().log('🚀 实时表单填充完成', 'success');
                }

                // 显示预览（延迟执行，不阻塞表单填充）
                setTimeout(() => {
                    this.showPreviewModal();
                }, 100);

                // 处理价格转换（延迟执行）
                if (result.data && window.OTA.managers.PriceManager) {
                    setTimeout(() => {
                        const priceManager = new window.OTA.managers.PriceManager(this.elements);
                        priceManager.processPriceConversion(result.data);
                    }, 50);
                }

                // 🔄 改进说明：多订单检测现在在统一入口处理，此处不再需要触发
                // 单订单模式下的数据已经经过统一入口的多订单检测筛选
                // 只有orderCount=1的情况才会到达这里，无需再次触发多订单检测

                getLogger().log('实时分析完成', 'success', {
                    confidence: confidence,
                    dataKeys: Object.keys(result.data)
                });
            } else {
                this.handleAnalysisError(new Error(result.message || '分析失败'));
            }
        }

        /**
         * 处理分析错误
         * @param {Error} error - 错误对象
         */
        handleAnalysisError(error) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置  
            this.hideProgressIndicator();
            this.updateGeminiStatus('❌ 分析失败，请检查输入');
            getLogger().log('实时分析失败', 'error', { error: error.message });
        }

        /**
         * 显示进度指示器
         * 🎬 增强：添加动画支持
         */
        showProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                this.realtimeAnalysis.progressIndicator.style.display = 'block';

                // 🎬 添加显示动画
                this.realtimeAnalysis.progressIndicator.classList.add('show');

                // 添加分析状态类
                this.realtimeAnalysis.progressIndicator.classList.add('analyzing');
            }
        }

        /**
         * 隐藏进度指示器
         * 🎬 增强：添加动画支持
         */
        hideProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                // 🎬 移除动画类
                this.realtimeAnalysis.progressIndicator.classList.remove('show', 'analyzing');

                // 延迟隐藏，等待动画完成
                setTimeout(() => {
                    if (this.realtimeAnalysis.progressIndicator) {
                        this.realtimeAnalysis.progressIndicator.style.display = 'none';
                    }
                }, 300); // 与CSS动画持续时间匹配
            }
        }

        /**
         * 更新进度指示器
         * 🎬 增强：添加动画支持
         * @param {number} progress - 进度百分比
         */
        updateProgressIndicator(progress) {
            if (this.realtimeAnalysis.progressIndicator) {
                const progressFill = this.realtimeAnalysis.progressIndicator.querySelector('.progress-fill');
                if (progressFill) {
                    // 🎬 使用动画管理器更新进度，如果可用
                    if (this.animationManager) {
                        this.animationManager.animateProgress(progressFill, progress);
                    } else {
                        // 降级方案：直接设置宽度
                        progressFill.style.width = `${Math.min(progress, 100)}%`;
                    }
                }
            }
        }

        /**
         * 清除实时分析状态
         */
        clearRealtimeAnalysis() {
            // 清除定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
                this.realtimeAnalysis.debounceTimer = null;
            }

            // 重置状态（🔧 修复：Gemini服务已自动管理isAnalyzing状态）
            this.hideProgressIndicator();
        }

        /**
         * 更新Gemini状态显示
         * @param {string} status - 状态文本
         */
        updateGeminiStatus(status) {
            if (window.OTA.managers.StateManager) {
                const stateManager = new window.OTA.managers.StateManager(this.elements);
                stateManager.updateGeminiStatus(status);
            }
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            if (this.uiManager && this.uiManager.showPreviewModal) {
                this.uiManager.showPreviewModal();
            }
        }

        /**
         * 检测中文字符并自动选择语言
         * 🔧 修复：使用统一语言检测器，添加降级处理
         * @param {string} text - 输入文本
         * @param {string} sourceField - 来源字段ID（可选）
         */
        async detectAndSetChineseLanguage(text, sourceField = 'unknown') {
            try {
                // 优先使用统一语言检测器
                if (window.OTA && window.OTA.unifiedLanguageDetector) {
                    return await window.OTA.unifiedLanguageDetector.detectAndApply(text, sourceField);
                }

                // 🔧 降级处理：如果统一检测器不可用，使用简单的本地检测
                getLogger().log('⚠️ 统一语言检测器不可用，使用降级检测', 'warning', { sourceField });

                const hasChinese = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/.test(text);
                const languageSelect = document.getElementById('language');

                if (languageSelect && hasChinese) {
                    // 查找中文选项
                    const chineseOption = Array.from(languageSelect.options).find(option =>
                        option.textContent.includes('中文') || option.value === '4'
                    );

                    if (chineseOption) {
                        languageSelect.value = chineseOption.value;
                        languageSelect.dispatchEvent(new Event('change', { bubbles: true }));
                        getLogger().log('✅ 降级语言检测：已设置中文', 'success');
                        return true;
                    }
                }

                return false;
            } catch (error) {
                getLogger().logError('自动语言检测失败', error);
                return false;
            }
        }

        /**
         * 计算数据置信度
         * @param {object} orderData - 订单数据
         * @returns {number} 置信度百分比
         */
        calculateDataConfidence(orderData) {
            if (!orderData || typeof orderData !== 'object') {
                return 0;
            }

            // 重要字段权重
            const importantFields = {
                'customer_name': 15,
                'customer_contact': 10,
                'pickup_location': 20,
                'dropoff_location': 20,
                'pickup_date': 15,
                'pickup_time': 10,
                'ota_price': 5,
                'ota_reference_number': 5
            };

            let filledWeight = 0;
            let totalWeight = 0;

            for (const [field, weight] of Object.entries(importantFields)) {
                totalWeight += weight;
                const value = orderData[field];
                if (value !== null && value !== undefined && value !== '' && value !== 0) {
                    filledWeight += weight;
                }
            }

            return Math.round((filledWeight / totalWeight) * 100);
        }

        /**
         * 执行渠道检测
         * @param {string} orderText - 原始订单文本
         * @param {Object} orderData - 解析后的订单数据
            return Math.round((filledWeight / totalWeight) * 100);
        }

        /**
         * 平均信心度计算
         */
        calculateAverageConfidence(orders) {
            if (!orders || orders.length === 0) return 0;
            
            const totalConfidence = orders.reduce((sum, order) => {
                return sum + this.calculateDataConfidence(order);
            }, 0);
            
            return Math.round(totalConfidence / orders.length);
        }

        /**
         * 启用/禁用实时分析
         * @param {boolean} enabled - 是否启用
         */
        setRealtimeAnalysisEnabled(enabled) {
            this.realtimeAnalysis.enabled = enabled;
            
            if (!enabled) {
                this.clearRealtimeAnalysis();
            }

            getLogger().log(`实时分析${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 获取实时分析状态
         * @returns {object} 实时分析状态
         */
        getRealtimeAnalysisStatus() {
            return {
                enabled: this.realtimeAnalysis.enabled,
                isAnalyzing: getGeminiService().getStatus().isAnalyzing, // 🔧 修复：使用Gemini服务的统一状态
                lastAnalysisTime: this.realtimeAnalysis.lastAnalysisTime
            };
        }

        /**
         * 获取存储的渠道检测结果
         * 🚀 架构修复：从AppState获取之前的渠道检测结果
         * @returns {object|null} 渠道检测结果
         */
        getStoredChannelDetectionResult() {
            try {
                const appState = getAppState();
                if (appState) {
                    const channelInfo = appState.get('lastChannelDetection');
                    if (channelInfo && channelInfo.timestamp) {
                        // 检查结果是否还有效（5分钟内）
                        const age = Date.now() - channelInfo.timestamp;
                        if (age < 5 * 60 * 1000) { // 5分钟
                            return channelInfo;
                        } else {
                            getLogger().log('渠道检测结果已过期', 'info', { age: Math.round(age / 1000) + 's' });
                        }
                    }
                }
                return null;
            } catch (error) {
                getLogger().log('获取渠道检测结果失败', 'error', error);
                return null;
            }
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.RealtimeAnalysisManager = RealtimeAnalysisManager;

})();
