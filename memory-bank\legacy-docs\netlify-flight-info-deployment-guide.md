# Netlify 航班信息查询功能部署指南

## 📋 概述

本指南详细说明如何在Netlify上部署航班信息查询功能，使用Netlify Functions作为无服务器后端来解决FlightAware API的CORS问题。

## 🏗️ 架构说明

```
前端 (js/flight-info-service.js)
    ↓ 调用 /api/flight-info
Netlify Functions (netlify/functions/flight-info.js)
    ↓ 代理调用
FlightAware AeroAPI
```

## 🔧 部署步骤

### 1. 获取FlightAware API密钥

1. 访问 [FlightAware AeroAPI](https://flightaware.com/commercial/aeroapi/)
2. 注册账户并申请API访问权限
3. 获取API密钥（格式类似：`E4zN674dWvkwyfJWnH0gOGWMCoMGBdmV`）

### 2. 配置Netlify环境变量

在Netlify控制台中配置环境变量：

1. 登录Netlify控制台
2. 选择你的站点
3. 进入 **Site settings** > **Environment variables**
4. 添加以下环境变量：

```
FLIGHTAWARE_API_KEY = your-actual-api-key-here
```

**重要提示：**
- 不要将API密钥提交到代码仓库
- 确保API密钥保密，不要在前端代码中暴露

### 3. 验证Netlify配置

确保项目根目录的 `netlify.toml` 文件包含以下配置：

```toml
[build]
  publish = "."
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"

# API重定向规则
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
```

### 4. 部署到Netlify

#### 方法一：Git集成部署（推荐）

1. 将代码推送到Git仓库（GitHub、GitLab等）
2. 在Netlify控制台连接你的Git仓库
3. Netlify会自动检测配置并部署

#### 方法二：手动部署

1. 在项目根目录运行：
```bash
# 如果没有安装Netlify CLI
npm install -g netlify-cli

# 登录Netlify
netlify login

# 部署
netlify deploy --prod
```

## 🧪 测试验证

### 1. 本地测试（可选）

使用Netlify CLI在本地测试Functions：

```bash
# 安装依赖
npm install -g netlify-cli

# 设置环境变量（创建.env文件）
echo "FLIGHTAWARE_API_KEY=your-api-key-here" > .env

# 启动本地开发服务器
netlify dev
```

访问：`http://localhost:8888/api/flight-info?flight=MH370`

### 2. 生产环境测试

部署完成后，测试以下端点：

#### 测试API端点
```
GET https://your-site.netlify.app/api/flight-info?flight=MH370
```

#### 预期响应格式
```json
{
  "success": true,
  "data": {
    "flights": [
      {
        "ident": "MH370",
        "fa_flight_id": "MAS370-1234567890",
        "origin": {
          "code": "WMKK",
          "city": "Kuala Lumpur"
        },
        "destination": {
          "code": "ZBAA",
          "city": "Beijing"
        }
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 3. 前端集成测试

使用现有的测试页面验证功能：

1. 访问：`https://your-site.netlify.app/test-flight-info-service.html`
2. 点击航班号示例按钮
3. 检查测试日志，确认显示"通过Netlify Functions查询航班"

## 🔍 故障排除

### 常见问题及解决方案

#### 1. API密钥错误
**错误信息：** `FlightAware API密钥未配置`
**解决方案：**
- 检查Netlify环境变量是否正确设置
- 确认变量名为 `FLIGHTAWARE_API_KEY`
- 重新部署站点以应用环境变量更改

#### 2. CORS错误
**错误信息：** `Access to fetch at '...' from origin '...' has been blocked by CORS policy`
**解决方案：**
- 确认使用 `/api/flight-info` 端点而不是直接调用FlightAware API
- 检查netlify.toml中的重定向规则是否正确

#### 3. Function超时
**错误信息：** `Function execution timed out`
**解决方案：**
- FlightAware API响应较慢时可能发生
- 检查网络连接
- 考虑增加超时时间（当前设置为10秒）

#### 4. 航班未找到
**错误信息：** `FLIGHT_NOT_FOUND`
**解决方案：**
- 确认航班号格式正确（如：MH370, AK6285）
- 检查航班是否存在于FlightAware数据库中
- 尝试使用其他已知的航班号

### 调试方法

#### 1. 查看Netlify Functions日志
1. 进入Netlify控制台
2. 选择站点 > **Functions** 标签
3. 点击 `flight-info` 函数查看执行日志

#### 2. 浏览器开发者工具
1. 打开浏览器开发者工具（F12）
2. 查看 **Network** 标签中的API请求
3. 检查 **Console** 标签中的错误信息

#### 3. 测试API端点
使用curl或Postman测试API端点：

```bash
curl "https://your-site.netlify.app/api/flight-info?flight=MH370"
```

## 📊 性能优化

### 1. 缓存策略
- 前端服务已实现5分钟缓存
- 减少重复API调用
- 提高响应速度

### 2. 防抖机制
- 100ms防抖延迟
- 避免频繁API调用
- 优化用户体验

### 3. 错误处理
- 完善的错误分类和处理
- 用户友好的错误提示
- 自动回退到模拟数据（开发环境）

## 🔒 安全考虑

### 1. API密钥保护
- ✅ API密钥存储在Netlify环境变量中
- ✅ 不在前端代码中暴露API密钥
- ✅ 通过Functions代理API调用

### 2. CORS安全
- ✅ 正确配置CORS头
- ✅ 限制允许的请求方法
- ✅ 验证请求参数

### 3. 输入验证
- ✅ 航班号格式验证
- ✅ 参数存在性检查
- ✅ 错误输入处理

## 📈 监控和维护

### 1. 定期检查
- 监控API调用成功率
- 检查Functions执行时间
- 验证错误处理机制

### 2. 更新维护
- 定期更新FlightAware API密钥
- 监控API使用配额
- 更新依赖和安全补丁

### 3. 性能监控
- 使用Netlify Analytics监控站点性能
- 跟踪Functions执行统计
- 优化响应时间

## 🎯 下一步

部署完成后，你可以：

1. **集成到现有系统**：航班信息查询功能已自动集成到多订单管理器中
2. **自定义UI**：根据需要调整航班信息显示界面
3. **扩展功能**：添加更多航班相关功能（如航班状态通知）
4. **监控使用**：跟踪API使用情况和用户反馈

## 📞 支持

如果遇到问题，请：

1. 查看本指南的故障排除部分
2. 检查Netlify Functions日志
3. 验证FlightAware API密钥和配额
4. 联系技术支持团队

---

**部署完成！** 🎉 你的航班信息查询功能现在已经在Netlify上正常运行了。
