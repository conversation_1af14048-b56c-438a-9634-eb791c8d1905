<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格提取功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-input { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>价格提取功能测试</h1>
    <p>测试Gemini服务增强后的价格提取能力</p>

    <button onclick="runTests()">开始测试</button>
    <button onclick="clearResults()">清除结果</button>

    <div id="results"></div>

    <!-- 引入必要的依赖 -->
    <script src="../js/logger.js"></script>
    <script src="../js/core/service-locator.js"></script>
    <script src="../js/gemini-service.js"></script>

    <script>
        // 测试用例
        const testCases = [
            {
                name: "基础价格关键词",
                text: "价格: 150 MYR",
                expected: { price: 150, currency: "MYR" }
            },
            {
                name: "商家收取格式",
                text: "商家收取 RM 200 的费用",
                expected: { price: 200, currency: "MYR" }
            },
            {
                name: "美元格式",
                text: "总费用 $50 USD",
                expected: { price: 50, currency: "USD" }
            },
            {
                name: "人民币格式",
                text: "服务费: ¥300人民币",
                expected: { price: 300, currency: "CNY" }
            },
            {
                name: "英文关键词",
                text: "Total cost: $75",
                expected: { price: 75, currency: "USD" }
            },
            {
                name: "带千位分隔符",
                text: "金额: 1,500 MYR",
                expected: { price: 1500, currency: "MYR" }
            },
            {
                name: "小数价格",
                text: "费用: RM 99.50",
                expected: { price: 99.50, currency: "MYR" }
            },
            {
                name: "复杂文本中的价格",
                text: "客户姓名: 张三, 联系电话: 123456789, 价格信息: 总计 RM 180, 备注: 包含税费",
                expected: { price: 180, currency: "MYR" }
            },
            {
                name: "无价格信息",
                text: "客户姓名: 李四, 联系电话: 987654321, 目的地: 机场",
                expected: { price: null, currency: "MYR" }
            }
        ];

        function runTests() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>测试结果</h2>';

            // 创建模拟的Gemini服务实例
            const geminiService = new window.GeminiAIService();

            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                try {
                    const result = geminiService.extractPriceAndCurrency(testCase.text);
                    const success = compareResults(result, testCase.expected);
                    
                    testDiv.innerHTML = `
                        <h3>测试 ${index + 1}: ${testCase.name}</h3>
                        <div class="test-input"><strong>输入:</strong> ${testCase.text}</div>
                        <div class="test-result ${success ? 'success' : 'error'}">
                            <strong>期望:</strong> 价格=${testCase.expected.price}, 货币=${testCase.expected.currency}<br>
                            <strong>实际:</strong> 价格=${result.price}, 货币=${result.currency}<br>
                            <strong>结果:</strong> ${success ? '✅ 通过' : '❌ 失败'}
                            ${result.source ? `<br><strong>来源:</strong> ${result.source}` : ''}
                        </div>
                    `;
                } catch (error) {
                    testDiv.innerHTML = `
                        <h3>测试 ${index + 1}: ${testCase.name}</h3>
                        <div class="test-input"><strong>输入:</strong> ${testCase.text}</div>
                        <div class="test-result error">
                            <strong>错误:</strong> ${error.message}
                        </div>
                    `;
                }
                
                resultsDiv.appendChild(testDiv);
            });

            // 显示测试统计
            const passedTests = document.querySelectorAll('.success').length;
            const totalTests = testCases.length;
            const statsDiv = document.createElement('div');
            statsDiv.className = 'test-result info';
            statsDiv.innerHTML = `<h3>测试统计: ${passedTests}/${totalTests} 通过 (${Math.round(passedTests/totalTests*100)}%)</h3>`;
            resultsDiv.insertBefore(statsDiv, resultsDiv.firstChild.nextSibling);
        }

        function compareResults(actual, expected) {
            return actual.price === expected.price && actual.currency === expected.currency;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 确保日志系统可用
        if (!window.getLogger) {
            window.getLogger = () => ({
                log: console.log,
                logError: console.error
            });
        }
    </script>
</body>
</html>