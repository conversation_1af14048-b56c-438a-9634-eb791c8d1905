<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航班号字段API映射测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .test-input {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✈️ 航班号字段API映射测试</h1>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button onclick="testFormManagerMapping()">🔄 测试表单管理器映射</button>
            <button onclick="testApiServiceMapping()">🚀 测试API服务映射</button>
            <button onclick="testEndToEndMapping()">🔗 测试端到端映射</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div class="test-section">
            <h2>📋 表单管理器映射测试</h2>
            <div id="formManagerTests"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 API服务映射测试</h2>
            <div id="apiServiceTests"></div>
        </div>
        
        <div class="test-section">
            <h2>🔗 端到端映射测试</h2>
            <div id="endToEndTests"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/managers/form-manager.js"></script>
    
    <script>
        // 测试用例数据
        const testData = {
            formData: {
                customerName: '张三',
                customerContact: '+60123456789',
                pickup: 'KLIA2 Terminal',
                dropoff: '双子塔',
                pickupDate: '2025-01-25',
                pickupTime: '15:30',
                flightInfo: 'MH123',  // 关键测试字段
                passengerCount: 2,
                luggageCount: 3,
                otaPrice: 150.00,
                otaReferenceNumber: 'TEST123456'
            },
            
            geminiData: {
                customer_name: '李四',
                customer_contact: '+60123456789',
                pickup_location: 'KLIA2 Terminal',
                dropoff_location: '双子塔',
                pickup_date: '2025-01-25',
                pickup_time: '15:30',
                flight_number: 'CZ351',  // Gemini返回的航班号字段
                passenger_count: 2,
                luggage_count: 3,
                ota_price: 150.00,
                ota_reference_number: 'TEST123456'
            }
        };

        // 测试表单管理器映射
        function testFormManagerMapping() {
            const container = document.getElementById('formManagerTests');
            container.innerHTML = '';
            
            try {
                // 模拟表单管理器的getFormData方法
                const fieldMapping = {
                    'customerName': 'customer_name',
                    'customerContact': 'customer_contact',
                    'pickup': 'pickup_location',
                    'dropoff': 'dropoff_location',
                    'pickupDate': 'pickup_date',
                    'pickupTime': 'pickup_time',
                    'flightInfo': 'flight_info',  // 关键映射
                    'passengerCount': 'passenger_number',
                    'luggageCount': 'luggage_count',
                    'otaPrice': 'ota_price',
                    'otaReferenceNumber': 'ota_reference_number'
                };
                
                const mappedData = {};
                Object.keys(testData.formData).forEach(key => {
                    const mappedKey = fieldMapping[key] || key;
                    mappedData[mappedKey] = testData.formData[key];
                });
                
                // 检查航班号字段是否正确映射
                const testCase = document.createElement('div');
                testCase.className = 'test-case';
                
                const hasFlightInfo = mappedData.hasOwnProperty('flight_info');
                const flightInfoValue = mappedData.flight_info;
                
                testCase.innerHTML = `
                    <div class="test-input">测试: 表单字段 flightInfo → API字段 flight_info</div>
                    <div class="test-result ${hasFlightInfo ? 'success' : 'error'}">
                        ${hasFlightInfo ? '✅' : '❌'} 映射结果: ${hasFlightInfo ? '成功' : '失败'}
                        <br>原始值: ${testData.formData.flightInfo}
                        <br>映射后: ${flightInfoValue || 'undefined'}
                    </div>
                    <div class="json-display">${JSON.stringify(mappedData, null, 2)}</div>
                `;
                
                container.appendChild(testCase);
                
            } catch (error) {
                const errorCase = document.createElement('div');
                errorCase.className = 'test-case';
                errorCase.innerHTML = `
                    <div class="test-input">表单管理器映射测试</div>
                    <div class="test-result error">❌ 测试异常: ${error.message}</div>
                `;
                container.appendChild(errorCase);
            }
        }

        // 测试API服务映射
        function testApiServiceMapping() {
            const container = document.getElementById('apiServiceTests');
            container.innerHTML = '';
            
            try {
                // 模拟API服务的preprocessOrderData方法
                const fieldMapping = {
                    'flightInfo': 'flight_info',    // 来自表单的航班号字段
                    'flight_number': 'flight_info', // 来自Gemini的航班号字段
                    'pickup_location': 'pickup',
                    'dropoff_location': 'destination',
                    'pickup_date': 'date',
                    'pickup_time': 'time',
                    'luggage_count': 'luggage_number',
                    'passenger_count': 'passenger_number',
                    'price': 'ota_price',
                    'otaPrice': 'ota_price'
                };
                
                // 测试表单数据映射
                const formMappedData = { ...testData.formData };
                Object.keys(fieldMapping).forEach(oldField => {
                    if (formMappedData[oldField] !== undefined) {
                        const newField = fieldMapping[oldField];
                        if (newField !== oldField) {
                            formMappedData[newField] = formMappedData[oldField];
                            delete formMappedData[oldField];
                        }
                    }
                });
                
                // 测试Gemini数据映射
                const geminiMappedData = { ...testData.geminiData };
                Object.keys(fieldMapping).forEach(oldField => {
                    if (geminiMappedData[oldField] !== undefined) {
                        const newField = fieldMapping[oldField];
                        if (newField !== oldField) {
                            geminiMappedData[newField] = geminiMappedData[oldField];
                            delete geminiMappedData[oldField];
                        }
                    }
                });
                
                // 显示测试结果
                const testCase1 = document.createElement('div');
                testCase1.className = 'test-case';
                const hasFormFlightInfo = formMappedData.hasOwnProperty('flight_info');
                
                testCase1.innerHTML = `
                    <div class="test-input">测试: 表单数据 → API数据 (flightInfo → flight_info)</div>
                    <div class="test-result ${hasFormFlightInfo ? 'success' : 'error'}">
                        ${hasFormFlightInfo ? '✅' : '❌'} 映射结果: ${hasFormFlightInfo ? '成功' : '失败'}
                        <br>原始值: ${testData.formData.flightInfo}
                        <br>API字段值: ${formMappedData.flight_info || 'undefined'}
                    </div>
                    <div class="json-display">${JSON.stringify(formMappedData, null, 2)}</div>
                `;
                
                const testCase2 = document.createElement('div');
                testCase2.className = 'test-case';
                const hasGeminiFlightInfo = geminiMappedData.hasOwnProperty('flight_info');
                
                testCase2.innerHTML = `
                    <div class="test-input">测试: Gemini数据 → API数据 (flight_number → flight_info)</div>
                    <div class="test-result ${hasGeminiFlightInfo ? 'success' : 'error'}">
                        ${hasGeminiFlightInfo ? '✅' : '❌'} 映射结果: ${hasGeminiFlightInfo ? '成功' : '失败'}
                        <br>原始值: ${testData.geminiData.flight_number}
                        <br>API字段值: ${geminiMappedData.flight_info || 'undefined'}
                    </div>
                    <div class="json-display">${JSON.stringify(geminiMappedData, null, 2)}</div>
                `;
                
                container.appendChild(testCase1);
                container.appendChild(testCase2);
                
            } catch (error) {
                const errorCase = document.createElement('div');
                errorCase.className = 'test-case';
                errorCase.innerHTML = `
                    <div class="test-input">API服务映射测试</div>
                    <div class="test-result error">❌ 测试异常: ${error.message}</div>
                `;
                container.appendChild(errorCase);
            }
        }

        // 测试端到端映射
        function testEndToEndMapping() {
            const container = document.getElementById('endToEndTests');
            container.innerHTML = '';
            
            try {
                // 模拟完整的数据流：表单 → 表单管理器 → API服务 → GoMyHire API
                
                // 步骤1: 表单数据 → 表单管理器映射
                const formManagerMapping = {
                    'flightInfo': 'flight_info'
                };
                
                const step1Data = { ...testData.formData };
                Object.keys(formManagerMapping).forEach(key => {
                    if (step1Data[key] !== undefined) {
                        const newKey = formManagerMapping[key];
                        step1Data[newKey] = step1Data[key];
                        delete step1Data[key];
                    }
                });
                
                // 步骤2: API服务预处理 (这里flight_info应该保持不变)
                const apiServiceMapping = {
                    'pickup': 'pickup_location',
                    'dropoff': 'dropoff_location',
                    'pickupDate': 'pickup_date',
                    'pickupTime': 'pickup_time'
                };
                
                const step2Data = { ...step1Data };
                Object.keys(apiServiceMapping).forEach(key => {
                    if (step2Data[key] !== undefined) {
                        const newKey = apiServiceMapping[key];
                        step2Data[newKey] = step2Data[key];
                        delete step2Data[key];
                    }
                });
                
                // 验证最终结果
                const hasFlightInfo = step2Data.hasOwnProperty('flight_info');
                const flightInfoValue = step2Data.flight_info;
                
                const testCase = document.createElement('div');
                testCase.className = 'test-case';
                
                testCase.innerHTML = `
                    <div class="test-input">端到端测试: 表单 → 表单管理器 → API服务 → GoMyHire API</div>
                    <div class="test-result ${hasFlightInfo ? 'success' : 'error'}">
                        ${hasFlightInfo ? '✅' : '❌'} 最终结果: ${hasFlightInfo ? '成功' : '失败'}
                        <br>原始表单值: ${testData.formData.flightInfo}
                        <br>最终API字段: flight_info = ${flightInfoValue || 'undefined'}
                        <br>数据流完整性: ${hasFlightInfo && flightInfoValue === testData.formData.flightInfo ? '✅ 完整' : '❌ 丢失'}
                    </div>
                    <div class="json-display">${JSON.stringify(step2Data, null, 2)}</div>
                `;
                
                container.appendChild(testCase);
                
            } catch (error) {
                const errorCase = document.createElement('div');
                errorCase.className = 'test-case';
                errorCase.innerHTML = `
                    <div class="test-input">端到端映射测试</div>
                    <div class="test-result error">❌ 测试异常: ${error.message}</div>
                `;
                container.appendChild(errorCase);
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('formManagerTests').innerHTML = '';
            document.getElementById('apiServiceTests').innerHTML = '';
            document.getElementById('endToEndTests').innerHTML = '';
        }

        // 页面加载时自动运行测试
        window.addEventListener('DOMContentLoaded', function() {
            console.log('航班号字段API映射测试页面已加载');
        });
    </script>
</body>
</html>
