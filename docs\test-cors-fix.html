<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS修复验证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-title {
            font-weight: bold;
            color: #34495e;
            margin-bottom: 10px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CORS修复验证测试</h1>
        
        <div class="test-section">
            <div class="test-title">📊 系统状态检查</div>
            <button onclick="checkSystemStatus()">检查系统状态</button>
            <div id="systemStatus"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🏨 酒店数据源测试</div>
            <button onclick="testHotelDataSources()">测试数据源</button>
            <div id="hotelDataResults"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 酒店查询测试</div>
            <button onclick="testHotelQueries()">测试酒店查询</button>
            <div id="queryResults"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📈 性能统计</div>
            <button onclick="showPerformanceStats()">显示统计</button>
            <div id="performanceStats"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🚨 CORS错误检查</div>
            <button onclick="checkCORSErrors()">检查CORS错误</button>
            <div id="corsResults"></div>
        </div>
    </div>

    <!-- 加载OTA系统脚本 -->
    <script src="js/core/script-loader.js"></script>
    
    <script>
        // 等待系统加载完成
        let systemReady = false;
        
        // 监听系统启动完成
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (window.OTA && window.OTA.container) {
                    systemReady = true;
                    log('systemStatus', '✅ OTA系统已加载', 'success');
                } else {
                    log('systemStatus', '⚠️ OTA系统未完全加载，请等待...', 'warning');
                    // 继续等待
                    setTimeout(() => {
                        if (window.OTA && window.OTA.container) {
                            systemReady = true;
                            log('systemStatus', '✅ OTA系统延迟加载完成', 'success');
                        }
                    }, 5000);
                }
            }, 3000);
        });

        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function checkSystemStatus() {
            clearResults('systemStatus');
            
            log('systemStatus', '🔍 检查系统组件...', 'info');
            
            // 检查核心组件
            const checks = [
                { name: 'OTA命名空间', check: () => !!window.OTA },
                { name: '依赖容器', check: () => !!window.OTA?.container },
                { name: '服务定位器', check: () => !!window.OTA?.serviceLocator },
                { name: 'GeminiService', check: () => !!window.getGeminiService?.() },
                { name: '知识库', check: () => !!window.OTA?.knowledgeBase },
                { name: '完整酒店数据', check: () => !!window.completeHotelData?.loaded },
                { name: '精简酒店数据', check: () => !!window.inlineHotelData?.loaded }
            ];

            checks.forEach(({ name, check }) => {
                try {
                    const result = check();
                    log('systemStatus', `${name}: ${result ? '✅ 可用' : '❌ 不可用'}`, result ? 'success' : 'error');
                } catch (error) {
                    log('systemStatus', `${name}: ❌ 错误 - ${error.message}`, 'error');
                }
            });
        }

        function testHotelDataSources() {
            clearResults('hotelDataResults');
            
            log('hotelDataResults', '🔍 测试酒店数据源...', 'info');
            
            // 测试完整数据
            if (window.completeHotelData?.loaded) {
                log('hotelDataResults', `✅ 完整数据: ${window.completeHotelData.totalHotels} 家酒店`, 'success');
                
                // 测试查询功能
                try {
                    const testResult = window.completeHotelData.queryHotel('香格里拉酒店');
                    if (testResult) {
                        log('hotelDataResults', `✅ 查询测试: ${testResult.chinese} → ${testResult.english}`, 'success');
                    } else {
                        log('hotelDataResults', '⚠️ 查询测试: 未找到结果', 'warning');
                    }
                } catch (error) {
                    log('hotelDataResults', `❌ 查询测试失败: ${error.message}`, 'error');
                }
            } else {
                log('hotelDataResults', '❌ 完整数据未加载', 'error');
            }
            
            // 测试精简数据
            if (window.inlineHotelData?.loaded) {
                log('hotelDataResults', `✅ 精简数据: ${window.inlineHotelData.totalHotels} 家酒店`, 'success');
            } else {
                log('hotelDataResults', '⚠️ 精简数据未加载', 'warning');
            }
            
            // 测试知识库
            try {
                const knowledgeBase = window.OTA?.knowledgeBase;
                if (knowledgeBase && typeof knowledgeBase.queryHotel === 'function') {
                    const result = knowledgeBase.queryHotel('香格里拉酒店');
                    if (result) {
                        log('hotelDataResults', `✅ 知识库查询: ${result.source} (置信度: ${result.confidence})`, 'success');
                    } else {
                        log('hotelDataResults', '⚠️ 知识库查询: 未找到结果', 'warning');
                    }
                } else {
                    log('hotelDataResults', '❌ 知识库不可用', 'error');
                }
            } catch (error) {
                log('hotelDataResults', `❌ 知识库测试失败: ${error.message}`, 'error');
            }
        }

        function testHotelQueries() {
            clearResults('queryResults');
            
            const testQueries = [
                '香格里拉酒店',
                '希尔顿酒店',
                '万豪酒店',
                '吉隆坡国际机场',
                '双子塔',
                '不存在的酒店'
            ];
            
            log('queryResults', '🔍 测试酒店查询功能...', 'info');
            
            testQueries.forEach(query => {
                try {
                    const knowledgeBase = window.OTA?.knowledgeBase;
                    if (knowledgeBase) {
                        const result = knowledgeBase.queryHotel(query);
                        if (result) {
                            log('queryResults', `✅ "${query}" → ${result.english} (${result.source})`, 'success');
                        } else {
                            log('queryResults', `⚠️ "${query}" → 未找到`, 'warning');
                        }
                    }
                } catch (error) {
                    log('queryResults', `❌ "${query}" → 错误: ${error.message}`, 'error');
                }
            });
        }

        function showPerformanceStats() {
            clearResults('performanceStats');
            
            const statsContainer = document.getElementById('performanceStats');
            
            // 创建统计卡片
            const statsDiv = document.createElement('div');
            statsDiv.className = 'stats';
            
            const stats = [
                { 
                    label: '完整数据酒店数', 
                    value: window.completeHotelData?.totalHotels || 0 
                },
                { 
                    label: '精简数据酒店数', 
                    value: window.inlineHotelData?.totalHotels || 0 
                },
                { 
                    label: '系统组件数', 
                    value: Object.keys(window.OTA || {}).length 
                },
                { 
                    label: '脚本加载错误', 
                    value: window.OTA?.loaderStats?.errors?.length || 0 
                }
            ];
            
            stats.forEach(stat => {
                const card = document.createElement('div');
                card.className = 'stat-card';
                card.innerHTML = `
                    <div class="stat-number">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                `;
                statsDiv.appendChild(card);
            });
            
            statsContainer.appendChild(statsDiv);
            
            // 显示详细信息
            if (window.completeHotelData?.getStats) {
                const detailStats = window.completeHotelData.getStats();
                log('performanceStats', `📊 详细统计: ${JSON.stringify(detailStats, null, 2)}`, 'info');
            }
        }

        function checkCORSErrors() {
            clearResults('corsResults');
            
            log('corsResults', '🔍 检查CORS相关错误...', 'info');
            
            // 检查控制台错误
            const errors = window.OTA?.loaderStats?.errors || [];
            const corsErrors = errors.filter(error => 
                error.message && error.message.includes('CORS')
            );
            
            if (corsErrors.length === 0) {
                log('corsResults', '✅ 未发现CORS错误', 'success');
            } else {
                corsErrors.forEach(error => {
                    log('corsResults', `❌ CORS错误: ${error.message}`, 'error');
                });
            }
            
            // 测试数据加载方式
            if (window.completeHotelData?.loaded) {
                log('corsResults', '✅ 使用内联数据，已避免CORS问题', 'success');
            } else {
                log('corsResults', '⚠️ 未使用内联数据，可能存在CORS风险', 'warning');
            }
            
            // 检查知识库数据源
            try {
                const knowledgeBase = window.OTA?.knowledgeBase;
                if (knowledgeBase) {
                    const status = knowledgeBase.getKnowledgeBaseStatus?.();
                    if (status?.hotelKnowledgeBase?.source) {
                        log('corsResults', `✅ 当前数据源: ${status.hotelKnowledgeBase.source}`, 'success');
                    }
                }
            } catch (error) {
                log('corsResults', `❌ 检查数据源失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
