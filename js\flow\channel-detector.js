/**
 * ============================================================================
 * 🚀 核心业务流程 - 渠道检测器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 渠道检测器 - 子层实现
 * @description 负责本地渠道特征检测，不调用Gemini API
 * 
 * @businessFlow 本地渠道特征检测
 * 在核心业务流程中的位置：
 * 输入内容（文字/图片）
 *     ↓
 * 【当前文件职责】本地渠道特征检测 - 本地处理
 *     ↓
 * A1. 无渠道特征 → 使用通用提示词 (prompt-builder.js)
 * A2. 有渠道特征 → 调取渠道策略文件提示词片段 → 组合提示词
 *     ↓
 * 发送Gemini API (gemini-caller.js) → 结果处理 → 订单管理
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：渠道检测的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供检测服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 Fliggy渠道特征检测：/订单编号[：:\s]*\d{19}/
 * - 🟢 JingGe渠道特征检测：/jingge|jinggeshop|精格|精格商铺/i
 * - 🟢 参考号模式检测：/^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i
 * - 🟢 关键词匹配检测：渠道名称直接匹配
 * - 🟢 置信度计算和结果封装
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地处理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有检测逻辑不变
 * - 兼容现有的检测结果格式
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用Gemini API（严格本地处理）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持检测准确率
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 渠道检测器 - 子层实现
     */
    class ChannelDetector {
        constructor() {
            this.logger = this.getLogger();
            
            // 渠道检测规则配置
            this.detectionRules = {
                // Fliggy渠道检测规则
                fliggy: {
                    patterns: [
                        /订单编号[：:\s]*\d{19}/,  // 🚀 修复：移除全局标志，避免lastIndex状态问题
                    ],
                    confidence: 0.95,
                    channel: 'fliggy'
                },

                // JingGe渠道检测规则
                jingge: {
                    patterns: [
                        /商铺/              // 🚀 修复：移除全局标志
                    ],
                    confidence: 0.85,
                    channel: 'jingge'
                },
                
                // 参考号模式检测
                referencePatterns: {
                    'CD': { channel: 'Chong Dealer', confidence: 0.95 },
                    'CT': { channel: 'Ctrip', confidence: 0.9 },
                    'KL': { channel: 'Klook West Malaysia', confidence: 0.9 },
                    'KK': { channel: 'Kkday', confidence: 0.9 }
                }
            };
            
            this.logger.log('渠道检测器已初始化', 'info');
        }

        /**
         * 检测渠道
         * @param {string} input - 输入内容
         * @param {object} options - 检测选项
         * @returns {Promise<object>} 检测结果
         */
        async detectChannel(input, options = {}) {
            try {
                if (!input || typeof input !== 'string') {
                    return { channel: null, confidence: 0, method: 'invalid_input' };
                }

                this.logger.log('开始渠道检测', 'info', { inputLength: input.length });

                // 1. Fliggy渠道检测
                const fliggyResult = this.detectFliggy(input);
                if (fliggyResult.confidence > 0.8) {
                    this.logger.log('检测到Fliggy渠道', 'success', fliggyResult);
                    return fliggyResult;
                }

                // 2. JingGe渠道检测
                const jinggeResult = this.detectJingGe(input);
                if (jinggeResult.confidence > 0.8) {
                    this.logger.log('检测到JingGe渠道', 'success', jinggeResult);
                    return jinggeResult;
                }

                // 3. 参考号模式检测
                const referenceResult = this.detectByReference(input);
                if (referenceResult.confidence > 0.8) {
                    this.logger.log('通过参考号检测到渠道', 'success', referenceResult);
                    return referenceResult;
                }

                // 4. 关键词检测
                const keywordResult = this.detectByKeywords(input);
                if (keywordResult.confidence > 0.6) {
                    this.logger.log('通过关键词检测到渠道', 'info', keywordResult);
                    return keywordResult;
                }

                // 未检测到任何渠道
                this.logger.log('未检测到特定渠道', 'info');
                return { channel: null, confidence: 0, method: 'no_match' };

            } catch (error) {
                this.logger.log('渠道检测失败', 'error', { error: error.message });
                return { channel: null, confidence: 0, method: 'error', error: error.message };
            }
        }

        /**
         * 检测Fliggy渠道
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectFliggy(text) {
            const rules = this.detectionRules.fliggy;
            let maxConfidence = 0;
            let matchedPattern = null;

            for (const pattern of rules.patterns) {
                const matches = text.match(pattern);
                if (matches && matches.length > 0) {
                    // 订单编号+19位数字的匹配置信度最高
                    if (pattern.source.includes('订单编号')) {
                        maxConfidence = 0.95;
                        matchedPattern = '订单编号+19位数字';
                        break;
                    } else {
                        maxConfidence = Math.max(maxConfidence, 0.85);
                        matchedPattern = pattern.source;
                    }
                }
            }

            return {
                channel: maxConfidence > 0 ? rules.channel : null,
                confidence: maxConfidence,
                method: 'fliggy_pattern',
                matchedPattern
            };
        }

        /**
         * 检测JingGe渠道
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectJingGe(text) {
            const rules = this.detectionRules.jingge;
            let maxConfidence = 0;
            let matchedPattern = null;

            for (const pattern of rules.patterns) {
                const matches = text.match(pattern);
                if (matches && matches.length > 0) {
                    maxConfidence = rules.confidence;
                    matchedPattern = pattern.source;
                    break;
                }
            }

            return {
                channel: maxConfidence > 0 ? rules.channel : null,
                confidence: maxConfidence,
                method: 'jingge_pattern',
                matchedPattern
            };
        }

        /**
         * 基于参考号检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectByReference(text) {
            const referencePattern = /\b([A-Z]{2})[A-Z0-9]{6,12}\b/g;
            const matches = text.match(referencePattern);

            if (matches && matches.length > 0) {
                for (const match of matches) {
                    const prefix = match.substring(0, 2);
                    const rule = this.detectionRules.referencePatterns[prefix];
                    
                    if (rule) {
                        return {
                            channel: rule.channel,
                            confidence: rule.confidence,
                            method: 'reference_pattern',
                            matchedReference: match
                        };
                    }
                }
            }

            return { channel: null, confidence: 0, method: 'reference_no_match' };
        }

        /**
         * 基于关键词检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectByKeywords(text) {
            const lowerText = text.toLowerCase();
            const keywords = {
                'chong dealer': { channel: 'Chong Dealer', confidence: 0.9 },
                '携程': { channel: 'Ctrip', confidence: 0.85 },
                'ctrip': { channel: 'Ctrip', confidence: 0.85 },
                'klook': { channel: 'Klook West Malaysia', confidence: 0.85 },
                'kkday': { channel: 'Kkday', confidence: 0.85 },
                'traveloka': { channel: 'Traveloka', confidence: 0.8 }
            };

            for (const [keyword, rule] of Object.entries(keywords)) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    return {
                        channel: rule.channel,
                        confidence: rule.confidence,
                        method: 'keyword_match',
                        matchedKeyword: keyword
                    };
                }
            }

            return { channel: null, confidence: 0, method: 'keyword_no_match' };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 导出到全局作用域（仅导出类，实例由依赖容器统一管理）
    window.ChannelDetector = ChannelDetector;
    window.OTA.ChannelDetector = ChannelDetector;

    console.log('✅ ChannelDetector (子层实现) 已加载');

})();
