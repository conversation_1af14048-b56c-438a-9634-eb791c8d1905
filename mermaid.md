---
type: "manual"
---

graph TD
    %% 启动与加载层（根据 script-manifest.js / script-loader.js / main.js）
    subgraph "🚀 启动与加载层"
    MANI[脚本清单\nScript Manifest]
    LOADER[脚本加载器\nScriptLoader]
    BOOT[应用启动器\nApplicationBootstrap]
    DCN[依赖容器\nDependencyContainer]
    SLOC[服务定位器\nServiceLocator]
    end

    subgraph "🎯 用户交互层"
        UI[用户界面<br/>订单输入框]
        FORM[表单管理器<br/>FormManager]
        UIM[UI管理器<br/>UIManager]
    PRICE[价格管理器<br/>PriceManager]
    STATE[状态管理器<br/>StateManager]
    EVT[事件管理器<br/>EventManager]
    end
    
    subgraph "🔄 实时处理层"
        RTM[实时分析管理器<br/>RealtimeAnalysisManager]
        LD[语言检测器<br/>LanguageDetector]
    end
    
    subgraph "🧠 业务控制层"
        BFC[业务流控制器<br/>BusinessFlowController]
        GSA[Gemini服务适配器<br/>GeminiServiceAdapter]
    OMC[订单管理控制器<br/>OrderManagementController]
    end
    
    %% 母子架构：订单子层（由 OMC 协调）
    subgraph "🧩 订单子层"
        MOH[多订单处理器<br/>MultiOrderHandler]
        APIC[API调用器<br/>APICaller]
        UIC[UI控制器<br/>UIController]
        HM[历史管理器<br/>HistoryManager]
    end
    
    subgraph "🔧 核心处理层"
        CD[渠道检测器<br/>ChannelDetector]
        PB[提示词构建器<br/>PromptBuilder]
        GC[Gemini调用器<br/>GeminiCaller]
        RP[结果处理器<br/>ResultProcessor]
    OP[订单解析器<br/>OrderParser]
    AT[地址翻译器<br/>AddressTranslator]
    end
    
    subgraph "🌐 外部服务层"
        API[Gemini API<br/>Google AI]
    end
    
    subgraph "📊 支持服务层"
        LOG[日志系统<br/>Logger]
        CACHE[缓存系统<br/>RequestCache]
        ANIM[动画管理器<br/>AnimationManager]
    CCV[货币转换器<br/>CurrencyConverter]
    OTAM[OTA通道管理器<br/>OTAManager]
    KB[知识库管理器<br/>KnowledgeBase]
    FOTAS[Fliggy策略<br/>FliggyOTAStrategy]
    JOTAS[JingGe策略<br/>JingGeOTAStrategy]
    APISVC[API服务<br/>ApiService]
    GFSL[字段标准化层<br/>GlobalFieldStandardizationLayer]
    OHM[订单历史管理器<br/>OrderHistoryManager]
    APP[应用状态<br/>AppState]
    UDM[统一数据管理器<br/>UnifiedDataManager]
    VCI[车辆配置集成<br/>VehicleConfigIntegration]
    end
    
    %% 主要数据流
    MANI --> LOADER
    LOADER --> BOOT
    BOOT -.-> DCN
    BOOT -.-> SLOC
    UI --> RTM
        UIM --> FORM
        UIM --> PRICE
        UIM --> STATE
        UIM --> EVT
        UIM --> RTM
    RTM --> LD
    LD --> BFC
    RTM --> GSA
    GSA --> BFC
        %% 语言检测器不直接调用业务流控制器（保留禁用路径标注）
    BFC --> CD
    BFC --> PB
    BFC --> GC
    BFC --> RP
        
    
        %% 多订单系统（js/multi-order/*）
        subgraph "📦 多订单系统"
            MOP[多订单处理器<br/>MultiOrderProcessor]
            BATCH[批量处理器<br/>BatchProcessor]
        end
        MOP --> APISVC
        BATCH --> APISVC
        MOP -.-> OHM
    BFC --> OMC
    GC --> API
    RP --> RTM
    RTM --> FORM
        SLOC -.-> MOH
        SLOC -.-> APIC
        UIM --> FORM
        class BFC,GSA,OMC,MOP,BATCH businessLayer
    EVT --> FORM
    RP --> OMC
    
    %% 订单子层依赖（由 OMC 协调）
    OMC --> MOH
    OMC --> APIC
    OMC -.-> UIC
    OMC --> HM
    
    GSA --> AT
    AT --> KB
    PB --> FOTAS
    PB --> JOTAS
    
    %% 支持服务连接
    RTM -.-> LOG
    BFC -.-> LOG
    GC -.-> LOG
    GC -.-> CACHE
    FORM -.-> ANIM
    RTM -.-> ANIM
    PRICE -.-> ANIM
    STATE -.-> ANIM
    PRICE -.-> CCV
    EVT -.-> LOG
    STATE -.-> LOG
    PB -.-> ANIM
    CD -.-> LOG
    OTAM -.-> PB
    OTAM -.-> CD
    SLOC -.-> OP
    SLOC -.-> KB
    SLOC -.-> AT
    SLOC -.-> OHM
    
    %% ApiService 消费者
    FORM -.-> APISVC
    UIM -.-> APISVC
    EVT --> APISVC
    
    %% 字段标准化层拦截的关键路径
    GFSL -.-> GSA
    GFSL -.-> GC
    GFSL -.-> APISVC
    GFSL -.-> MOH
    GFSL -.-> FORM
    
    %% 语言检测与应用状态
    LD -.-> APP
    
    %% 历史管理
    EVT -.-> OHM
    
    %% 🚀 优化后的本地处理路径
    LD --> CD
    CD --> FORM
    
    %% ❌ 修复前的问题路径（已禁用）
    LD -.->|❌ 已禁用| BFC
    
    %% 样式定义
    classDef startupLayer fill:#e8eaf6,stroke:#3949ab,stroke-width:2px
    classDef userLayer fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef realtimeLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef businessLayer fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef coreLayer fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef externalLayer fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef supportLayer fill:#f5f5f5,stroke:#616161,stroke-width:2px
    
    class MANI,LOADER,BOOT,DCN,SLOC startupLayer
    class UI,FORM userLayer
        class UIM,PRICE,STATE,EVT userLayer
    class RTM,LD realtimeLayer
    class BFC,GSA,OMC businessLayer
    class CD,PB,GC,RP coreLayer
    class API externalLayer
    class LOG,CACHE,ANIM,CCV,OTAM,KB,FOTAS,JOTAS,APISVC,GFSL,OHM,APP supportLayer
    class OP,AT coreLayer
    class MOH,APIC,UIC,HM businessLayer
    class HDATA supportLayer
    class HNDB supportLayer

    %% 可选/降级组件样式（使用虚线连线表示）

    %% 数据源（可选展示）
    %% KB 的数据来源
    HDATA[酒店数据<br/>data/hotel-data.json]
    KB -.-> HDATA
    
    %% 兼容/遗留路径与数据源
    ATS[地址翻译服务(遗留)<br/>AddressTranslationService]
    AT -.-> ATS
    HNDB[酒店名称数据库<br/>hotel-name-database.js]
    ATS -.-> HNDB
    
    %% 统一数据管理器消费者
    FORM -.-> UDM
    UIM -.-> UDM
    
    %% 车辆配置集成校验依赖
    VCI -.-> APISVC
    VCI -.-> UIM
