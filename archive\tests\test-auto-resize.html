<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应高度测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            box-sizing: border-box;
        }
        
        /* 自适应高度样式 */
        .auto-resize {
            resize: none;
            min-height: 35px;
            max-height: 200px;
            overflow-y: hidden;
            transition: height 0.2s ease;
            line-height: 1.4;
        }
        
        textarea.auto-resize {
            min-height: 60px;
            max-height: 300px;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background-color: #0056b3;
        }
        
        .flight-error-demo {
            border-left: 4px solid #f44336;
            padding-left: 12px;
            margin: 10px 0;
        }
        
        .flight-error-demo .error-title {
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 8px;
        }
        
        .flight-error-demo .error-detail {
            color: #666;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>🧪 自适应高度和国际化错误提示测试</h1>
    
    <div class="test-container">
        <h2>📏 任务1: 自适应高度输入框测试</h2>
        
        <div class="form-group">
            <label for="test-input">普通输入框 (自适应高度):</label>
            <input type="text" id="test-input" class="auto-resize" placeholder="输入长文本测试自适应高度...">
            <div id="input-status" class="status"></div>
        </div>
        
        <div class="form-group">
            <label for="test-textarea">文本域 (自适应高度):</label>
            <textarea id="test-textarea" class="auto-resize" placeholder="输入多行文本测试自适应高度..."></textarea>
            <div id="textarea-status" class="status"></div>
        </div>
        
        <div class="form-group">
            <label for="pickup-test">上车地点 (模拟真实场景):</label>
            <input type="text" id="pickup-test" class="auto-resize" placeholder="KLIA Terminal 1, Departure Hall...">
        </div>
        
        <div class="form-group">
            <label for="dropoff-test">目的地 (模拟真实场景):</label>
            <input type="text" id="dropoff-test" class="auto-resize" placeholder="Hotel Grand Hyatt Kuala Lumpur...">
        </div>
        
        <button class="test-button" onclick="fillLongText()">填充长文本测试</button>
        <button class="test-button" onclick="clearAllInputs()">清空所有输入</button>
        <button class="test-button" onclick="testAutoResize()">手动测试自适应</button>
    </div>
    
    <div class="test-container">
        <h2>🌐 任务2: 航班信息错误提示国际化测试</h2>
        
        <div class="form-group">
            <label>语言切换:</label>
            <button class="test-button" onclick="setLanguage('zh')">中文</button>
            <button class="test-button" onclick="setLanguage('en')">English</button>
            <span id="current-language">当前语言: 中文</span>
        </div>
        
        <div class="form-group">
            <label>错误类型测试:</label>
            <button class="test-button" onclick="showFlightError('invalidFormat')">格式无效</button>
            <button class="test-button" onclick="showFlightError('notFound')">航班未找到</button>
            <button class="test-button" onclick="showFlightError('networkError')">网络错误</button>
            <button class="test-button" onclick="showFlightError('timeout')">查询超时</button>
        </div>
        
        <div id="flight-error-display"></div>
    </div>
    
    <div class="test-container">
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/auto-resize-manager.js"></script>
    
    <script>
        // 测试脚本
        let currentLang = 'zh';
        
        // 模拟国际化函数
        const translations = {
            zh: {
                'flight.error.invalidFormat': '航班号格式无效',
                'flight.error.notFound': '航班信息未找到',
                'flight.error.networkError': '网络连接失败',
                'flight.error.timeout': '查询超时',
                'common.flightNumber': '查询航班号: {flightNumber}'
            },
            en: {
                'flight.error.invalidFormat': 'Invalid flight number format',
                'flight.error.notFound': 'Flight information not found',
                'flight.error.networkError': 'Network connection failed',
                'flight.error.timeout': 'Query timeout',
                'common.flightNumber': 'Flight Number: {flightNumber}'
            }
        };
        
        function t(key, params = {}) {
            let message = translations[currentLang][key] || key;
            Object.keys(params).forEach(paramKey => {
                message = message.replace(`{${paramKey}}`, params[paramKey]);
            });
            return message;
        }
        
        function setLanguage(lang) {
            currentLang = lang;
            document.getElementById('current-language').textContent = 
                lang === 'zh' ? '当前语言: 中文' : 'Current Language: English';
            logResult(`语言已切换为: ${lang}`);
        }
        
        function showFlightError(errorType) {
            const errorMessage = t(`flight.error.${errorType}`);
            const flightNumber = 'MH370';
            const flightNumberText = t('common.flightNumber', { flightNumber });
            
            const errorHtml = `
                <div class="flight-error-demo">
                    <div class="error-title">❌ ${errorMessage}</div>
                    <div class="error-detail">${flightNumberText}</div>
                </div>
            `;
            
            document.getElementById('flight-error-display').innerHTML = errorHtml;
            logResult(`显示${currentLang === 'zh' ? '中文' : '英文'}错误提示: ${errorType}`);
        }
        
        function fillLongText() {
            const longText = `KLIA Terminal 1, Departure Hall Level 3, Gate A1, Near Starbucks Coffee Shop, Meeting Point Area, Please look for driver with name board showing passenger name. Additional instructions: Please wait at the designated pickup point and call the driver if you cannot locate them.`;
            
            document.getElementById('test-input').value = longText;
            document.getElementById('pickup-test').value = longText;
            
            const multilineText = `This is line 1 of the test text.
This is line 2 with more content to test auto-resize.
Line 3: Testing the automatic height adjustment feature.
Line 4: The textarea should expand to show all content.
Line 5: Maximum height should be respected.
Line 6: Scrollbar should appear if content exceeds max height.`;
            
            document.getElementById('test-textarea').value = multilineText;
            
            // 触发输入事件
            ['test-input', 'pickup-test', 'test-textarea'].forEach(id => {
                const element = document.getElementById(id);
                element.dispatchEvent(new Event('input', { bubbles: true }));
            });
            
            logResult('已填充长文本，触发自适应高度调整');
        }
        
        function clearAllInputs() {
            ['test-input', 'test-textarea', 'pickup-test', 'dropoff-test'].forEach(id => {
                const element = document.getElementById(id);
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
            });
            logResult('已清空所有输入框');
        }
        
        function testAutoResize() {
            const results = [];
            
            ['test-input', 'test-textarea', 'pickup-test', 'dropoff-test'].forEach(id => {
                const element = document.getElementById(id);
                const initialHeight = element.offsetHeight;
                
                // 添加测试内容
                element.value = 'Test content that should trigger auto-resize functionality';
                element.dispatchEvent(new Event('input', { bubbles: true }));
                
                const newHeight = element.offsetHeight;
                results.push(`${id}: ${initialHeight}px → ${newHeight}px`);
            });
            
            logResult('自适应高度测试结果:\n' + results.join('\n'));
        }
        
        function logResult(message) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logResult('测试页面已加载');
            
            // 检查自适应高度管理器是否可用
            if (typeof window.OTA?.autoResizeManager !== 'undefined') {
                logResult('✅ 自适应高度管理器已加载');
            } else {
                logResult('❌ 自适应高度管理器未找到，使用简化版本');
                initSimpleAutoResize();
            }
            
            // 检查国际化功能
            if (typeof window.t === 'function') {
                logResult('✅ 国际化功能已加载');
            } else {
                logResult('ℹ️ 使用内置国际化功能');
            }
        });
        
        // 简化版自适应高度实现
        function initSimpleAutoResize() {
            const autoResizeElements = document.querySelectorAll('.auto-resize');
            
            autoResizeElements.forEach(element => {
                const autoResize = () => {
                    element.style.height = 'auto';
                    const newHeight = Math.min(
                        Math.max(element.scrollHeight, element.tagName === 'TEXTAREA' ? 60 : 35),
                        element.tagName === 'TEXTAREA' ? 300 : 200
                    );
                    element.style.height = newHeight + 'px';
                };
                
                element.addEventListener('input', autoResize);
                element.addEventListener('paste', () => setTimeout(autoResize, 10));
                autoResize(); // 初始调整
            });
        }
    </script>
</body>
</html>
