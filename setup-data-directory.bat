@echo off
REM ============================================================================
REM CORS问题解决方案 - 数据目录设置脚本
REM 创建data目录并复制酒店数据文件
REM ============================================================================

echo.
echo 🔧 CORS问题解决方案 - 数据目录设置
echo ============================================================================
echo.

REM 检查是否在正确的目录
if not exist "index.html" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo    项目根目录应包含 index.html 文件
    echo.
    pause
    exit /b 1
)

echo ✅ 检测到项目根目录
echo.

REM 创建data目录
echo 📁 创建data目录...
if not exist "data" (
    mkdir data
    echo ✅ data目录创建成功
) else (
    echo ℹ️  data目录已存在
)
echo.

REM 检查源文件
echo 📋 检查源数据文件...
if exist "hotels_by_region.json" (
    echo ✅ 找到源文件: hotels_by_region.json
    
    REM 复制并重命名文件
    echo 📄 复制酒店数据文件...
    copy "hotels_by_region.json" "data\hotel-data.json" >nul 2>&1
    
    if exist "data\hotel-data.json" (
        echo ✅ 酒店数据文件复制成功: data\hotel-data.json
        
        REM 显示文件大小
        for %%A in ("data\hotel-data.json") do (
            echo ℹ️  文件大小: %%~zA 字节
        )
    ) else (
        echo ❌ 文件复制失败
        goto :error
    )
) else (
    echo ❌ 未找到源文件: hotels_by_region.json
    echo    请确保此文件存在于项目根目录
    goto :error
)
echo.

REM 验证文件内容
echo 🔍 验证文件内容...
findstr /C:"metadata" "data\hotel-data.json" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 文件内容验证成功
) else (
    echo ⚠️  文件内容可能不完整，但已复制
)
echo.

REM 创建其他可能需要的数据文件
echo 📄 创建其他数据文件...

REM 创建简单的机场数据文件
if not exist "data\airport-data.json" (
    echo [{"code":"KLIA","name":"Kuala Lumpur International Airport","chinese":"吉隆坡国际机场"},{"code":"PEN","name":"Penang International Airport","chinese":"槟城国际机场"}] > "data\airport-data.json"
    echo ✅ 创建机场数据文件: data\airport-data.json
)

REM 创建区域映射文件
if not exist "data\region-mapping.json" (
    echo {"regions":["吉隆坡","槟城","新山","亚庇","兰卡威","马六甲","怡保","关丹"]} > "data\region-mapping.json"
    echo ✅ 创建区域映射文件: data\region-mapping.json
)
echo.

REM 显示最终结果
echo 📊 设置完成统计:
echo ============================================================================
echo 📁 data目录: %CD%\data
echo.
echo 📄 已创建的文件:
if exist "data\hotel-data.json" echo    ✅ hotel-data.json
if exist "data\airport-data.json" echo    ✅ airport-data.json  
if exist "data\region-mapping.json" echo    ✅ region-mapping.json
echo.

echo 🎯 CORS解决方案状态:
echo    ✅ 方案1: 数据内联化 (已在代码中实现)
echo    ✅ 方案2: 数据目录结构 (刚刚设置完成)
echo    ✅ 方案3: 智能降级机制 (已在代码中实现)
echo.

echo 📝 使用说明:
echo    1. 数据内联化是主要解决方案，无需额外配置
echo    2. data目录作为备用方案，在生产环境部署时有用
echo    3. 系统会自动选择最佳数据源
echo.

echo 🚀 下一步:
echo    1. 打开 index.html 测试系统
echo    2. 运行 test-cors-fix.html 验证修复效果
echo    3. 检查浏览器控制台确认无CORS错误
echo.

echo ✅ 数据目录设置完成！
echo ============================================================================
echo.
pause
exit /b 0

:error
echo.
echo ❌ 设置过程中出现错误
echo 请检查:
echo    1. 是否在正确的项目目录
echo    2. 是否有足够的文件权限
echo    3. hotels_by_region.json 文件是否存在
echo.
pause
exit /b 1
