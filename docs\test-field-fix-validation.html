<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-input {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .results {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .before, .after {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #6c757d;
        }
        
        .after {
            border-left-color: #28a745;
        }
        
        .field-check {
            margin: 5px 0;
            padding: 5px;
        }
        
        .field-check.missing {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 3px solid #dc3545;
            padding-left: 10px;
        }
        
        .field-check.present {
            background-color: #d4edda;
            color: #155724;
            border-left: 3px solid #28a745;
            padding-left: 10px;
        }
        
        .field-check.corrected {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left: 3px solid #17a2b8;
            padding-left: 10px;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #b6d4db;
            color: #0c5460;
        }
        
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 字段修复验证测试</h1>
    
    <div class="container">
        <h2>测试数据</h2>
        <textarea id="testInput" class="test-input" placeholder="输入订单文本...">订单编号：2870937337559853573买家：贝贝我爱你7055支付时间：2025-08-09 22:18:21
查看详情

经济7座

【送机】

马来西亚-斗湖省

[出发]归仙阿那亚海景酒店

[抵达]斗湖机场MAIN

约82.3公里

2025-08-11 07:50:00

联系人

真实号：15824577055

---
3成人0儿童

司机姓名：yen

司机电话：176...</textarea>
        
        <button class="btn" onclick="runFieldTest()">🚀 运行字段修复测试</button>
        <button class="btn" onclick="clearResults()">🧹 清空结果</button>
    </div>

    <div class="container" id="statusContainer">
        <h2>系统状态检查</h2>
        <div id="systemStatus"></div>
    </div>

    <div class="container" id="resultsContainer" style="display: none;">
        <h2>测试结果对比</h2>
        <div id="testResults"></div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="js/core/script-manifest.js"></script>
    
    <script>
        // 等待系统加载完成
        let systemReady = false;
        
        window.addEventListener('load', async () => {
            await checkSystemStatus();
        });
        
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            try {
                // 检查脚本加载器
                if (typeof window.ScriptLoader === 'undefined') {
                    statusDiv.innerHTML = '<div class="status error">❌ 脚本加载器未就绪</div>';
                    return;
                }
                
                statusDiv.innerHTML = '<div class="status info">⏳ 正在检查系统状态...</div>';
                
                // 等待系统启动（延长时间确保所有修复生效）
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                let statusHtml = '';
                
                // 检查关键组件
                const checks = [
                    { name: '字段标准化层', check: () => window.OTA?.globalFieldStandardizationLayer },
                    { name: '特性开关', check: () => window.OTA?.featureToggle },
                    { name: 'Gemini服务', check: () => window.OTA?.geminiService },
                    { name: 'Prompt构建器', check: () => window.OTA?.promptBuilder },
                    { name: 'Fliggy策略', check: () => window.FliggyOTAStrategy },
                    { name: '业务流控制器', check: () => window.OTA?.businessFlowController }
                ];
                
                for (const { name, check } of checks) {
                    const isReady = check();
                    statusHtml += `<div class="field-check ${isReady ? 'present' : 'missing'}">
                        ${isReady ? '✅' : '❌'} ${name}: ${isReady ? '已就绪' : '未就绪'}
                    </div>`;
                }
                
                // 检查字段标准化是否启用
                const isEnabled = window.OTA?.featureToggle?.isEnabled('enableGlobalFieldStandardization');
                const fieldLayer = window.OTA?.globalFieldStandardizationLayer || window.getGlobalFieldStandardizationLayer?.();
                const isInitialized = fieldLayer?.initialized || false;
                
                statusHtml += `<div class="field-check ${isEnabled ? 'corrected' : 'missing'}">
                    ${isEnabled ? '🔧' : '❌'} 字段标准化特性开关: ${isEnabled ? '已启用' : '未启用'}
                </div>`;
                
                statusHtml += `<div class="field-check ${isInitialized ? 'corrected' : 'missing'}">
                    ${isInitialized ? '⚡' : '❌'} 字段标准化拦截器: ${isInitialized ? '已初始化' : '未初始化'}
                </div>`;
                
                // 检查Fliggy策略字段片段
                try {
                    const fliggySnippets = window.FliggyOTAStrategy?.getFieldPromptSnippets?.();
                    const hasUpdatedSnippets = fliggySnippets && fliggySnippets.pickup_date && fliggySnippets.driving_region_id;
                    statusHtml += `<div class="field-check ${hasUpdatedSnippets ? 'corrected' : 'missing'}">
                        ${hasUpdatedSnippets ? '📝' : '❌'} Fliggy策略增强片段: ${hasUpdatedSnippets ? '已更新' : '未更新'}
                    </div>`;
                } catch (e) {
                    statusHtml += `<div class="field-check missing">❌ Fliggy策略检查失败</div>`;
                }
                
                systemReady = true;
                statusDiv.innerHTML = statusHtml;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 系统检查失败: ${error.message}</div>`;
            }
        }
        
        async function runFieldTest() {
            if (!systemReady) {
                alert('系统尚未就绪，请等待系统加载完成');
                return;
            }
            
            const testInput = document.getElementById('testInput').value.trim();
            if (!testInput) {
                alert('请输入测试数据');
                return;
            }
            
            const resultsContainer = document.getElementById('resultsContainer');
            const testResults = document.getElementById('testResults');
            
            resultsContainer.style.display = 'block';
            testResults.innerHTML = '<div class="status info">⏳ 正在运行测试...</div>';
            
            try {
                // 运行Gemini解析
                const geminiService = window.OTA?.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未就绪');
                }
                
                console.log('🔧 开始字段修复验证测试...');
                console.log('测试输入:', testInput);
                
                const parseResult = await geminiService.parseOrder(testInput);
                console.log('Gemini解析结果:', parseResult);
                
                // 分析结果
                const analysis = analyzeFieldResults(parseResult);
                
                // 显示结果
                displayResults(analysis, testInput);
                
            } catch (error) {
                testResults.innerHTML = `<div class="status error">❌ 测试失败: ${error.message}</div>`;
                console.error('测试失败:', error);
            }
        }
        
        function analyzeFieldResults(parseResult) {
            const requiredFields = [
                'pickup_location', 'dropoff_location', 'pickup_date', 'pickup_time',
                'customer_name', 'ota_reference_number', 'ota_price',
                'sub_category_id', 'car_type_id', 'driving_region_id', 'languages_id_array'
            ];
            
            const expectedMappings = {
                'passenger_count': 'passenger_number',
                'luggage_count': 'luggage_number'
            };
            
            let data = null;
            if (Array.isArray(parseResult) && parseResult.length > 0) {
                data = parseResult[0];
            } else if (parseResult && typeof parseResult === 'object') {
                data = parseResult;
            }
            
            if (!data) {
                return {
                    success: false,
                    error: '未能获取有效的解析结果',
                    data: parseResult
                };
            }
            
            const analysis = {
                success: true,
                data: data,
                fieldStatus: {},
                missingFields: [],
                presentFields: [],
                mappingStatus: {},
                improvements: []
            };
            
            // 检查必填字段
            for (const field of requiredFields) {
                if (data.hasOwnProperty(field) && data[field] !== null && data[field] !== undefined) {
                    analysis.presentFields.push(field);
                    analysis.fieldStatus[field] = 'present';
                } else {
                    analysis.missingFields.push(field);
                    analysis.fieldStatus[field] = 'missing';
                }
            }
            
            // 检查字段映射
            for (const [oldField, newField] of Object.entries(expectedMappings)) {
                if (data.hasOwnProperty(oldField)) {
                    analysis.mappingStatus[oldField] = 'needs_mapping';
                } else if (data.hasOwnProperty(newField)) {
                    analysis.mappingStatus[newField] = 'correctly_mapped';
                }
            }
            
            // 检查关键修复点
            if (data.pickup_date) {
                analysis.improvements.push('✅ pickup_date 字段已正确提取');
            }
            
            if (data.driving_region_id) {
                analysis.improvements.push('✅ driving_region_id 字段已正确识别');
            } else {
                analysis.improvements.push('❌ driving_region_id 仍然缺失');
            }
            
            if (data.car_type_id === 35) {
                analysis.improvements.push('✅ car_type_id 映射正确 (经济7座 → 35)');
            }
            
            return analysis;
        }
        
        function displayResults(analysis, originalInput) {
            const testResults = document.getElementById('testResults');
            
            if (!analysis.success) {
                testResults.innerHTML = `<div class="status error">❌ 分析失败: ${analysis.error}</div>
                    <pre>${JSON.stringify(analysis.data, null, 2)}</pre>`;
                return;
            }
            
            let html = '<div class="comparison">';
            
            // 左侧：问题分析
            html += '<div class="before">';
            html += '<h3>📋 字段状态分析</h3>';
            
            // 缺失字段
            if (analysis.missingFields.length > 0) {
                html += '<h4>❌ 缺失字段:</h4>';
                analysis.missingFields.forEach(field => {
                    html += `<div class="field-check missing">${field}</div>`;
                });
            }
            
            // 已有字段
            if (analysis.presentFields.length > 0) {
                html += '<h4>✅ 已有字段:</h4>';
                analysis.presentFields.forEach(field => {
                    html += `<div class="field-check present">${field}: ${JSON.stringify(analysis.data[field])}</div>`;
                });
            }
            
            // 字段映射状态
            html += '<h4>🔧 字段映射状态:</h4>';
            Object.entries(analysis.mappingStatus).forEach(([field, status]) => {
                const statusClass = status === 'correctly_mapped' ? 'corrected' : 'missing';
                const statusText = status === 'correctly_mapped' ? '已正确映射' : '需要映射';
                html += `<div class="field-check ${statusClass}">${field}: ${statusText}</div>`;
            });
            
            html += '</div>';
            
            // 右侧：改进效果
            html += '<div class="after">';
            html += '<h3>🚀 修复效果</h3>';
            
            analysis.improvements.forEach(improvement => {
                const isSuccess = improvement.startsWith('✅');
                html += `<div class="field-check ${isSuccess ? 'corrected' : 'missing'}">${improvement}</div>`;
            });
            
            html += '<h4>完整解析结果:</h4>';
            html += `<pre>${JSON.stringify(analysis.data, null, 2)}</pre>`;
            
            html += '</div>';
            html += '</div>';
            
            // 总结
            const successRate = (analysis.presentFields.length / (analysis.presentFields.length + analysis.missingFields.length) * 100).toFixed(1);
            html += `<div class="status ${successRate > 80 ? 'success' : 'info'}">
                📊 字段完整率: ${successRate}% (${analysis.presentFields.length}/${analysis.presentFields.length + analysis.missingFields.length})
            </div>`;
            
            testResults.innerHTML = html;
        }
        
        function clearResults() {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.style.display = 'none';
        }
    </script>
</body>
</html>