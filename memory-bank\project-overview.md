# 项目概述与状态

> **最后更新**: 2025-08-09
> 本文档整合了项目的当前状态、开发进度、技术架构和核心数据流，提供一站式概览。

---

## 🎯 当前状态 (As of 2025-01-24)

- **阶段**: OTA平台特殊规则处理模块开发计划制定完成。
- **健康度**: 优秀 (语法错误0个，功能100%可用)。
- **下一步**: 准备进入OTA平台特殊规则处理模块的第一阶段开发。

### 最近完成的工作

1. **核心问题修复**: 清理按键、OTA参考号、多订单检测、Gemini提示词。
2. **Chrome工具验证**: 100%测试通过率。
3. **文档整合**: 完成修复报告和进度更新。
4. **系统性能验证**: 响应时间433.52ms，内存使用9.54MB。
5. **文件与服务优化**: 清理过时文件，优化服务注册。
6. **航班号识别优化**: 准确率提升至95%+, 支持8种格式和50+航空公司。

---

## 🚀 开发计划与进度

### 当前工作焦点

- **OTA平台特殊规则处理模块开发**: 准备进入第一阶段开发（架构设计）。
- **系统维护**: 系统已完全修复，处于稳定运行状态。
- **性能监控**: 持续监控系统健康度和性能表现。

### 下一步行动计划

1. **第一阶段开发** (3-4天): 架构设计和核心结构。
2. **第二阶段开发** (5-6天): 核心功能实现。
3. **第三阶段开发** (4-5天): 系统集成和优化。
4. **第四阶段开发** (2-3天): 测试和文档。

---

## 🏗️ 技术架构

**系统类型**: 纯前端OTA订单处理系统，通过Google Gemini AI解析订单，调用GoMyHire API创建。
**部署方式**: Netlify静态托管，支持离线(file://)和在线(HTTP)双模式。
**核心架构**: Manager模式 + 事件驱动 + 状态管理。

### 关键设计

- **传统脚本加载**: 使用`<script>`标签而非ES6模块，保证兼容性。
- **延迟依赖获取**: Manager间通过延迟获取避免循环依赖。
- **集中式状态**: `AppState`作为全局状态中心。
- **防御性编程**: 大量的null检查和错误处理。

### 数据流全景

1. **AI解析入口**: `ui-manager.js` -> `gemini-service.js`
2. **AI结果回调**: `ui-manager.js` -> `app-state.js` (状态变更)
3. **表单填充**: `ui-manager.js` (监听状态变更)
4. **预览展示**: `ui-manager.js` (从表单收集数据)

### 文件结构核心

- **主入口**: `index.html`, `main.js`
- **Manager层 (`js/managers/`)**: `UIManager`, `FormManager`, `PriceManager`, `EventManager`, `StateManager`, `RealtimeAnalysisManager`
- **服务层 (`js/`)**: `api-service.js`, `gemini-service.js`, `logger.js`
- **功能模块 (`js/`)**: `multi-order-manager.js`, `order-history-manager.js`

---

## 🔗 关键依赖与命名规范

- **AI输出字段**: `snake_case` (e.g., `car_type_id`)
- **表单元素ID**: `camelCase` (e.g., `carTypeId`)
- **映射方式**: 动态转换 `snake_case` to `camelCase`。
- **同步机制**: 通过监听状态变更，确保下拉框渲染完成后再填充表单，避免竞态问题。
