// 字段映射测试脚本
console.log('🧪 开始字段映射测试');

// 模拟MultiOrderManager类的关键方法
class TestMultiOrderManager {
    constructor() {
        this.state = {
            parsedOrders: [],
            isMultiOrderMode: false
        };
    }

    getFieldValue(order, fieldName) {
        // 直接从Gemini返回的字段获取值，与单订单模式保持一致
        const value = order[fieldName] || '';
        return this.processFieldValue(value, fieldName);
    }

    processFieldValue(value, fieldName) {
        // 如果值为null、undefined、空字符串或false，返回空字符串（不显示）
        if (value === null || value === undefined || value === '' || value === false) {
            return '';
        }
        
        // 如果是布尔值true，返回简单的"是"
        if (value === true) {
            return '是';
        }
        
        // 处理ID字段的映射 - 与单订单模式保持一致的静态映射
        
        // 车型ID映射 - 根据API实际数据
        if (fieldName === 'vehicleType' || fieldName === 'car_type_id') {
            const carTypeMapping = {
                '5': '5 Seater',
                '15': '7 Seater MPV',
                '16': '10 Seater Van',
                '20': '4WD',
                '23': '13 Seater Van',
                '24': '45 Seater Bus',
                '25': '25 Seater Bus',
                '26': 'VIP Van',
                '30': 'Premium 5 Seater',
                '31': 'Premium 7 Seater',
                '32': 'Luxury 5 Seater',
                '33': 'Luxury 7 Seater',
                '34': 'Economy 5 Seater',
                '35': 'Standard 5 Seater',
                '36': 'Standard 7 Seater',
                '37': 'Executive 5 Seater',
                '38': 'Executive 7 Seater',
                '39': 'VIP 5 Seater'
            };
            return carTypeMapping[value] || value;
        }
        
        // 区域ID映射 - 根据API实际数据
        if (fieldName === 'drivingRegion' || fieldName === 'driving_region_id') {
            const regionMapping = {
                '1': 'Kl/selangor',
                '2': 'Penang',
                '3': 'JB',
                '4': 'Malacca',
                '5': 'Genting',
                '6': 'Cameron',
                '8': 'Langkawi',
                '9': 'Sabah',
                '10': 'Sarawak',
                '12': 'East Malaysia',
                '13': 'SMW'
            };
            return regionMapping[value] || value;
        }
        
        // 服务类型ID映射 - 根据API实际数据
        if (fieldName === 'subCategoryId' || fieldName === 'sub_category_id') {
            const serviceMapping = {
                '2': 'Pickup',
                '3': 'Dropoff', 
                '4': 'Charter',
                '5': 'Paging'
            };
            return serviceMapping[value] || value;
        }
        
        // 语言映射 - 根据API实际数据
        if (fieldName === 'preferredLanguage' || fieldName === 'language') {
            const languageMapping = {
                '2': 'English',
                '3': 'Malay',
                '4': 'Chinese', 
                '5': 'Paging',
                '6': 'Charter',
                '8': '携程司导',
                'en': 'English',
                'english': 'English',
                'cn': 'Chinese',
                'chinese': 'Chinese',
                'zh': 'Chinese',
                'ms': 'Malay',
                'malay': 'Malay',
                'paging': 'Paging',
                'charter': 'Charter'
            };
            
            const lowerLang = value.toLowerCase();
            return languageMapping[lowerLang] || value;
        }
        
        // 其他字段直接返回转换为字符串的值
        return String(value);
    }

    testFieldMapping(order) {
        console.log('📋 测试订单数据:', order);
        
        // 定义要显示的字段及其属性
        const fieldConfig = {
            // 基础客户信息
            customerName: { label: '客户', editable: true, priority: 1 },
            customerContact: { label: '电话', editable: true, priority: 2 },
            customerEmail: { label: '邮箱', editable: true, priority: 3 },
            
            // 地点和时间
            pickup: { label: '上车', editable: true, priority: 4, isRoute: true },
            dropoff: { label: '下车', editable: true, priority: 5, isRoute: true },
            pickupDate: { label: '日期', editable: true, priority: 6 },
            pickupTime: { label: '时间', editable: true, priority: 7 },
            
            // 价格和渠道
            price: { label: '价格', editable: true, priority: 8 },
            ota: { label: '渠道', editable: false, priority: 9 },
            otaReferenceNumber: { label: '参考号', editable: true, priority: 10 },
            
            // 车型和区域
            vehicleType: { label: '车型', editable: true, priority: 11 },
            drivingRegion: { label: '区域', editable: true, priority: 12 },
            
            // 乘客信息
            passengerCount: { label: '乘客', editable: true, priority: 13 },
            luggageCount: { label: '行李', editable: true, priority: 14 },
            
            // 特殊要求
            meetAndGreet: { label: '接机', editable: false, priority: 15 },
            babyChair: { label: '婴儿椅', editable: false, priority: 16 },
            tourGuide: { label: '导游', editable: false, priority: 17 },
            wheelchairAccessible: { label: '轮椅', editable: false, priority: 18 },
            
            // 航班信息
            flightInfo: { label: '航班', editable: true, priority: 19 },
            
            // 语言和服务
            preferredLanguage: { label: '语言', editable: false, priority: 20 },
            subCategoryId: { label: '服务类型', editable: true, priority: 21 }
        };

        // 按优先级排序字段
        const sortedFields = Object.entries(fieldConfig).sort((a, b) => a[1].priority - b[1].priority);
        
        console.log('🔍 字段映射测试结果:');
        const results = [];
        
        for (const [fieldName, config] of sortedFields) {
            const value = this.getFieldValue(order, fieldName);
            
            const result = {
                字段名: fieldName,
                标签: config.label,
                原始值: order[fieldName],
                处理后值: value,
                是否显示: !!value,
                是否路线字段: !!config.isRoute
            };
            
            results.push(result);
            console.log(`📋 ${fieldName}:`, result);
        }
        
        // 统计
        const displayedFields = results.filter(r => r.是否显示);
        console.log(`📊 统计: 总字段 ${results.length}, 显示字段 ${displayedFields.length}, 隐藏字段 ${results.length - displayedFields.length}`);
        
        return results;
    }
}

// 创建测试实例
const testManager = new TestMultiOrderManager();

// 创建测试订单数据
const testOrder = {
    customerName: '张三',
    customerContact: '13800138000',
    customerEmail: '<EMAIL>',
    pickup: 'Kuala Lumpur International Airport (KLIA1)',
    dropoff: 'THE FACE Style Hotel',
    pickupDate: '2024-12-25',
    pickupTime: '14:30',
    price: '150.00',
    ota: 'Fliggy',
    otaReferenceNumber: 'FG123456789',
    vehicleType: '5 Seater',
    drivingRegion: 'Kl/selangor',
    passengerCount: '3',
    luggageCount: '2',
    meetAndGreet: true,
    babyChair: false,
    tourGuide: false,
    wheelchairAccessible: false,
    flightInfo: 'MH370',
    preferredLanguage: 'Chinese',
    subCategoryId: '2',
    extraRequirement: '需要婴儿椅',
    hotelName: 'The Face Hotel',
    airportTerminal: 'Terminal 1',
    gateNumber: 'A12',
    bookingReference: 'BOOK123456'
};

// 执行测试
console.log('🚀 开始执行字段映射测试');
const testResults = testManager.testFieldMapping(testOrder);

console.log('✅ 测试完成');
