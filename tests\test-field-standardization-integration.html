<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段标准化集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 字段标准化集成测试</h1>
        <p>测试全局字段标准化层是否正确集成到项目中</p>
        
        <button onclick="testSystemIntegration()">测试系统集成</button>
        <button onclick="testFieldMapping()">测试字段映射</button>
        <button onclick="testDisplayFunctions()">测试显示函数</button>
        
        <div id="results"></div>
    </div>

    <!-- 必需的脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/core/global-field-standardization-layer.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testSystemIntegration() {
            addResult('<h3>🔍 系统集成测试</h3>', 'info');
            
            // 检查全局字段标准化层是否加载
            if (window.OTA && window.OTA.globalFieldStandardizationLayer) {
                addResult('✅ 全局字段标准化层已加载', 'success');
                
                // 检查全局方法是否可用
                if (typeof window.standardizeFieldsToApi === 'function') {
                    addResult('✅ 全局标准化方法可用', 'success');
                } else {
                    addResult('❌ 全局标准化方法不可用', 'error');
                }
                
                // 获取统计信息
                const stats = window.OTA.globalFieldStandardizationLayer.getStatistics();
                addResult(`📊 系统统计: 映射规则${stats.totalMappings}个, API标准字段${stats.apiStandardFields}个`, 'info');
                
            } else {
                addResult('❌ 全局字段标准化层未加载', 'error');
            }
            
            // 检查多订单管理器是否存在
            if (window.OTA && window.OTA.multiOrderManager) {
                addResult('✅ 多订单管理器已加载', 'success');
            } else {
                addResult('⚠️ 多订单管理器未加载', 'error');
            }
        }

        function testFieldMapping() {
            addResult('<h3>🗺️ 字段映射测试</h3>', 'info');
            
            if (!window.standardizeFieldsToApi) {
                addResult('❌ 标准化函数不可用', 'error');
                return;
            }
            
            // 测试不同格式的数据
            const testCases = [
                {
                    name: '车型字段映射',
                    input: { carTypeId: 5, vehicleType: 'sedan' },
                    expectedFields: ['car_type_id']
                },
                {
                    name: '区域字段映射', 
                    input: { drivingRegionId: 1, region: 'KL' },
                    expectedFields: ['driving_region_id']
                },
                {
                    name: '客户信息映射',
                    input: { customerName: '张三', customerContact: '123456789' },
                    expectedFields: ['customer_name', 'customer_contact']
                },
                {
                    name: '时间字段映射',
                    input: { pickupDate: '2025-01-08', pickupTime: '10:00' },
                    expectedFields: ['date', 'time']
                }
            ];
            
            testCases.forEach(testCase => {
                try {
                    const result = window.standardizeFieldsToApi(testCase.input, 'integration-test');
                    const hasAllExpectedFields = testCase.expectedFields.every(field => 
                        result.hasOwnProperty(field)
                    );
                    
                    if (hasAllExpectedFields) {
                        addResult(`✅ ${testCase.name}: 通过`, 'success');
                        addResult(`<div class="json-display">输入: ${JSON.stringify(testCase.input)}\n输出: ${JSON.stringify(result)}</div>`, 'info');
                    } else {
                        addResult(`❌ ${testCase.name}: 失败 - 缺少预期字段`, 'error');
                        addResult(`<div class="json-display">预期字段: ${testCase.expectedFields.join(', ')}\n实际字段: ${Object.keys(result).join(', ')}</div>`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${testCase.name}: 异常 - ${error.message}`, 'error');
                }
            });
        }

        function testDisplayFunctions() {
            addResult('<h3>🖥️ 显示函数测试</h3>', 'info');
            
            // 等待多订单管理器加载
            setTimeout(() => {
                if (!window.OTA || !window.OTA.multiOrderManager) {
                    addResult('❌ 多订单管理器未加载，无法测试显示函数', 'error');
                    return;
                }
                
                const manager = window.OTA.multiOrderManager;
                
                // 测试车型显示
                const testOrder1 = { car_type_id: 5 };
                try {
                    const vehicleDisplay = manager.getVehicleTypeDisplay(testOrder1);
                    addResult(`✅ 车型显示测试: car_type_id=5 → "${vehicleDisplay}"`, 'success');
                } catch (error) {
                    addResult(`❌ 车型显示测试失败: ${error.message}`, 'error');
                }
                
                // 测试区域显示
                const testOrder2 = { driving_region_id: 1 };
                try {
                    const regionDisplay = manager.getDrivingRegionDisplay(testOrder2);
                    addResult(`✅ 区域显示测试: driving_region_id=1 → "${regionDisplay}"`, 'success');
                } catch (error) {
                    addResult(`❌ 区域显示测试失败: ${error.message}`, 'error');
                }
                
                // 测试混合字段格式（旧格式）
                const testOrder3 = { carTypeId: 15, drivingRegionId: 2 };
                try {
                    const vehicleDisplay = manager.getVehicleTypeDisplay(testOrder3);
                    const regionDisplay = manager.getDrivingRegionDisplay(testOrder3);
                    addResult(`✅ 兼容性测试: carTypeId=15 → "${vehicleDisplay}", drivingRegionId=2 → "${regionDisplay}"`, 'success');
                } catch (error) {
                    addResult(`❌ 兼容性测试失败: ${error.message}`, 'error');
                }
                
            }, 1000);
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h2>🚀 自动集成测试开始</h2>', 'info');
                testSystemIntegration();
            }, 500);
        });
    </script>
</body>
</html>