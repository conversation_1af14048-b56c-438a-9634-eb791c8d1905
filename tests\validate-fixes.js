const fs = require('fs');
const path = require('path');

// 验证修复是否正确应用
function validateFixes() {
    console.log('🔍 开始验证下拉菜单和字段映射修复...\n');
    
    const projectRoot = 'c:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH';
    const multiOrderManagerPath = path.join(projectRoot, 'js', 'multi-order-manager-v2.js');
    
    try {
        // 读取多订单管理器文件
        const content = fs.readFileSync(multiOrderManagerPath, 'utf8');
        
        // 检查下拉菜单事件处理修复
        const hasBlurEvent = content.includes('blur');
        const hasClickOutside = content.includes('click');
        const hasKeyboardEvent = content.includes('keydown');
        const hasEventRemoval = content.includes('removeEventListener');
        
        console.log('📋 下拉菜单事件处理检查:');
        console.log(`  ✅ 模糊事件处理: ${hasBlurEvent ? '已修复' : '❌ 缺失'}`);
        console.log(`  ✅ 外部点击处理: ${hasClickOutside ? '已修复' : '❌ 缺失'}`);
        console.log(`  ✅ 键盘事件处理: ${hasKeyboardEvent ? '已修复' : '❌ 缺失'}`);
        console.log(`  ✅ 事件清理: ${hasEventRemoval ? '已修复' : '❌ 缺失'}`);
        
        // 检查字段映射增强
        const hasEnhancedGetFieldValue = content.includes('getFieldValue') && 
                                       content.includes('ALTERNATIVE_FIELDS');
        const hasEnhancedUpdateMethod = content.includes('updateOrderFieldValue') &&
                                      content.includes('carTypeId') &&
                                      content.includes('drivingRegionId');
        
        console.log('\n📋 字段映射增强检查:');
        console.log(`  ✅ getFieldValue增强: ${hasEnhancedGetFieldValue ? '已修复' : '❌ 缺失'}`);
        console.log(`  ✅ updateOrderFieldValue增强: ${hasEnhancedUpdateMethod ? '已修复' : '❌ 缺失'}`);
        
        // 检查特定的修复内容
        const fixes = [
            {
                name: '下拉菜单blur事件',
                pattern: /select\.addEventListener\(['"]blur['"]/,
                found: false
            },
            {
                name: '全局点击事件',
                pattern: /document\.addEventListener\(['"]click['"]/,
                found: false
            },
            {
                name: 'ESC键处理',
                pattern: /key === ['"]Escape['"]/,
                found: false
            },
            {
                name: '字段映射备选项',
                pattern: /ALTERNATIVE_FIELDS\[fieldName\]/,
                found: false
            },
            {
                name: '车型ID同步更新',
                pattern: /car_type_id.*matchedCarType\.id/s,
                found: false
            }
        ];
        
        fixes.forEach(fix => {
            fix.found = fix.pattern.test(content);
        });
        
        console.log('\n📋 具体修复内容检查:');
        fixes.forEach(fix => {
            console.log(`  ${fix.found ? '✅' : '❌'} ${fix.name}: ${fix.found ? '已应用' : '未找到'}`);
        });
        
        // 生成总结
        const dropdownFixed = hasBlurEvent && hasClickOutside && hasKeyboardEvent;
        const fieldMappingFixed = hasEnhancedGetFieldValue && hasEnhancedUpdateMethod;
        
        console.log('\n🎯 修复状态总结:');
        console.log(`  下拉菜单收缩问题: ${dropdownFixed ? '✅ 已修复' : '❌ 需要检查'}`);
        console.log(`  字段映射增强问题: ${fieldMappingFixed ? '✅ 已修复' : '❌ 需要检查'}`);
        
        if (dropdownFixed && fieldMappingFixed) {
            console.log('\n🎉 所有修复已成功应用！');
            console.log('\n📝 下一步建议:');
            console.log('1. 在浏览器中打开 test-dropdown-field-mapping-fix.html');
            console.log('2. 点击"生成测试订单"按钮');
            console.log('3. 测试下拉菜单的收缩行为');
            console.log('4. 验证字段显示是否正确（显示名称而非ID）');
        } else {
            console.log('\n⚠️ 部分修复可能未完全应用，请检查代码。');
        }
        
    } catch (error) {
        console.error('❌ 验证过程中出现错误:', error.message);
    }
}

// 运行验证
validateFixes();
