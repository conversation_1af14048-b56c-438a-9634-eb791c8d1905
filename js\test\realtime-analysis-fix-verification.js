/**
 * ============================================================================
 * 🔧 实时分析管理器修复验证测试
 * ============================================================================
 *
 * @fileoverview 验证实时分析管理器的根本性修复效果
 * @description 测试字段标准化、动画系统集成和实时分析功能的协调工作
 *
 * @features 测试功能
 * - 实时分析管理器初始化验证
 * - 字段标准化支持验证
 * - 动画系统集成验证
 * - 端到端实时分析测试
 * - 错误信息消除验证
 *
 * @usage 使用方法
 * 在浏览器控制台中运行：testRealtimeAnalysisFix()
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    /**
     * 实时分析管理器修复验证测试套件
     */
    class RealtimeAnalysisFixVerification {
        constructor() {
            this.testResults = [];
            this.realtimeManager = null;
            
            // 测试订单文本
            this.testOrderText = `订单编号：4670922206817960132
买家：叶伶睿
支付时间：2025-08-07 09:22:31

豪华7座

【送机】

新加坡-新加坡

[出发]新加坡升达酒店-武吉士
[抵达]樟宜机场T3

约23.7公里

2025-08-13 09:00:00

叶伶睿
真实号：13857841893

---
3成人0儿童`;
        }

        /**
         * 运行完整的修复验证测试
         */
        async runFixVerification() {
            console.log('🔧 开始实时分析管理器修复验证测试...');
            console.log('='.repeat(60));

            try {
                // 1. 基础组件验证
                await this.testRealtimeManagerInitialization();
                await this.testFieldStandardizationSupport();
                await this.testAnimationSystemIntegration();
                
                // 2. 功能验证
                await this.testRealtimeAnalysisFunction();
                await this.testErrorMessageElimination();
                
                // 3. 端到端验证
                await this.testEndToEndRealtimeAnalysis();
                
                // 输出测试结果
                this.outputVerificationResults();
                
            } catch (error) {
                console.error('❌ 修复验证测试失败:', error);
                this.addTestResult('修复验证执行', false, `测试执行失败: ${error.message}`);
            }
        }

        /**
         * 测试实时分析管理器初始化
         */
        async testRealtimeManagerInitialization() {
            console.log('🔧 测试实时分析管理器初始化...');
            
            try {
                // 检查实时分析管理器是否存在
                const uiManager = window.OTA?.uiManager;
                this.realtimeManager = uiManager?.managers?.realtimeAnalysis;
                
                if (!this.realtimeManager) {
                    this.addTestResult('实时分析管理器初始化', false, '实时分析管理器实例不存在');
                    return;
                }
                
                // 检查关键方法
                const requiredMethods = [
                    'init', 'triggerRealtimeAnalysis', 'handleRealtimeInput',
                    'initializeAnimationManager', 'setupRealtimeAnalysis'
                ];
                
                const missingMethods = requiredMethods.filter(method => 
                    typeof this.realtimeManager[method] !== 'function'
                );
                
                if (missingMethods.length > 0) {
                    this.addTestResult('实时分析管理器初始化', false, 
                        `缺少方法: ${missingMethods.join(', ')}`);
                    return;
                }
                
                // 检查是否有analyzeText方法（应该没有）
                const hasAnalyzeText = typeof this.realtimeManager.analyzeText === 'function';
                if (hasAnalyzeText) {
                    this.addTestResult('实时分析管理器初始化', false, 
                        '意外发现analyzeText方法，这可能导致拦截器问题');
                    return;
                }
                
                this.addTestResult('实时分析管理器初始化', true, 
                    '实时分析管理器正确初始化，无analyzeText方法');
                
            } catch (error) {
                this.addTestResult('实时分析管理器初始化', false, error.message);
            }
        }

        /**
         * 测试字段标准化支持
         */
        async testFieldStandardizationSupport() {
            console.log('🔧 测试字段标准化支持...');
            
            try {
                // 检查Gemini服务是否被拦截（实时分析通过Gemini服务获得字段标准化）
                const geminiService = window.getGeminiService?.();
                if (!geminiService) {
                    this.addTestResult('字段标准化支持', false, 'Gemini服务不可用');
                    return;
                }
                
                const isGeminiIntercepted = geminiService.isIntercepted || 
                                          window.OTA?.geminiService?.isIntercepted;
                
                if (isGeminiIntercepted) {
                    this.addTestResult('字段标准化支持', true, 
                        'Gemini服务已被拦截，实时分析自动获得字段标准化支持');
                } else {
                    this.addTestResult('字段标准化支持', false, 
                        'Gemini服务未被拦截，字段标准化可能不生效');
                }
                
            } catch (error) {
                this.addTestResult('字段标准化支持', false, error.message);
            }
        }

        /**
         * 测试动画系统集成
         */
        async testAnimationSystemIntegration() {
            console.log('🎬 测试动画系统集成...');
            
            try {
                if (!this.realtimeManager) {
                    this.addTestResult('动画系统集成', false, '实时分析管理器不可用');
                    return;
                }
                
                // 检查动画管理器是否已集成
                const hasAnimationManager = !!this.realtimeManager.animationManager;
                const animationManagerType = typeof this.realtimeManager.animationManager;
                
                if (hasAnimationManager) {
                    this.addTestResult('动画系统集成', true, 
                        `动画管理器已集成 (类型: ${animationManagerType})`);
                } else {
                    // 检查是否是降级方案
                    const animationManager = window.OTA?.animationManager || window.animationManager;
                    if (!animationManager) {
                        this.addTestResult('动画系统集成', true, 
                            '动画管理器不可用，使用降级方案（正常）');
                    } else {
                        this.addTestResult('动画系统集成', false, 
                            '动画管理器可用但未集成到实时分析管理器');
                    }
                }
                
            } catch (error) {
                this.addTestResult('动画系统集成', false, error.message);
            }
        }

        /**
         * 测试实时分析功能
         */
        async testRealtimeAnalysisFunction() {
            console.log('🔄 测试实时分析功能...');
            
            try {
                if (!this.realtimeManager) {
                    this.addTestResult('实时分析功能', false, '实时分析管理器不可用');
                    return;
                }
                
                // 检查Gemini服务可用性
                const geminiService = window.getGeminiService?.();
                if (!geminiService || !geminiService.isAvailable()) {
                    this.addTestResult('实时分析功能', false, 'Gemini服务不可用');
                    return;
                }
                
                // 检查实时分析配置
                const realtimeStatus = this.realtimeManager.getRealtimeAnalysisStatus?.();
                if (realtimeStatus) {
                    const isEnabled = realtimeStatus.enabled;
                    this.addTestResult('实时分析功能', isEnabled, 
                        `实时分析${isEnabled ? '已启用' : '已禁用'}`);
                } else {
                    this.addTestResult('实时分析功能', true, 
                        '实时分析状态检查方法不可用，但管理器存在');
                }
                
            } catch (error) {
                this.addTestResult('实时分析功能', false, error.message);
            }
        }

        /**
         * 测试错误信息消除
         */
        async testErrorMessageElimination() {
            console.log('🔍 测试错误信息消除...');
            
            try {
                // 检查字段标准化层的拦截器状态
                const fieldStandardization = window.OTA?.GlobalFieldStandardizationLayer;
                if (!fieldStandardization) {
                    this.addTestResult('错误信息消除', false, '字段标准化层不可用');
                    return;
                }
                
                // 模拟控制台日志捕获
                const originalConsoleLog = console.log;
                const logMessages = [];
                
                console.log = function(...args) {
                    logMessages.push(args.join(' '));
                    originalConsoleLog.apply(console, args);
                };
                
                // 触发字段标准化层的拦截器方法
                try {
                    fieldStandardization.interceptRealtimeAnalysisManager();
                } catch (error) {
                    // 忽略可能的错误
                }
                
                // 恢复原始console.log
                console.log = originalConsoleLog;
                
                // 检查是否有错误信息
                const hasErrorMessage = logMessages.some(msg => 
                    msg.includes('跳过实时分析管理器重试（减法修复）') ||
                    msg.includes('实时分析管理器未找到')
                );
                
                if (hasErrorMessage) {
                    this.addTestResult('错误信息消除', false, 
                        '仍然存在实时分析管理器相关的错误信息');
                } else {
                    this.addTestResult('错误信息消除', true, 
                        '实时分析管理器错误信息已消除');
                }
                
            } catch (error) {
                this.addTestResult('错误信息消除', false, error.message);
            }
        }

        /**
         * 测试端到端实时分析
         */
        async testEndToEndRealtimeAnalysis() {
            console.log('🔄 测试端到端实时分析...');
            
            try {
                if (!this.realtimeManager) {
                    this.addTestResult('端到端实时分析', false, '实时分析管理器不可用');
                    return;
                }
                
                // 检查输入元素
                const orderInput = document.getElementById('orderInput');
                if (!orderInput) {
                    this.addTestResult('端到端实时分析', false, '订单输入框不存在');
                    return;
                }
                
                // 模拟输入测试订单文本
                const originalValue = orderInput.value;
                orderInput.value = this.testOrderText;
                
                // 创建输入事件
                const inputEvent = new Event('input', { bubbles: true });
                inputEvent.isTrusted = true; // 模拟真实用户输入
                
                // 触发实时分析
                let analysisTriggered = false;
                const originalTrigger = this.realtimeManager.triggerRealtimeAnalysis;
                
                this.realtimeManager.triggerRealtimeAnalysis = async function(text) {
                    analysisTriggered = true;
                    console.log('🔄 实时分析已触发，文本长度:', text?.length);
                    // 不实际执行分析，避免API调用
                    return Promise.resolve();
                };
                
                // 触发输入事件
                orderInput.dispatchEvent(inputEvent);
                
                // 等待防抖延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 恢复原始方法和值
                this.realtimeManager.triggerRealtimeAnalysis = originalTrigger;
                orderInput.value = originalValue;
                
                if (analysisTriggered) {
                    this.addTestResult('端到端实时分析', true, 
                        '端到端实时分析功能正常，能够正确触发');
                } else {
                    this.addTestResult('端到端实时分析', false, 
                        '端到端实时分析未能正确触发');
                }
                
            } catch (error) {
                this.addTestResult('端到端实时分析', false, error.message);
            }
        }

        /**
         * 添加测试结果
         */
        addTestResult(testName, passed, message) {
            this.testResults.push({
                name: testName,
                passed,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 输出验证结果
         */
        outputVerificationResults() {
            console.log('\n' + '='.repeat(60));
            console.log('🔧 实时分析管理器修复验证结果');
            console.log('='.repeat(60));
            
            const passedTests = this.testResults.filter(result => result.passed);
            const failedTests = this.testResults.filter(result => !result.passed);
            
            console.log(`✅ 通过: ${passedTests.length} 项`);
            console.log(`❌ 失败: ${failedTests.length} 项`);
            console.log(`📊 总计: ${this.testResults.length} 项`);
            
            if (failedTests.length > 0) {
                console.log('\n❌ 失败的测试:');
                failedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            if (passedTests.length > 0) {
                console.log('\n✅ 通过的测试:');
                passedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            const successRate = Math.round((passedTests.length / this.testResults.length) * 100);
            console.log(`\n🎯 修复成功率: ${successRate}%`);
            
            if (successRate >= 90) {
                console.log('🎉 实时分析管理器修复验证通过！');
                console.log('✨ 系统已恢复正常，错误信息已消除');
            } else if (successRate >= 70) {
                console.log('⚠️ 修复基本成功，但仍有部分问题需要解决');
            } else {
                console.log('❌ 修复验证失败，需要进一步诊断和修复');
            }
            
            // 提供使用建议
            console.log('\n💡 使用建议:');
            console.log('- 实时分析功能现在应该正常工作');
            console.log('- 字段标准化通过Gemini服务自动应用');
            console.log('- 动画效果会根据可用性自动启用或降级');
            console.log('- 不再出现"跳过实时分析管理器重试"错误信息');
        }
    }

    // 创建全局测试函数
    window.testRealtimeAnalysisFix = async function() {
        const tester = new RealtimeAnalysisFixVerification();
        await tester.runFixVerification();
        return tester.testResults;
    };

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.RealtimeAnalysisFixVerification = RealtimeAnalysisFixVerification;

    console.log('🔧 实时分析管理器修复验证脚本已加载');
    console.log('💡 使用 testRealtimeAnalysisFix() 运行修复验证测试');

})();
