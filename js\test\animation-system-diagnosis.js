/**
 * ============================================================================
 * 🎬 动画系统诊断工具
 * ============================================================================
 *
 * @fileoverview 诊断动画系统为什么没有按预期运行
 * @description 检查动画管理器状态、CSS样式、集成状态等
 * 
 * @features 诊断功能
 * - 动画管理器状态检查
 * - CSS样式加载检查
 * - 动画变量设置检查
 * - 管理器集成状态检查
 * - 用户偏好设置检查
 * - 实时动画测试
 * 
 * @usage 使用方法
 * 在浏览器控制台中运行：diagnoseAnimationSystem()
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    /**
     * 动画系统诊断工具
     */
    class AnimationSystemDiagnosis {
        constructor() {
            this.diagnosisResults = [];
            this.animationManager = null;
        }

        /**
         * 运行完整的动画系统诊断
         */
        async runDiagnosis() {
            console.log('🎬 开始动画系统诊断...');
            console.log('='.repeat(50));

            try {
                // 1. 基础状态检查
                await this.checkAnimationManagerStatus();
                await this.checkCSSStylesLoading();
                await this.checkAnimationVariables();
                
                // 2. 集成状态检查
                await this.checkManagerIntegrations();
                await this.checkUserPreferences();
                
                // 3. 功能测试
                await this.testAnimationFunctions();
                await this.testRealTimeAnimations();
                
                // 输出诊断结果
                this.outputDiagnosisResults();
                
            } catch (error) {
                console.error('❌ 动画系统诊断失败:', error);
                this.addDiagnosisResult('诊断执行', 'ERROR', `诊断执行失败: ${error.message}`);
            }
        }

        /**
         * 检查动画管理器状态
         */
        async checkAnimationManagerStatus() {
            console.log('🔍 检查动画管理器状态...');
            
            try {
                // 检查动画管理器是否存在
                this.animationManager = window.OTA?.animationManager || window.animationManager;
                
                if (!this.animationManager) {
                    this.addDiagnosisResult('动画管理器存在性', 'ERROR', '动画管理器实例不存在');
                    return;
                }
                
                this.addDiagnosisResult('动画管理器存在性', 'OK', '动画管理器实例存在');
                
                // 检查动画是否启用
                const isEnabled = this.animationManager.isAnimationEnabled?.();
                this.addDiagnosisResult('动画启用状态', isEnabled ? 'OK' : 'WARNING', 
                    `动画${isEnabled ? '已启用' : '已禁用'}`);
                
                // 检查配置
                const config = this.animationManager.config;
                if (config) {
                    this.addDiagnosisResult('动画配置', 'OK', 
                        `配置存在 - 持续时间: ${Object.keys(config.durations || {}).length}, 缓动: ${Object.keys(config.easings || {}).length}`);
                } else {
                    this.addDiagnosisResult('动画配置', 'ERROR', '动画配置不存在');
                }
                
                // 检查关键方法
                const methods = ['animateFieldFill', 'animateProgress', 'animateStatusFeedback', 'animateButtonClick'];
                const missingMethods = methods.filter(method => typeof this.animationManager[method] !== 'function');
                
                if (missingMethods.length === 0) {
                    this.addDiagnosisResult('动画方法', 'OK', '所有关键动画方法存在');
                } else {
                    this.addDiagnosisResult('动画方法', 'ERROR', `缺少方法: ${missingMethods.join(', ')}`);
                }
                
            } catch (error) {
                this.addDiagnosisResult('动画管理器状态', 'ERROR', error.message);
            }
        }

        /**
         * 检查CSS样式加载
         */
        async checkCSSStylesLoading() {
            console.log('🎨 检查CSS样式加载...');
            
            try {
                // 检查动画CSS文件是否加载
                const stylesheets = Array.from(document.styleSheets);
                const animationStylesheet = stylesheets.find(sheet => 
                    sheet.href && sheet.href.includes('animations.css')
                );
                
                if (animationStylesheet) {
                    this.addDiagnosisResult('动画CSS文件', 'OK', '动画CSS文件已加载');
                } else {
                    this.addDiagnosisResult('动画CSS文件', 'WARNING', '未找到专用的动画CSS文件');
                }
                
                // 检查关键CSS类是否存在
                const testElement = document.createElement('div');
                testElement.className = 'field-filling';
                document.body.appendChild(testElement);
                
                const computedStyle = getComputedStyle(testElement);
                const hasAnimation = computedStyle.animationName !== 'none' || 
                                   computedStyle.transition !== 'all 0s ease 0s';
                
                document.body.removeChild(testElement);
                
                if (hasAnimation) {
                    this.addDiagnosisResult('CSS动画类', 'OK', '动画CSS类正常工作');
                } else {
                    this.addDiagnosisResult('CSS动画类', 'WARNING', '动画CSS类可能未正确定义');
                }
                
            } catch (error) {
                this.addDiagnosisResult('CSS样式检查', 'ERROR', error.message);
            }
        }

        /**
         * 检查动画变量设置
         */
        async checkAnimationVariables() {
            console.log('⚙️ 检查动画变量设置...');
            
            try {
                const root = document.documentElement;
                const computedStyle = getComputedStyle(root);
                
                // 检查关键CSS变量
                const variables = [
                    '--animation-duration-fast',
                    '--animation-duration-normal', 
                    '--animation-duration-slow',
                    '--animation-easing-ease-out',
                    '--animations-enabled'
                ];
                
                const missingVariables = [];
                const setVariables = [];
                
                variables.forEach(variable => {
                    const value = computedStyle.getPropertyValue(variable);
                    if (value && value.trim()) {
                        setVariables.push(`${variable}: ${value.trim()}`);
                    } else {
                        missingVariables.push(variable);
                    }
                });
                
                if (setVariables.length > 0) {
                    this.addDiagnosisResult('CSS动画变量', 'OK', 
                        `${setVariables.length}个变量已设置: ${setVariables.slice(0, 2).join(', ')}...`);
                }
                
                if (missingVariables.length > 0) {
                    this.addDiagnosisResult('CSS动画变量', 'WARNING', 
                        `${missingVariables.length}个变量未设置: ${missingVariables.join(', ')}`);
                }
                
            } catch (error) {
                this.addDiagnosisResult('动画变量检查', 'ERROR', error.message);
            }
        }

        /**
         * 检查管理器集成状态
         */
        async checkManagerIntegrations() {
            console.log('🔗 检查管理器集成状态...');
            
            try {
                const integrations = [
                    {
                        name: '实时分析管理器',
                        manager: window.OTA?.uiManager?.managers?.realtimeAnalysis,
                        property: 'animationManager'
                    },
                    {
                        name: '表单管理器', 
                        manager: window.OTA?.uiManager?.managers?.form,
                        property: 'animationManager'
                    },
                    {
                        name: 'UI状态管理器',
                        manager: window.OTA?.uiManager?.managers?.state,
                        property: 'animationManager'
                    }
                ];
                
                integrations.forEach(integration => {
                    if (!integration.manager) {
                        this.addDiagnosisResult(`${integration.name}集成`, 'WARNING', 
                            `${integration.name}不存在`);
                        return;
                    }
                    
                    const hasAnimationManager = !!integration.manager[integration.property];
                    this.addDiagnosisResult(`${integration.name}集成`, 
                        hasAnimationManager ? 'OK' : 'WARNING',
                        `动画管理器${hasAnimationManager ? '已集成' : '未集成'}`);
                });
                
            } catch (error) {
                this.addDiagnosisResult('管理器集成检查', 'ERROR', error.message);
            }
        }

        /**
         * 检查用户偏好设置
         */
        async checkUserPreferences() {
            console.log('👤 检查用户偏好设置...');
            
            try {
                // 检查应用状态中的动画偏好
                const appState = window.getAppState?.();
                if (appState) {
                    const animationsEnabled = appState.getAnimationsEnabled?.();
                    this.addDiagnosisResult('用户动画偏好', 
                        animationsEnabled ? 'OK' : 'WARNING',
                        `用户动画偏好: ${animationsEnabled ? '启用' : '禁用'}`);
                } else {
                    this.addDiagnosisResult('用户动画偏好', 'WARNING', '应用状态不可用');
                }
                
                // 检查系统偏好
                const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
                this.addDiagnosisResult('系统动画偏好', 
                    prefersReducedMotion ? 'WARNING' : 'OK',
                    `系统偏好: ${prefersReducedMotion ? '减少动画' : '正常动画'}`);
                
            } catch (error) {
                this.addDiagnosisResult('用户偏好检查', 'ERROR', error.message);
            }
        }

        /**
         * 测试动画函数
         */
        async testAnimationFunctions() {
            console.log('🧪 测试动画函数...');
            
            try {
                if (!this.animationManager) {
                    this.addDiagnosisResult('动画函数测试', 'ERROR', '动画管理器不可用');
                    return;
                }
                
                // 创建测试元素
                const testInput = document.createElement('input');
                testInput.type = 'text';
                testInput.style.position = 'absolute';
                testInput.style.left = '-9999px';
                document.body.appendChild(testInput);
                
                // 测试字段填充动画
                try {
                    await this.animationManager.animateFieldFill(testInput, '测试值', { fieldName: 'test' });
                    this.addDiagnosisResult('字段填充动画', 'OK', '字段填充动画函数正常');
                } catch (error) {
                    this.addDiagnosisResult('字段填充动画', 'ERROR', `字段填充动画失败: ${error.message}`);
                }
                
                // 清理测试元素
                document.body.removeChild(testInput);
                
                // 测试状态反馈动画
                const testDiv = document.createElement('div');
                testDiv.style.position = 'absolute';
                testDiv.style.left = '-9999px';
                document.body.appendChild(testDiv);
                
                try {
                    await this.animationManager.animateStatusFeedback(testDiv, 'success');
                    this.addDiagnosisResult('状态反馈动画', 'OK', '状态反馈动画函数正常');
                } catch (error) {
                    this.addDiagnosisResult('状态反馈动画', 'ERROR', `状态反馈动画失败: ${error.message}`);
                }
                
                document.body.removeChild(testDiv);
                
            } catch (error) {
                this.addDiagnosisResult('动画函数测试', 'ERROR', error.message);
            }
        }

        /**
         * 测试实时动画
         */
        async testRealTimeAnimations() {
            console.log('⚡ 测试实时动画...');
            
            try {
                // 检查进度指示器元素
                const progressIndicator = document.querySelector('.progress-indicator') || 
                                        document.querySelector('#progressIndicator') ||
                                        document.querySelector('[class*="progress"]');
                
                if (progressIndicator) {
                    this.addDiagnosisResult('进度指示器元素', 'OK', '进度指示器元素存在');
                } else {
                    this.addDiagnosisResult('进度指示器元素', 'WARNING', '未找到进度指示器元素');
                }
                
                // 检查关键表单字段
                const keyFields = ['orderInput', 'customerName', 'pickup', 'dropoff'];
                const existingFields = keyFields.filter(id => document.getElementById(id));
                
                this.addDiagnosisResult('表单字段元素', 'OK', 
                    `${existingFields.length}/${keyFields.length} 个关键字段存在: ${existingFields.join(', ')}`);
                
            } catch (error) {
                this.addDiagnosisResult('实时动画测试', 'ERROR', error.message);
            }
        }

        /**
         * 添加诊断结果
         */
        addDiagnosisResult(category, status, message) {
            this.diagnosisResults.push({
                category,
                status,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 输出诊断结果
         */
        outputDiagnosisResults() {
            console.log('\n' + '='.repeat(50));
            console.log('🎬 动画系统诊断结果');
            console.log('='.repeat(50));
            
            const okResults = this.diagnosisResults.filter(r => r.status === 'OK');
            const warningResults = this.diagnosisResults.filter(r => r.status === 'WARNING');
            const errorResults = this.diagnosisResults.filter(r => r.status === 'ERROR');
            
            console.log(`✅ 正常: ${okResults.length} 项`);
            console.log(`⚠️ 警告: ${warningResults.length} 项`);
            console.log(`❌ 错误: ${errorResults.length} 项`);
            console.log(`📊 总计: ${this.diagnosisResults.length} 项`);
            
            if (errorResults.length > 0) {
                console.log('\n❌ 错误项目:');
                errorResults.forEach(result => {
                    console.log(`  - ${result.category}: ${result.message}`);
                });
            }
            
            if (warningResults.length > 0) {
                console.log('\n⚠️ 警告项目:');
                warningResults.forEach(result => {
                    console.log(`  - ${result.category}: ${result.message}`);
                });
            }
            
            if (okResults.length > 0) {
                console.log('\n✅ 正常项目:');
                okResults.forEach(result => {
                    console.log(`  - ${result.category}: ${result.message}`);
                });
            }
            
            // 提供修复建议
            this.provideFixSuggestions();
        }

        /**
         * 提供修复建议
         */
        provideFixSuggestions() {
            console.log('\n💡 修复建议:');
            
            const errorResults = this.diagnosisResults.filter(r => r.status === 'ERROR');
            const warningResults = this.diagnosisResults.filter(r => r.status === 'WARNING');
            
            if (errorResults.length === 0 && warningResults.length === 0) {
                console.log('🎉 动画系统状态良好，无需修复！');
                return;
            }
            
            if (errorResults.some(r => r.category.includes('动画管理器'))) {
                console.log('1. 检查动画管理器是否正确初始化');
                console.log('   - 确认 js/managers/animation-manager.js 已加载');
                console.log('   - 检查初始化过程中是否有错误');
            }
            
            if (warningResults.some(r => r.category.includes('CSS'))) {
                console.log('2. 检查CSS样式文件');
                console.log('   - 确认 css/components/animations.css 已正确加载');
                console.log('   - 检查CSS变量是否正确设置');
            }
            
            if (warningResults.some(r => r.category.includes('集成'))) {
                console.log('3. 检查管理器集成');
                console.log('   - 确认各管理器的 initializeAnimationManager() 方法被调用');
                console.log('   - 检查动画管理器实例是否正确传递');
            }
            
            if (warningResults.some(r => r.category.includes('偏好'))) {
                console.log('4. 检查用户偏好设置');
                console.log('   - 运行: window.getAppState().setAnimationsEnabled(true)');
                console.log('   - 检查系统是否设置了 prefers-reduced-motion');
            }
        }
    }

    // 创建全局诊断函数
    window.diagnoseAnimationSystem = async function() {
        const diagnosis = new AnimationSystemDiagnosis();
        await diagnosis.runDiagnosis();
        return diagnosis.diagnosisResults;
    };

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.AnimationSystemDiagnosis = AnimationSystemDiagnosis;

    console.log('🎬 动画系统诊断工具已加载');
    console.log('💡 使用 diagnoseAnimationSystem() 运行完整诊断');

})();
