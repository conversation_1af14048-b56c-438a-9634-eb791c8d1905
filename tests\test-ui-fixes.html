<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单界面修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>多订单界面修复验证</h1>
        
        <h3>测试项目：</h3>
        <ul>
            <li>✅ 关闭按键事件监听器</li>
            <li>✅ 返回主页按键事件监听器</li>
            <li>✅ 移除语言选择选项</li>
            <li>✅ 移除应用设置按键</li>
            <li>✅ OTA选择实时应用功能</li>
        </ul>

        <h3>测试步骤：</h3>
        <ol>
            <li>点击下方按钮模拟多订单检测</li>
            <li>观察多订单面板是否正确显示</li>
            <li>测试关闭和返回主页按键是否工作</li>
            <li>验证批量设置是否只显示OTA选择</li>
            <li>测试OTA选择变更是否实时应用</li>
        </ol>

        <button class="test-btn" onclick="testMultiOrderDisplay()">显示多订单面板</button>
        <button class="test-btn" onclick="testBatchOtaChange()">测试OTA实时应用</button>
        <button class="test-btn" onclick="checkButtonEvents()">检查按键事件</button>

        <div id="result" class="result" style="display: none;">
            <h4>测试结果：</h4>
            <div id="resultContent"></div>
        </div>
    </div>

    <!-- 最小化的必需依赖 -->
    <script>
        // 模拟基础环境
        window.OTA = window.OTA || {};
        
        // 模拟logger
        function getLogger() {
            return {
                log: (message, level, data) => {
                    console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
                }
            };
        }
    </script>

    <!-- 加载CSS -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/multi-order/mobile.css">

    <!-- 模拟多订单面板HTML结构 -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-content">
            <div class="multi-order-header">
                <div class="header-left">
                    <button type="button" id="backToMainBtn" class="btn btn-header-back" title="返回主页">←返回主页</button>
                    <h3>📦多订单管理</h3>
                </div>
                <div class="multi-order-controls">
                    <div class="header-actions">
                        <button type="button" id="closeMultiOrderBtn" class="btn btn-icon btn-close" title="关闭">✕</button>
                    </div>
                </div>
            </div>

            <!-- 简化版批量操作控件 -->
            <div class="batch-controls">
                <span class="batch-controls-label">批量设置:</span>
                <select id="batchOtaSelect" class="batch-dropdown-btn">
                    <option value="">选择OTA渠道</option>
                    <!-- OTA选项将通过JavaScript动态生成 -->
                </select>
                <span class="batch-info-text">选择后自动应用到订单</span>
            </div>

            <div class="multi-order-list" id="multiOrderList">
                <!-- 多订单项将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        function testMultiOrderDisplay() {
            console.log('🧪 测试多订单面板显示...');
            
            // 模拟多订单检测结果
            const mockMultiOrderResult = {
                isMultiOrder: true,
                confidence: 0.9,
                orderCount: 2,
                orders: [
                    {
                        customer_name: "张三",
                        customer_contact: "12345678901",
                        pickup: "吉隆坡国际机场",
                        dropoff: "吉隆坡希尔顿酒店",
                        pickup_date: "2025-08-07",
                        pickup_time: "10:00",
                        ota_price: 150,
                        currency: "MYR",
                        _otaChannel: "agoda",
                        vehicle_type: "豪华轿车",
                        driving_region: "市区"
                    },
                    {
                        customer_name: "李四", 
                        customer_contact: "09876543210",
                        pickup: "双子塔购物中心",
                        dropoff: "云顶高原度假村",
                        pickup_date: "2025-08-07",
                        pickup_time: "14:00",
                        ota_price: 200,
                        currency: "MYR",
                        _otaChannel: "booking",
                        vehicle_type: "商务车",
                        driving_region: "跨州"
                    }
                ]
            };

            // 创建并派发事件
            const event = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: mockMultiOrderResult,
                    orderText: "测试订单文本，包含两个订单..."
                }
            });

            document.dispatchEvent(event);
            
            updateResult('多订单面板已显示，请检查界面元素');
        }

        function testBatchOtaChange() {
            console.log('🧪 测试OTA实时应用...');
            
            const otaSelect = document.getElementById('batchOtaSelect');
            if (otaSelect) {
                // 模拟选择Agoda
                otaSelect.value = 'agoda';
                otaSelect.dispatchEvent(new Event('change'));
                updateResult('已模拟选择Agoda，请查看控制台日志确认实时应用');
            } else {
                updateResult('❌ 未找到OTA选择元素');
            }
        }

        function checkButtonEvents() {
            console.log('🧪 检查按键事件绑定...');
            
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            const backBtn = document.getElementById('backToMainBtn');
            
            let results = [];
            
            if (closeBtn) {
                // 检查是否有事件监听器
                const hasCloseListener = closeBtn.onclick !== null || 
                                       (closeBtn._listeners && closeBtn._listeners.click);
                results.push(`关闭按键事件: ${hasCloseListener ? '✅' : '❓'}`);
            } else {
                results.push('关闭按键元素: ❌ 未找到');
            }
            
            if (backBtn) {
                const hasBackListener = backBtn.onclick !== null || 
                                      (backBtn._listeners && backBtn._listeners.click);
                results.push(`返回主页按键事件: ${hasBackListener ? '✅' : '❓'}`);
            } else {
                results.push('返回主页按键元素: ❌ 未找到');
            }
            
            // 检查是否移除了语言选择
            const langSelect = document.getElementById('batchLanguageSelect');
            results.push(`语言选择已移除: ${!langSelect ? '✅' : '❌'}`);
            
            // 检查是否移除了应用按键
            const applyBtn = document.getElementById('applyBatchBtn');
            results.push(`应用设置按键已移除: ${!applyBtn ? '✅' : '❌'}`);
            
            updateResult(results.join('<br>'));
        }

        function updateResult(content) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = content;
            result.style.display = 'block';
        }

        // 页面加载完成后检查初始状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('📊 页面加载完成，检查MultiOrderManagerV2实例...');
                const manager = window.OTA?.multiOrderManagerV2;
                if (manager) {
                    console.log('✅ MultiOrderManagerV2实例存在');
                    console.log('📋 状态:', manager.state);
                } else {
                    console.log('❌ MultiOrderManagerV2实例不存在');
                }
            }, 1000);
        });
    </script>
</body>
</html>
