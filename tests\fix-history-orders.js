/**
 * 历史订单模块问题修复脚本
 * 解决显示和持久化问题
 */

(function() {
    'use strict';
    
    console.log('🔧 开始历史订单问题修复...');
    
    // 修复1: 确保历史订单管理器正确初始化
    function fixHistoryManager() {
        console.log('🔧 修复历史订单管理器...');
        
        try {
            // 确保全局函数存在
            if (typeof window.getOrderHistoryManager !== 'function') {
                console.error('❌ getOrderHistoryManager 函数不存在');
                return false;
            }
            
            // 获取管理器实例
            const manager = window.getOrderHistoryManager();
            if (!manager) {
                console.error('❌ 无法获取历史订单管理器实例');
                return false;
            }
            
            // 确保当前用户设置正确
            const appState = window.getAppState && window.getAppState();
            if (appState) {
                const userEmail = appState.get('auth.user.email');
                if (userEmail) {
                    manager.setCurrentUser(userEmail);
                    console.log(`✅ 设置当前用户: ${userEmail}`);
                } else {
                    // 设置默认用户
                    manager.setCurrentUser('<EMAIL>');
                    console.log('✅ 设置默认用户: <EMAIL>');
                }
            } else {
                manager.setCurrentUser('<EMAIL>');
                console.log('✅ 设置默认用户: <EMAIL>');
            }
            
            return manager;
            
        } catch (error) {
            console.error('❌ 修复历史订单管理器失败:', error);
            return false;
        }
    }
    
    // 修复2: 修复localStorage数据格式
    function fixLocalStorageData() {
        console.log('🔧 修复localStorage数据...');
        
        try {
            const storageKey = 'ota_order_history';
            const existingData = localStorage.getItem(storageKey);
            
            if (!existingData) {
                // 初始化空的历史数据
                localStorage.setItem(storageKey, JSON.stringify({}));
                console.log('✅ 初始化空的历史数据结构');
                return true;
            }
            
            const parsed = JSON.parse(existingData);
            
            // 如果是旧的数组格式，进行迁移
            if (Array.isArray(parsed)) {
                console.log('🔄 发现旧格式数据，进行迁移...');
                const migratedData = {
                    '<EMAIL>': parsed
                };
                localStorage.setItem(storageKey, JSON.stringify(migratedData));
                console.log(`✅ 已迁移 ${parsed.length} 条历史记录到新格式`);
            } else if (typeof parsed === 'object') {
                console.log('✅ 数据格式正确，无需迁移');
            } else {
                // 数据格式错误，重置
                localStorage.setItem(storageKey, JSON.stringify({}));
                console.log('⚠️ 数据格式错误，已重置');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 修复localStorage数据失败:', error);
            // 重置为空数据
            localStorage.setItem('ota_order_history', JSON.stringify({}));
            return false;
        }
    }
    
    // 修复3: 修复DOM元素和事件绑定
    function fixDOMAndEvents() {
        console.log('🔧 修复DOM和事件绑定...');
        
        try {
            // 检查历史面板元素
            const historyPanel = document.getElementById('historyPanel');
            if (!historyPanel) {
                console.error('❌ historyPanel 元素不存在');
                return false;
            }
            
            // 确保面板样式正确
            historyPanel.style.position = 'fixed';
            historyPanel.style.zIndex = '9999';
            
            // 检查历史按钮
            const historyBtn = document.getElementById('historyBtn');
            if (!historyBtn) {
                console.error('❌ historyBtn 元素不存在');
                return false;
            }
            
            // 重新绑定点击事件（作为备用）
            historyBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('🎯 历史按钮被点击，尝试显示历史面板...');
                
                try {
                    // 尝试多种方式获取历史管理器
                    let manager = null;
                    
                    if (window.getOrderHistoryManager) {
                        manager = window.getOrderHistoryManager();
                    } else if (window.OTA && window.OTA.orderHistoryManager) {
                        manager = window.OTA.orderHistoryManager;
                    } else if (window.orderHistoryManager) {
                        manager = window.orderHistoryManager;
                    }
                    
                    if (manager && manager.showHistoryPanel) {
                        manager.showHistoryPanel();
                        console.log('✅ 通过管理器显示历史面板');
                    } else {
                        // 降级方案：直接显示面板
                        historyPanel.classList.remove('hidden');
                        historyPanel.style.display = 'flex';
                        console.log('✅ 使用降级方案显示历史面板');
                        
                        // 尝试渲染历史数据
                        if (manager && manager.renderHistory && manager.getHistory) {
                            const history = manager.getHistory();
                            manager.renderHistory(history);
                            manager.updateStatistics(history);
                            manager.updateRecordCount(history.length);
                        }
                    }
                } catch (error) {
                    console.error('❌ 显示历史面板失败:', error);
                }
            }, { passive: false });
            
            console.log('✅ DOM和事件修复完成');
            return true;
            
        } catch (error) {
            console.error('❌ 修复DOM和事件失败:', error);
            return false;
        }
    }
    
    // 修复4: 创建示例数据（如果没有数据）
    function createSampleDataIfEmpty() {
        console.log('🔧 检查并创建示例数据...');
        
        try {
            const manager = window.getOrderHistoryManager && window.getOrderHistoryManager();
            if (!manager) {
                console.log('⚠️ 无法获取历史管理器，跳过示例数据创建');
                return false;
            }
            
            const history = manager.getHistory();
            if (history.length === 0) {
                console.log('📝 创建示例历史订单...');
                
                const sampleOrders = [
                    {
                        customerName: '张三',
                        customerContact: '+60123456789',
                        customerEmail: '<EMAIL>',
                        pickup: '吉隆坡国际机场 KLIA',
                        destination: '双子塔 Petronas Twin Towers',
                        date: new Date().toISOString().split('T')[0],
                        time: '14:30',
                        passengerNumber: '2',
                        luggageNumber: '3',
                        otaReferenceNumber: 'SAMPLE001',
                        otaChannel: 'Booking.com',
                        subCategoryId: 'airport-transfer'
                    },
                    {
                        customerName: '李四',
                        customerContact: '+60187654321',
                        customerEmail: '<EMAIL>',
                        pickup: '吉隆坡中央车站 KL Sentral',
                        destination: 'KLCC购物中心',
                        date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // 昨天
                        time: '10:15',
                        passengerNumber: '1',
                        luggageNumber: '2',
                        otaReferenceNumber: 'SAMPLE002',
                        otaChannel: 'Agoda',
                        subCategoryId: 'city-transfer'
                    }
                ];
                
                sampleOrders.forEach((orderData, index) => {
                    const orderId = `SAMPLE_${Date.now()}_${index}`;
                    manager.addOrder(orderData, orderId, {
                        success: true,
                        message: '示例订单创建成功',
                        data: { id: orderId }
                    });
                });
                
                console.log(`✅ 已创建 ${sampleOrders.length} 个示例订单`);
                return true;
            } else {
                console.log(`✅ 已存在 ${history.length} 条历史记录，无需创建示例数据`);
                return true;
            }
            
        } catch (error) {
            console.error('❌ 创建示例数据失败:', error);
            return false;
        }
    }
    
    // 修复5: 强化渲染函数
    function enhanceRenderFunction() {
        console.log('🔧 强化渲染函数...');
        
        try {
            const manager = window.getOrderHistoryManager && window.getOrderHistoryManager();
            if (!manager) {
                console.log('⚠️ 无法获取历史管理器，跳过渲染函数强化');
                return false;
            }
            
            // 备份原始渲染函数
            const originalRenderHistory = manager.renderHistory;
            
            // 强化渲染函数
            manager.renderHistory = function(orders) {
                console.log(`🎨 开始渲染 ${orders.length} 条历史订单`);
                
                try {
                    // 调用原始函数
                    if (originalRenderHistory) {
                        originalRenderHistory.call(this, orders);
                    }
                    
                    // 额外检查和修复
                    const container = document.getElementById('historyListContainer');
                    if (container && orders.length > 0) {
                        // 确保容器可见
                        container.style.display = 'block';
                        
                        // 如果容器为空但有数据，进行手动渲染
                        if (container.children.length === 0) {
                            console.log('⚠️ 容器为空，进行手动渲染');
                            this.manualRenderHistory(orders);
                        }
                    }
                    
                    console.log('✅ 历史订单渲染完成');
                    
                } catch (error) {
                    console.error('❌ 渲染历史订单失败:', error);
                    
                    // 降级方案：显示错误信息
                    const container = document.getElementById('historyListContainer');
                    if (container) {
                        container.innerHTML = `
                            <div class="error-state" style="text-align: center; padding: 20px; color: #dc3545;">
                                <div style="font-size: 2rem; margin-bottom: 10px;">❌</div>
                                <div>渲染历史订单时出现错误</div>
                                <div style="font-size: 0.9em; margin-top: 10px;">${error.message}</div>
                            </div>
                        `;
                    }
                }
            };
            
            // 添加手动渲染方法
            manager.manualRenderHistory = function(orders) {
                const container = document.getElementById('historyListContainer');
                if (!container) return;
                
                if (orders.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state" style="text-align: center; padding: 40px; color: #6c757d;">
                            <div style="font-size: 3rem; margin-bottom: 15px;">📝</div>
                            <div>暂无历史订单</div>
                        </div>
                    `;
                    return;
                }
                
                const ordersHtml = orders.map((order, index) => {
                    const isSuccess = order.metadata?.apiResponse?.success !== false && 
                                    !String(order.orderId || '').startsWith('FAILED_');
                    const statusText = isSuccess ? '✅ 成功' : '❌ 失败';
                    const statusColor = isSuccess ? '#28a745' : '#dc3545';
                    
                    let timeStr = 'N/A';
                    try {
                        timeStr = new Date(order.timestamp).toLocaleString();
                    } catch (e) {
                        timeStr = order.timestamp || 'N/A';
                    }
                    
                    return `
                        <div class="history-item" style="border: 1px solid #e0e0e0; border-radius: 8px; margin-bottom: 10px; overflow: hidden;">
                            <div class="history-item-header" style="background: #f8f9fa; padding: 10px 15px; display: flex; justify-content: space-between; align-items: center;">
                                <span><strong>订单ID:</strong> ${order.orderId || 'N/A'}</span>
                                <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                                <span style="font-size: 0.9em; color: #6c757d;">${timeStr}</span>
                            </div>
                            <div class="history-item-content" style="padding: 15px;">
                                <p><strong>客户:</strong> ${order.orderData?.customerName || 'N/A'}</p>
                                <p><strong>服务:</strong> ${order.orderData?.subCategoryId || 'N/A'}</p>
                                <p><strong>OTA参考号:</strong> ${order.orderData?.otaReferenceNumber || 'N/A'}</p>
                                <p><strong>上车地点:</strong> ${order.orderData?.pickup || 'N/A'}</p>
                                <p><strong>目的地:</strong> ${order.orderData?.destination || 'N/A'}</p>
                                <p><strong>日期时间:</strong> ${order.orderData?.date || 'N/A'} ${order.orderData?.time || ''}</p>
                            </div>
                        </div>
                    `;
                }).join('');
                
                container.innerHTML = ordersHtml;
                console.log('✅ 手动渲染完成');
            };
            
            console.log('✅ 渲染函数强化完成');
            return true;
            
        } catch (error) {
            console.error('❌ 强化渲染函数失败:', error);
            return false;
        }
    }
    
    // 主修复流程
    function runCompleteFix() {
        console.log('🚀 开始完整修复流程...');
        
        const fixes = [
            { name: 'LocalStorage数据', fn: fixLocalStorageData },
            { name: '历史订单管理器', fn: fixHistoryManager },
            { name: 'DOM和事件绑定', fn: fixDOMAndEvents },
            { name: '渲染函数强化', fn: enhanceRenderFunction },
            { name: '示例数据创建', fn: createSampleDataIfEmpty }
        ];
        
        const results = [];
        
        fixes.forEach(fix => {
            console.log(`🔧 正在修复: ${fix.name}`);
            try {
                const result = fix.fn();
                results.push({ name: fix.name, success: result });
                console.log(`${result ? '✅' : '❌'} ${fix.name}: ${result ? '成功' : '失败'}`);
            } catch (error) {
                results.push({ name: fix.name, success: false, error: error.message });
                console.error(`❌ ${fix.name}: ${error.message}`);
            }
        });
        
        // 生成修复报告
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        console.log('\n📊 修复报告:');
        console.log(`成功: ${successCount}/${totalCount}`);
        
        results.forEach(result => {
            console.log(`${result.success ? '✅' : '❌'} ${result.name}${result.error ? ` (${result.error})` : ''}`);
        });
        
        if (successCount === totalCount) {
            console.log('🎉 所有修复完成，历史订单模块应该可以正常工作了！');
            
            // 尝试自动测试
            setTimeout(() => {
                try {
                    const manager = window.getOrderHistoryManager();
                    if (manager) {
                        const history = manager.getHistory();
                        console.log(`📋 当前历史记录数: ${history.length}`);
                        
                        if (history.length > 0) {
                            console.log('🎯 尝试自动渲染历史记录...');
                            manager.renderHistory(history);
                            manager.updateStatistics && manager.updateStatistics(history);
                            manager.updateRecordCount && manager.updateRecordCount(history.length);
                        }
                    }
                } catch (error) {
                    console.error('⚠️ 自动测试失败:', error);
                }
            }, 1000);
            
        } else {
            console.log('⚠️ 部分修复失败，可能仍存在问题');
        }
        
        return { successCount, totalCount, results };
    }
    
    // 公开API
    window.fixHistoryOrderModule = {
        runCompleteFix,
        fixHistoryManager,
        fixLocalStorageData,
        fixDOMAndEvents,
        createSampleDataIfEmpty,
        enhanceRenderFunction
    };
    
    // 如果是直接运行，延迟执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runCompleteFix, 1000);
        });
    } else {
        setTimeout(runCompleteFix, 1000);
    }
    
})();

console.log('✅ 历史订单修复脚本已加载');
