/**
 * 车型配置集成验证器
 * 🏷️ 标签: @OTA_VEHICLE_CONFIG_INTEGRATION
 * 📝 说明: 验证车型配置管理器的集成状态，确保所有服务正确使用统一配置
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    /**
     * 车型配置集成验证器
     */
    class VehicleConfigIntegration {
        constructor() {
            this.initialized = false;
            this.logger = null;
        }

        /**
         * 初始化集成验证器
         */
        init() {
            if (this.initialized) return;

            this.logger = this.getLogger();
            this.validateIntegration();
            this.setupIntegrationMonitoring();
            
            this.initialized = true;
            this.log('车型配置集成验证器已初始化', 'info');
        }

        /**
         * 验证车型配置管理器集成
         */
        validateIntegration() {
            const results = {
                configManagerAvailable: false,
                defaultCarTypeId: null,
                servicesIntegrated: 0,
                servicesChecked: 0,
                issues: []
            };

            try {
                // 验证配置管理器可用性
                const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                if (vehicleConfigManager) {
                    results.configManagerAvailable = true;
                    results.defaultCarTypeId = vehicleConfigManager.getDefaultCarTypeId();
                    
                    // 验证默认车型ID是否为5
                    if (results.defaultCarTypeId === 5) {
                        this.log('✅ 默认车型ID验证通过: 5', 'info');
                    } else {
                        results.issues.push(`默认车型ID不正确: 期望5, 实际${results.defaultCarTypeId}`);
                    }
                } else {
                    results.issues.push('车型配置管理器不可用');
                }

                // 验证各服务的集成状态
                const servicesToCheck = [
                    { name: 'GeminiService', check: this.validateGeminiServiceIntegration },
                    { name: 'ApiService', check: this.validateApiServiceIntegration },
                    { name: 'MultiOrderTransformer', check: this.validateTransformerIntegration }
                ];

                servicesToCheck.forEach(service => {
                    results.servicesChecked++;
                    try {
                        if (service.check.call(this)) {
                            results.servicesIntegrated++;
                            this.log(`✅ ${service.name} 集成验证通过`, 'info');
                        } else {
                            results.issues.push(`${service.name} 集成验证失败`);
                        }
                    } catch (error) {
                        results.issues.push(`${service.name} 集成验证出错: ${error.message}`);
                    }
                });

                // 生成集成报告
                this.generateIntegrationReport(results);

            } catch (error) {
                results.issues.push(`集成验证过程出错: ${error.message}`);
                this.log('集成验证失败', 'error', { error: error.message });
            }

            return results;
        }

        /**
         * 验证Gemini服务集成
         */
        validateGeminiServiceIntegration() {
            try {
                const geminiService = getGeminiService ? getGeminiService() : null;
                if (!geminiService) return false;

                // 测试车型推荐功能
                if (typeof geminiService.recommendCarType === 'function') {
                    const recommendedId = geminiService.recommendCarType(2); // 2个乘客应该返回5
                    if (recommendedId === 5) {
                        return true;
                    }
                }
                
                return false;
            } catch (error) {
                this.log('Gemini服务集成验证出错', 'warning', { error: error.message });
                return false;
            }
        }

        /**
         * 验证API服务集成
         */
        validateApiServiceIntegration() {
            try {
                const apiService = getApiService ? getApiService() : null;
                if (!apiService) return false;

                // 测试车型推荐功能
                if (typeof apiService.recommendCarType === 'function') {
                    const recommendedId = apiService.recommendCarType(2); // 2个乘客应该返回5
                    if (recommendedId === 5) {
                        return true;
                    }
                }
                
                return false;
            } catch (error) {
                this.log('API服务集成验证出错', 'warning', { error: error.message });
                return false;
            }
        }

        /**
         * 验证Transformer集成
         */
        validateTransformerIntegration() {
            try {
                // 检查MultiOrderTransformer是否能获取默认车型
                const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                if (vehicleConfigManager) {
                    const defaultId = vehicleConfigManager.getDefaultCarTypeId();
                    return defaultId === 5;
                }
                return false;
            } catch (error) {
                this.log('Transformer集成验证出错', 'warning', { error: error.message });
                return false;
            }
        }

        /**
         * 生成集成报告
         */
        generateIntegrationReport(results) {
            const report = {
                timestamp: new Date().toISOString(),
                status: results.issues.length === 0 ? 'SUCCESS' : 'ISSUES_FOUND',
                summary: {
                    configManagerAvailable: results.configManagerAvailable,
                    defaultCarTypeId: results.defaultCarTypeId,
                    servicesIntegrated: results.servicesIntegrated,
                    servicesChecked: results.servicesChecked,
                    integrationRate: results.servicesChecked > 0 ? 
                        (results.servicesIntegrated / results.servicesChecked * 100).toFixed(1) + '%' : '0%'
                },
                issues: results.issues,
                recommendations: this.generateRecommendations(results)
            };

            this.log('🔍 车型配置集成报告', 'info', report);

            // 如果有问题，显示警告
            if (results.issues.length > 0) {
                this.log(`⚠️ 发现 ${results.issues.length} 个集成问题`, 'warning', {
                    issues: results.issues
                });
            } else {
                this.log('🎉 车型配置集成验证完全通过！', 'success');
            }

            return report;
        }

        /**
         * 生成改进建议
         */
        generateRecommendations(results) {
            const recommendations = [];

            if (!results.configManagerAvailable) {
                recommendations.push('确保车型配置管理器正确加载和初始化');
            }

            if (results.defaultCarTypeId !== 5) {
                recommendations.push('检查车型配置管理器的默认车型设置');
            }

            if (results.servicesIntegrated < results.servicesChecked) {
                recommendations.push('检查未通过集成验证的服务，确保正确调用车型配置管理器');
            }

            if (results.issues.length > 0) {
                recommendations.push('解决集成验证过程中发现的问题');
            }

            return recommendations;
        }

        /**
         * 设置集成监控
         */
        setupIntegrationMonitoring() {
            try {
                // 监控配置管理器状态变化
                const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                if (vehicleConfigManager && typeof vehicleConfigManager.subscribe === 'function') {
                    vehicleConfigManager.subscribe((changes, newConfig) => {
                        this.log('车型配置发生变更', 'info', {
                            changes: changes,
                            newDefaultCarTypeId: newConfig.defaults.carTypeId
                        });
                        
                        // 重新验证集成状态
                        setTimeout(() => {
                            this.validateIntegration();
                        }, 100);
                    });
                }
            } catch (error) {
                this.log('设置集成监控失败', 'warning', { error: error.message });
            }
        }

        /**
         * 执行车型配置一致性检查
         */
        performConsistencyCheck() {
            const results = {
                checkedFiles: 0,
                inconsistencies: [],
                summary: {}
            };

            const servicesToCheck = [
                {
                    name: 'GeminiService',
                    getValue: () => {
                        const gemini = getGeminiService();
                        return gemini ? gemini.recommendCarType(2) : null;
                    }
                },
                {
                    name: 'ApiService', 
                    getValue: () => {
                        const api = getApiService();
                        return api ? api.recommendCarType(2) : null;
                    }
                },
                {
                    name: 'VehicleConfigManager',
                    getValue: () => {
                        const config = getVehicleConfigManager();
                        return config ? config.getDefaultCarTypeId() : null;
                    }
                }
            ];

            const expectedValue = 5; // 统一的默认车型ID

            servicesToCheck.forEach(service => {
                try {
                    const value = service.getValue();
                    results.checkedFiles++;
                    
                    if (value !== expectedValue) {
                        results.inconsistencies.push({
                            service: service.name,
                            expected: expectedValue,
                            actual: value
                        });
                    }
                    
                    results.summary[service.name] = value;
                } catch (error) {
                    results.inconsistencies.push({
                        service: service.name,
                        error: error.message
                    });
                }
            });

            this.log('车型配置一致性检查完成', 'info', {
                checkedServices: results.checkedFiles,
                inconsistencies: results.inconsistencies.length,
                results: results.summary
            });

            return results;
        }

        /**
         * 获取Logger实例
         */
        getLogger() {
            try {
                return window.getLogger ? window.getLogger() : console;
            } catch (e) {
                return console;
            }
        }

        /**
         * 日志输出
         */
        log(message, level = 'info', data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(`[VehicleConfigIntegration] ${message}`, level, data);
            } else {
                console.log(`[VehicleConfigIntegration] ${message}`, data);
            }
        }
    }

    // 创建全局实例
    const vehicleConfigIntegration = new VehicleConfigIntegration();

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.VehicleConfigIntegration = VehicleConfigIntegration;
    window.OTA.vehicleConfigIntegration = vehicleConfigIntegration;

    // 便捷访问函数
    window.getVehicleConfigIntegration = () => vehicleConfigIntegration;

    // 自动初始化（延迟执行，确保其他模块已加载）
    setTimeout(() => {
        try {
            vehicleConfigIntegration.init();
        } catch (error) {
            console.error('车型配置集成验证器初始化失败:', error);
        }
    }, 2000);

    console.log('✅ 车型配置集成验证器已加载');

})();