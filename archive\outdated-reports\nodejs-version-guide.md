# Netlify Node.js 版本支持指南

## 问题分析

**错误**: `Version '22.x' not found`

**原因**: Netlify 当前不支持 Node.js 22.x 版本

## Netlify 支持的 Node.js 版本

### ✅ 推荐版本（稳定且广泛支持）

1. **Node.js 18** （推荐）
   ```toml
   NODE_VERSION = "18"
   ```
   - LTS 版本，长期支持
   - Netlify 完全支持
   - 性能优秀，稳定性高

2. **Node.js 20** 
   ```toml
   NODE_VERSION = "20"
   ```
   - 当前 LTS 版本
   - 大多数 Netlify 环境支持

3. **Node.js 16** 
   ```toml
   NODE_VERSION = "16"
   ```
   - 较老的 LTS 版本
   - 完全兼容，但建议升级

### ❌ 不支持的版本

- **Node.js 22.x** - 过新，Netlify 尚未支持
- **Node.js 21.x** - 非 LTS 版本，不稳定
- **Node.js 19.x** - 非 LTS 版本，已废弃

## 当前修复

已将配置更改为：
```toml
[build.environment]
  NODE_VERSION = "18"
```

## 版本选择建议

### 对于生产环境：
- **首选**: Node.js 18 (最稳定)
- **备选**: Node.js 20 (最新 LTS)

### 对于您的项目：
Node.js 18 是最佳选择，因为：
1. ✅ 完全兼容您的静态站点项目
2. ✅ Netlify 100% 支持
3. ✅ 长期支持版本（LTS）
4. ✅ 性能和稳定性优秀
5. ✅ 广泛的生态系统支持

## 验证新配置

修复后的部署应该显示：
```
✅ Installing Node.js version 18
✅ Now using node v18.x.x (npm v10.x.x)
```

## 如果仍有问题

如果 Node.js 18 也有问题，可以尝试：

1. **明确指定版本**:
   ```toml
   NODE_VERSION = "18.17.0"
   ```

2. **使用默认版本**:
   ```toml
   # 删除 NODE_VERSION 配置，使用 Netlify 默认版本
   ```

3. **检查 Netlify 构建环境**:
   在构建日志中查看支持的版本列表

## 相关资源

- [Netlify Node.js 支持文档](https://docs.netlify.com/configure-builds/manage-dependencies/#node-js-and-javascript)
- [Node.js LTS 发布计划](https://nodejs.org/en/about/releases/)
- [Netlify 构建环境文档](https://docs.netlify.com/configure-builds/build-environment/)
