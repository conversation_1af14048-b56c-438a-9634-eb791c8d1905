/**
 * ============================================================================
 * 🎬 动画管理器 - 统一动画控制系统
 * ============================================================================
 *
 * @fileoverview 动画管理器 - 统一管理所有UI动画效果
 * @description 提供动画开关控制、用户偏好管理和响应式动画适配
 * 
 * @features 核心功能
 * - 统一动画控制和管理
 * - 用户偏好设置支持
 * - 响应式动画适配
 * - 性能优化和可访问性支持
 * - 与现有UI系统完美集成
 * 
 * @integration 集成说明
 * - 与字段标准化系统兼容
 * - 不影响现有功能逻辑
 * - 支持动态开关控制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.OTA && window.OTA.AnimationManager) {
        console.log('[AnimationManager] 动画管理器已存在，跳过重复加载');
        return;
    }

    /**
     * 动画管理器类
     * 统一管理所有UI动画效果
     */
    class AnimationManager {
        constructor() {
            this.logger = this.getLogger();
            this.isEnabled = true; // 默认启用动画
            this.animationQueue = new Map(); // 动画队列
            this.activeAnimations = new Set(); // 活跃动画集合
            
            // 动画配置
            this.config = {
                // 动画持续时间（毫秒）
                durations: {
                    fast: 150,
                    normal: 300,
                    slow: 500,
                    progress: 1000
                },
                // 缓动函数
                easings: {
                    easeOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
                    easeInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
                    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
                },
                // 动画类型配置
                types: {
                    fieldFill: { duration: 'normal', easing: 'easeOut' },
                    buttonClick: { duration: 'fast', easing: 'easeInOut' },
                    statusFeedback: { duration: 'normal', easing: 'bounce' },
                    progress: { duration: 'progress', easing: 'easeOut' }
                }
            };

            this.initialize();
        }

        /**
         * 获取日志记录器
         * @returns {Object} 日志记录器实例
         */
        getLogger() {
            return window.getLogger ? window.getLogger() : console;
        }

        /**
         * 初始化动画管理器
         */
        initialize() {
            this.logger.log('🎬 初始化动画管理器...', 'info');
            
            // 检查用户偏好
            this.loadUserPreferences();
            
            // 检查系统偏好（减少动画）
            this.checkSystemPreferences();
            
            // 设置CSS变量
            this.setCSSVariables();
            
            // 监听偏好变化
            this.setupPreferenceListeners();

            // 🎬 初始化按钮动画处理器
            this.initializeButtonAnimations();

            this.logger.log('✅ 动画管理器初始化完成', 'success', {
                enabled: this.isEnabled,
                config: this.config
            });
        }

        /**
         * 加载用户偏好设置
         */
        loadUserPreferences() {
            try {
                const appState = window.getAppState ? window.getAppState() : null;
                if (appState) {
                    // 从应用状态获取动画偏好
                    const animationEnabled = appState.get('config.animationsEnabled');
                    if (animationEnabled !== undefined) {
                        this.isEnabled = animationEnabled;
                    }
                }
            } catch (error) {
                this.logger.log('加载用户动画偏好失败，使用默认设置', 'warning', { error: error.message });
            }
        }

        /**
         * 检查系统偏好（可访问性）
         */
        checkSystemPreferences() {
            // 检查用户是否偏好减少动画
            if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                this.isEnabled = false;
                this.logger.log('检测到用户偏好减少动画，已禁用动画效果', 'info');
            }
        }

        /**
         * 设置CSS变量
         */
        setCSSVariables() {
            const root = document.documentElement;
            
            // 设置动画持续时间变量
            Object.entries(this.config.durations).forEach(([key, value]) => {
                root.style.setProperty(`--animation-duration-${key}`, `${value}ms`);
            });
            
            // 设置缓动函数变量
            Object.entries(this.config.easings).forEach(([key, value]) => {
                root.style.setProperty(`--animation-easing-${key}`, value);
            });
            
            // 设置动画启用状态
            root.style.setProperty('--animations-enabled', this.isEnabled ? '1' : '0');
        }

        /**
         * 设置偏好监听器
         */
        setupPreferenceListeners() {
            // 监听媒体查询变化
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addListener((e) => {
                    if (e.matches) {
                        this.disable();
                    } else {
                        this.enable();
                    }
                });
            }

            // 监听应用状态变化
            try {
                const appState = window.getAppState ? window.getAppState() : null;
                if (appState && typeof appState.on === 'function') {
                    appState.on('config.animationsEnabled', (enabled) => {
                        if (enabled) {
                            this.enable();
                        } else {
                            this.disable();
                        }
                    });
                }
            } catch (error) {
                this.logger.log('设置应用状态监听器失败', 'warning', { error: error.message });
            }
        }

        /**
         * 启用动画
         */
        enable() {
            this.isEnabled = true;
            document.documentElement.style.setProperty('--animations-enabled', '1');
            document.body.classList.remove('animations-disabled');
            this.logger.log('✅ 动画已启用', 'info');
        }

        /**
         * 禁用动画
         */
        disable() {
            this.isEnabled = false;
            document.documentElement.style.setProperty('--animations-enabled', '0');
            document.body.classList.add('animations-disabled');
            this.logger.log('⏸️ 动画已禁用', 'info');
        }

        /**
         * 检查动画是否启用
         * @returns {boolean} 是否启用动画
         */
        isAnimationEnabled() {
            return this.isEnabled;
        }

        /**
         * 初始化按钮动画处理器
         * 🎬 新增：为所有按钮添加点击动画支持
         */
        initializeButtonAnimations() {
            if (!this.isEnabled) {
                return;
            }

            try {
                // 为所有按钮添加点击动画事件监听器
                document.addEventListener('click', (event) => {
                    const button = event.target.closest('.btn');
                    if (button && !button.disabled) {
                        this.animateButtonClick(button);
                    }
                });

                // 为按钮添加悬停动画增强
                document.addEventListener('mouseover', (event) => {
                    const button = event.target.closest('.btn');
                    if (button && !button.disabled && this.isEnabled) {
                        this.enhanceButtonHover(button);
                    }
                });

                this.logger.log('✅ 按钮动画处理器初始化完成', 'success');
            } catch (error) {
                this.logger.log('按钮动画处理器初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 增强按钮悬停效果
         * 🎬 新增：动态增强按钮悬停动画
         * @param {HTMLElement} button - 按钮元素
         */
        enhanceButtonHover(button) {
            if (!this.isEnabled || !button) {
                return;
            }

            // 添加悬停增强类
            button.classList.add('btn-hover-enhanced');

            // 移除增强类的清理函数
            const removeEnhancement = () => {
                button.classList.remove('btn-hover-enhanced');
                button.removeEventListener('mouseleave', removeEnhancement);
            };

            button.addEventListener('mouseleave', removeEnhancement, { once: true });
        }

        /**
         * 执行字段填充动画
         * @param {HTMLElement} element - 目标元素
         * @param {string} value - 填充值
         * @param {Object} options - 动画选项
         * @returns {Promise} 动画完成Promise
         */
        async animateFieldFill(element, value, options = {}) {
            if (!this.isEnabled || !element) {
                if (element) element.value = value;
                return Promise.resolve();
            }

            const animationId = `field-fill-${Date.now()}`;
            this.activeAnimations.add(animationId);

            try {
                // 添加动画类
                element.classList.add('field-filling');
                
                // 设置动画样式
                const config = this.config.types.fieldFill;
                element.style.transition = `all ${this.config.durations[config.duration]}ms ${this.config.easings[config.easing]}`;
                
                // 执行填充动画
                await this.executeFieldFillAnimation(element, value, options);
                
                // 清理动画类
                element.classList.remove('field-filling');
                element.style.transition = '';
                
            } catch (error) {
                this.logger.log('字段填充动画执行失败', 'error', { error: error.message });
                // 降级：直接设置值
                if (element) element.value = value;
            } finally {
                this.activeAnimations.delete(animationId);
            }
        }

        /**
         * 执行字段填充动画的具体实现
         * @param {HTMLElement} element - 目标元素
         * @param {string} value - 填充值
         * @param {Object} options - 动画选项
         * @returns {Promise} 动画完成Promise
         */
        executeFieldFillAnimation(element, value, options = {}) {
            return new Promise((resolve) => {
                // 先淡出
                element.style.opacity = '0.3';
                element.style.transform = 'scale(0.98)';
                
                setTimeout(() => {
                    // 设置值
                    element.value = value;
                    
                    // 淡入并恢复
                    element.style.opacity = '1';
                    element.style.transform = 'scale(1)';
                    
                    // 添加高亮效果
                    element.classList.add('field-highlight');
                    
                    setTimeout(() => {
                        element.classList.remove('field-highlight');
                        resolve();
                    }, this.config.durations.normal);
                    
                }, this.config.durations.fast);
            });
        }

        /**
         * 执行按钮点击动画
         * @param {HTMLElement} button - 按钮元素
         * @param {Object} options - 动画选项
         * @returns {Promise} 动画完成Promise
         */
        async animateButtonClick(button, options = {}) {
            if (!this.isEnabled || !button) {
                return Promise.resolve();
            }

            const animationId = `button-click-${Date.now()}`;
            this.activeAnimations.add(animationId);

            try {
                // 添加点击动画类
                button.classList.add('btn-clicking');
                
                // 执行点击动画
                await new Promise(resolve => {
                    setTimeout(() => {
                        button.classList.remove('btn-clicking');
                        resolve();
                    }, this.config.durations.fast);
                });
                
            } catch (error) {
                this.logger.log('按钮点击动画执行失败', 'error', { error: error.message });
            } finally {
                this.activeAnimations.delete(animationId);
            }
        }

        /**
         * 执行状态反馈动画
         * @param {HTMLElement} element - 目标元素
         * @param {string} status - 状态类型 ('success', 'error', 'warning', 'info')
         * @param {Object} options - 动画选项
         * @returns {Promise} 动画完成Promise
         */
        async animateStatusFeedback(element, status, options = {}) {
            if (!this.isEnabled || !element) {
                return Promise.resolve();
            }

            const animationId = `status-feedback-${Date.now()}`;
            this.activeAnimations.add(animationId);

            try {
                // 清除之前的状态类
                element.classList.remove('status-success', 'status-error', 'status-warning', 'status-info');
                
                // 添加新的状态类和动画类
                element.classList.add(`status-${status}`, 'status-animating');
                
                // 执行状态动画
                await new Promise(resolve => {
                    setTimeout(() => {
                        element.classList.remove('status-animating');
                        resolve();
                    }, this.config.durations.normal);
                });
                
            } catch (error) {
                this.logger.log('状态反馈动画执行失败', 'error', { error: error.message });
            } finally {
                this.activeAnimations.delete(animationId);
            }
        }

        /**
         * 执行进度动画
         * @param {HTMLElement} element - 进度元素
         * @param {number} progress - 进度百分比 (0-100)
         * @param {Object} options - 动画选项
         * @returns {Promise} 动画完成Promise
         */
        async animateProgress(element, progress, options = {}) {
            if (!this.isEnabled || !element) {
                return Promise.resolve();
            }

            const animationId = `progress-${Date.now()}`;
            this.activeAnimations.add(animationId);

            try {
                // 设置进度动画
                element.style.transition = `width ${this.config.durations.normal}ms ${this.config.easings.easeOut}`;
                element.style.width = `${Math.min(Math.max(progress, 0), 100)}%`;
                
                // 等待动画完成
                await new Promise(resolve => {
                    setTimeout(resolve, this.config.durations.normal);
                });
                
            } catch (error) {
                this.logger.log('进度动画执行失败', 'error', { error: error.message });
            } finally {
                this.activeAnimations.delete(animationId);
            }
        }

        /**
         * 获取动画统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                enabled: this.isEnabled,
                activeAnimations: this.activeAnimations.size,
                queuedAnimations: this.animationQueue.size,
                config: this.config
            };
        }
    }

    // 创建全局实例
    const animationManager = new AnimationManager();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.AnimationManager = AnimationManager;
    window.OTA.animationManager = animationManager;

    // 兼容性导出
    window.animationManager = animationManager;

    console.log('✅ 动画管理器已加载并初始化');

})();
