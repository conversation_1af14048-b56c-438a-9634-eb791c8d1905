<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlightAware API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            background-color: #3498db;
            color: white;
        }
        .btn:hover { opacity: 0.8; }
        .btn-success { background-color: #27ae60; }
        .btn-warning { background-color: #f39c12; }
        
        #testLog {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .api-info {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛫 FlightAware API 测试</h1>
        
        <div class="api-info warning">
            <strong>⚠️ 重要说明：</strong><br>
            由于浏览器的CORS限制，无法直接从前端调用FlightAware API。<br>
            这个测试页面将演示CORS错误，证明为什么需要Netlify Functions作为代理。
        </div>
        
        <div class="api-info">
            <strong>🎯 测试目的：</strong><br>
            1. 验证API密钥是否有效<br>
            2. 演示CORS问题<br>
            3. 确认需要使用Netlify Functions代理
        </div>
        
        <button class="btn btn-success" onclick="testDirectAPI()">测试直接API调用（会失败）</button>
        <button class="btn btn-warning" onclick="testNetlifyFunction()">测试Netlify Function（本地会失败）</button>
        <button class="btn" onclick="clearLog()">清空日志</button>
        
        <div id="testLog">等待测试开始...\n</div>
    </div>

    <script>
        const API_KEY = 'E4zN674dWvkwyfJWnH0gOGWMCoMGBdmV';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logElement.textContent += `[${timestamp}] ${statusIcon} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
            log('测试日志已清空');
        }

        // 测试直接API调用（会因为CORS失败）
        async function testDirectAPI() {
            log('🧪 测试直接调用FlightAware API...');
            log('⚠️ 预期结果：CORS错误（这是正常的）', 'warning');
            
            try {
                const response = await fetch('https://aeroapi.flightaware.com/aeroapi/flights/MH370', {
                    method: 'GET',
                    headers: {
                        'x-apikey': API_KEY,
                        'Accept': 'application/json'
                    }
                });
                
                // 如果到达这里，说明没有CORS错误（不太可能）
                const data = await response.json();
                log('🎉 意外成功！API调用成功', 'success');
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
            } catch (error) {
                if (error.message.includes('CORS') || error.message.includes('fetch')) {
                    log('✅ 预期的CORS错误发生了', 'success');
                    log(`错误信息: ${error.message}`);
                    log('💡 这证明了为什么需要Netlify Functions作为代理');
                } else {
                    log(`❌ 其他错误: ${error.message}`, 'error');
                }
            }
        }

        // 测试Netlify Function调用
        async function testNetlifyFunction() {
            log('🧪 测试Netlify Function调用...');
            
            const isLocal = window.location.protocol === 'file:' || 
                           window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1';
            
            if (isLocal) {
                log('🏠 检测到本地环境', 'warning');
                log('⚠️ 预期结果：网络错误（Netlify Functions在本地不可用）', 'warning');
            } else {
                log('🌐 检测到远程环境');
                log('🎯 如果配置正确，应该能成功调用API');
            }
            
            try {
                const response = await fetch('/api/flight-info?flight=MH370');
                
                if (!response.ok) {
                    const errorText = await response.text().catch(() => 'Unknown error');
                    log(`❌ HTTP错误: ${response.status} ${response.statusText}`, 'error');
                    log(`错误内容: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.success) {
                    log('🎉 Netlify Function调用成功！', 'success');
                    log(`航班数据: ${JSON.stringify(data.data, null, 2)}`);
                    log('✅ 功能正常工作，可以部署到生产环境');
                } else {
                    log(`⚠️ API返回错误: ${data.message}`, 'warning');
                    log(`错误代码: ${data.error}`);
                }
                
            } catch (error) {
                if (isLocal) {
                    log('✅ 预期的本地环境错误', 'success');
                    log(`错误信息: ${error.message}`);
                    log('💡 在Netlify部署后应该能正常工作');
                } else {
                    log(`❌ 生产环境错误: ${error.message}`, 'error');
                    log('🔧 请检查Netlify Functions配置和环境变量');
                }
            }
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            log('🚀 FlightAware API 测试页面已加载');
            log('📋 API密钥: ' + API_KEY.substring(0, 8) + '...' + API_KEY.substring(API_KEY.length - 4));
            
            const isLocal = window.location.protocol === 'file:' || 
                           window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1';
            
            if (isLocal) {
                log('🏠 当前在本地环境');
                log('💡 建议：部署到Netlify后进行完整测试');
            } else {
                log('🌐 当前在远程环境');
                log('🎯 可以进行完整的功能测试');
            }
        });
    </script>
</body>
</html>
