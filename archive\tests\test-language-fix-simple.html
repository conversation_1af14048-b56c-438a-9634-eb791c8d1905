<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言检测修复验证</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .test-area { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        input, textarea { width: 100%; padding: 8px; margin: 10px 0; }
        .checkbox-item { margin: 10px 0; }
        .result { background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 语言检测修复验证</h1>
    
    <div class="test-area">
        <h3>测试输入字段：</h3>
        <input type="text" id="customerName" placeholder="客户姓名">
        <input type="text" id="pickup" placeholder="上车地点">
        <textarea id="extraRequirement" placeholder="额外要求"></textarea>
        
        <h3>语言选择：</h3>
        <div class="checkbox-item">
            <input type="checkbox" id="lang_2" name="languagesIdArray" value="2">
            <label for="lang_2">English (EN)</label>
        </div>
        <div class="checkbox-item">
            <input type="checkbox" id="lang_4" name="languagesIdArray" value="4">
            <label for="lang_4">Chinese (CN)</label>
        </div>
        
        <button onclick="testEnglish()">测试英文内容</button>
        <button onclick="testChinese()">测试中文内容</button>
        <button onclick="clearAll()">清空所有</button>
    </div>
    
    <div class="result" id="result">等待测试...</div>

    <script>
        // 模拟基础依赖
        window.OTA = window.OTA || {};
        
        function getLogger() {
            return {
                log: (msg, level, data) => console.log(`[${level}] ${msg}`, data),
                logError: (msg, error) => console.error(msg, error)
            };
        }
        
        function updateResult(message) {
            document.getElementById('result').innerHTML = message;
        }
        
        function getCurrentSelection() {
            const selected = [];
            document.querySelectorAll('input[name="languagesIdArray"]:checked').forEach(cb => {
                const label = document.querySelector(`label[for="${cb.id}"]`);
                selected.push(label ? label.textContent : cb.value);
            });
            return selected;
        }
        
        function testEnglish() {
            document.getElementById('customerName').value = 'John Smith';
            document.getElementById('pickup').value = 'Kuala Lumpur Airport';
            document.getElementById('extraRequirement').value = 'Need English speaking driver';
            
            // 触发事件
            ['customerName', 'pickup', 'extraRequirement'].forEach(id => {
                const element = document.getElementById(id);
                element.dispatchEvent(new Event('input', { bubbles: true }));
            });
            
            setTimeout(() => {
                const selected = getCurrentSelection();
                updateResult(`✅ 英文测试完成<br>当前选择: ${selected.join(', ')}`);
            }, 500);
        }
        
        function testChinese() {
            document.getElementById('customerName').value = '张三';
            document.getElementById('pickup').value = '吉隆坡机场';
            document.getElementById('extraRequirement').value = '需要中文司机';
            
            // 触发事件
            ['customerName', 'pickup', 'extraRequirement'].forEach(id => {
                const element = document.getElementById(id);
                element.dispatchEvent(new Event('input', { bubbles: true }));
            });
            
            setTimeout(() => {
                const selected = getCurrentSelection();
                updateResult(`✅ 中文测试完成<br>当前选择: ${selected.join(', ')}`);
            }, 500);
        }
        
        function clearAll() {
            ['customerName', 'pickup', 'extraRequirement'].forEach(id => {
                document.getElementById(id).value = '';
            });
            document.querySelectorAll('input[name="languagesIdArray"]').forEach(cb => {
                cb.checked = false;
            });
            updateResult('已清空所有内容');
        }
        
        // 监听语言选择变化
        document.addEventListener('change', function(event) {
            if (event.target.name === 'languagesIdArray') {
                const selected = getCurrentSelection();
                console.log('语言选择变化:', selected);
            }
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            updateResult('✅ 页面已加载，可以开始测试');
        });
    </script>
    
    <!-- 加载核心语言检测器 -->
    <script src="js/core/language-detector.js"></script>
</body>
</html>