/**
 * 全局字段标准化拦截层
 * 基于GoMyHire API标准规范，实现非侵入式的字段统一映射
 * 
 * 核心功能：
 * 1. 自动拦截所有数据流，进行字段名称标准化
 * 2. 保证从数据源到API发送的整个数据流中字段名称完全一致
 * 3. 清理所有导致数据转换的冗余逻辑
 * 4. 消除字段名称不一致导致的数据映射错误
 * 
 * 设计原则：
 * - 非侵入性：不修改现有代码，通过拦截器实现
 * - 双向映射：支持API字段↔前端字段的双向转换
 * - 向后兼容：支持所有历史遗留字段格式
 * - 性能优化：缓存映射结果，减少重复计算
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.OTA && window.OTA.GlobalFieldStandardizationLayer) {
        console.log('[GlobalFieldStandardization] 字段标准化层已存在，跳过重复加载');
        return;
    }

    /**
     * 全局字段标准化拦截层类
     */
    class GlobalFieldStandardizationLayer {
        constructor() {
            this.initialized = false;
            this.interceptors = new Map();
            this.cache = new Map();
            this.statistics = {
                transformations: 0,
                cacheHits: 0,
                errors: 0
            };
            
            // 初始化标准字段映射表
            this.initializeStandardMappings();
            // 减法开发：默认不自动安装拦截器，受特性开关控制
        }

        /**
         * 初始化标准字段映射配置
         */
        initializeStandardMappings() {
            // GoMyHire API标准字段（snake_case）- 基于最新API文档
            this.API_STANDARD_FIELDS = {
                'sub_category_id': 'sub_category_id',
                'ota': 'ota',
                'ota_reference_number': 'ota_reference_number',
                'ota_price': 'ota_price',
                'customer_name': 'customer_name',
                'customer_contact': 'customer_contact',
                'customer_email': 'customer_email',
                'flight_info': 'flight_info',
                'pickup': 'pickup',
                'pickup_lat': 'pickup_lat',
                'pickup_long': 'pickup_long',
                'date': 'date',
                'time': 'time',
                'destination': 'destination',
                'destination_lat': 'destination_lat',
                'destination_long': 'destination_long',
                'car_type_id': 'car_type_id',
                'passenger_number': 'passenger_number',
                'luggage_number': 'luggage_number',
                'driver_fee': 'driver_fee',
                'driver_collect': 'driver_collect',
                'tour_guide': 'tour_guide',
                'baby_chair': 'baby_chair',
                'meet_and_greet': 'meet_and_greet',
                'extra_requirement': 'extra_requirement',
                'incharge_by_backend_user_id': 'incharge_by_backend_user_id',
                'driving_region_id': 'driving_region_id',
                'languages_id_array': 'languages_id_array',
                // 系统内部字段
                'currency': 'currency',
                'raw_text': 'raw_text'
            };

            // 统一字段映射表（包含所有变体到API标准字段的映射）
            this.UNIFIED_FIELD_MAPPING = {
                // === 客户信息字段统一 ===
                'customerName': 'customer_name',
                'customer_name': 'customer_name',
                'customerContact': 'customer_contact',
                'customer_contact': 'customer_contact',
                'customerEmail': 'customer_email',
                'customer_email': 'customer_email',

                // === 订单基础信息统一 ===
                'otaReferenceNumber': 'ota_reference_number',
                'ota_reference_number': 'ota_reference_number',
                'flightInfo': 'flight_info',
                'flight_info': 'flight_info',
                'flight_number': 'flight_info',

                // === 时间字段统一 ===
                'pickupDate': 'date',
                'pickup_date': 'date',
                'date': 'date',
                'pickupTime': 'time',
                'pickup_time': 'time',
                'time': 'time',

                // === 地点字段统一 ===
                'pickupLocation': 'pickup',
                'pickup_location': 'pickup',
                'pickup': 'pickup',
                'dropoffLocation': 'destination',
                'dropoff_location': 'destination',
                'dropoff': 'destination',
                'destination': 'destination',

                // === 坐标字段统一 ===
                'pickupLat': 'pickup_lat',
                'pickup_lat': 'pickup_lat',
                'pickupLong': 'pickup_long',
                'pickup_long': 'pickup_long',
                'destinationLat': 'destination_lat',
                'destination_lat': 'destination_lat',
                'destinationLong': 'destination_long',
                'destination_long': 'destination_long',

                // === 车型字段统一 ===
                'carTypeId': 'car_type_id',
                'car_type_id': 'car_type_id',
                'carType': 'car_type_id',
                'car_type': 'car_type_id',
                'vehicleType': 'car_type_id',
                'vehicle_type': 'car_type_id',
                'vehicle': 'car_type_id',

                // === 区域字段统一 ===
                'drivingRegionId': 'driving_region_id',
                'driving_region_id': 'driving_region_id',
                'drivingRegion': 'driving_region_id',
                'driving_region': 'driving_region_id',
                'region': 'driving_region_id',
                'area': 'driving_region_id',

                // === 服务类别统一 ===
                'subCategoryId': 'sub_category_id',
                'sub_category_id': 'sub_category_id',

                // === 乘客和行李字段统一 ===
                'passengerCount': 'passenger_number',
                'passenger_count': 'passenger_number',
                'passenger_number': 'passenger_number',
                'luggageCount': 'luggage_number',
                'luggage_count': 'luggage_number',
                'luggage_number': 'luggage_number',

                // === 语言字段统一 ===
                'languagesIdArray': 'languages_id_array',
                'languages_id_array': 'languages_id_array',

                // === 额外需求统一 ===
                'extraRequirement': 'extra_requirement',
                'extra_requirement': 'extra_requirement',

                // === 服务选项统一 ===
                'babyChair': 'baby_chair',
                'baby_chair': 'baby_chair',
                'tourGuide': 'tour_guide',
                'tour_guide': 'tour_guide',
                'meetAndGreet': 'meet_and_greet',
                'meet_and_greet': 'meet_and_greet',

                // === 价格字段统一 ===
                'otaPrice': 'ota_price',
                'ota_price': 'ota_price',
                'price': 'ota_price',
                'cost': 'ota_price',
                'amount': 'ota_price',

                // === 司机费用字段统一 ===
                'driverFee': 'driver_fee',
                'driver_fee': 'driver_fee',
                'driverCollect': 'driver_collect',
                'driver_collect': 'driver_collect',

                // === 货币字段统一 ===
                'currency': 'currency',

                // === 系统字段统一 ===
                'rawText': 'raw_text',
                'raw_text': 'raw_text',
                'ota': 'ota',
                'incharge_by_backend_user_id': 'incharge_by_backend_user_id'
            };

            // 反向映射：API字段 → 前端字段
            this.API_TO_FRONTEND_MAPPING = {
                'customer_name': 'customerName',
                'customer_contact': 'customerContact',
                'customer_email': 'customerEmail',
                'ota_reference_number': 'otaReferenceNumber',
                'flight_info': 'flightInfo',
                'date': 'pickupDate',
                'time': 'pickupTime',
                'pickup': 'pickupLocation',
                'destination': 'dropoffLocation',
                'car_type_id': 'carTypeId',
                'sub_category_id': 'subCategoryId',
                'driving_region_id': 'drivingRegionId',
                'passenger_number': 'passengerCount',
                'luggage_number': 'luggageCount',
                'languages_id_array': 'languagesIdArray',
                'extra_requirement': 'extraRequirement',
                'baby_chair': 'babyChair',
                'tour_guide': 'tourGuide',
                'meet_and_greet': 'meetAndGreet',
                'ota_price': 'otaPrice',
                'currency': 'currency',
                'raw_text': 'rawText',
                'ota': 'ota',
                'incharge_by_backend_user_id': 'inchargeByBackendUserId'
            };

            // 可忽略的字段（系统内部字段，不需要映射）
            this.IGNORABLE_FIELDS = new Set([
                '_otaChannel', 'needsPagingService', 'confidence', 'analysis',
                'timestamp', 'source', 'isMultiOrder', 'orderCount',
                'originalData', '_metadata', '__proto__', 'constructor'
            ]);
        }

        /**
         * 初始化拦截器系统
         */
        initialize() {
            if (this.initialized) {
                console.warn('[GlobalFieldStandardization] 拦截器已初始化，跳过');
                return;
            }

            // 若未开启开关则不安装
            const enabled = window.OTA?.featureToggle?.isEnabled('enableGlobalFieldStandardization');
            if (!enabled) {
                console.log('[GlobalFieldStandardization] 已加载（未启用，受特性开关控制）');
                return;
            }

            // 拦截Gemini服务输出
            this.interceptGeminiService();
            
            // 拦截API服务输入
            this.interceptApiService();
            
            // 拦截多订单管理器
            this.interceptMultiOrderManager();
            
            // 拦截表单管理器
            this.interceptFormManager();
            
            // 拦截实时分析管理器（主要的Gemini调用路径）
            this.interceptRealtimeAnalysisManager();

            this.initialized = true;
            console.log('[GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成');
            
            // 设置延迟重试机制，防止某些服务延迟加载
            this.setupDelayedRetry();
        }

        /**
         * 拦截Gemini服务的数据输出
         */
        interceptGeminiService() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 150; // 最长等待15秒，适配按顺序加载

            // 统一对结果进行标准化（数组/对象均支持），幂等
            const standardizeResult = (res) => {
                try {
                    if (!res) return res;
                    // 实时返回数组（新架构常见）
                    if (Array.isArray(res)) {
                        return res.map(o => self.standardizeToApiFields(o, 'gemini-output'));
                    }
                    // 非实时返回对象
                    if (typeof res === 'object') {
                        // 兼容旧结构 { orders: [...] }
                        if (Array.isArray(res.orders)) {
                            const clone = { ...res };
                            clone.orders = res.orders.map(o => self.standardizeToApiFields(o, 'gemini-output'));
                            return clone;
                        }
                        return self.standardizeToApiFields(res, 'gemini-output');
                    }
                    return res;
                } catch (e) {
                    console.warn('[GlobalFieldStandardization] 标准化结果失败（已忽略）', e);
                    return res;
                }
            };

            const interceptGemini = () => {
                attempts++;

                // 最新优先：兼容性适配器（对外暴露的统一入口）
                const adapter = window.OTA?.geminiService || window.geminiService || null;
                // 子层实现：直接调用API的调用器
                const caller = window.OTA?.geminiCaller || null;

                console.log(`[GlobalFieldStandardization] 尝试安装Gemini拦截器 (${attempts}/${maxAttempts})`, {
                    hasAdapter: !!adapter,
                    hasCaller: !!caller,
                    adapterHasParseOrder: adapter ? typeof adapter.parseOrder === 'function' : false,
                    adapterHasLegacyParse: adapter ? typeof adapter.parseOrdersFromText === 'function' : false,
                    callerHasParseAPIResponse: caller ? typeof caller.parseAPIResponse === 'function' : false
                });

                // 方案A：拦截适配器 parseOrder（首选路径）
                if (adapter && typeof adapter.parseOrder === 'function' && !adapter._fieldStandardizationHooked) {
                    const originalParseOrder = adapter.parseOrder.bind(adapter);
                    adapter.parseOrder = async function(text, isRealtime = false) {
                        console.log('[GlobalFieldStandardization] 🔧 拦截到Gemini调用 parseOrder');
                        const result = await originalParseOrder(text, isRealtime);
                        const processed = standardizeResult(result);
                        return processed;
                    };
                    adapter._fieldStandardizationHooked = true;
                    console.log('[GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrder)');
                    return; // 成功安装，结束轮询
                }

                // 方案B：兼容旧方法 parseOrdersFromText（保留原逻辑）
                if (adapter && typeof adapter.parseOrdersFromText === 'function' && !adapter._fieldStandardizationHookedLegacy) {
                    const originalParse = adapter.parseOrdersFromText.bind(adapter);
                    adapter.parseOrdersFromText = function(text, options = {}) {
                        console.log('[GlobalFieldStandardization] 🔧 拦截到Gemini调用 parseOrdersFromText');
                        return originalParse(text, options).then(standardizeResult);
                    };
                    adapter._fieldStandardizationHookedLegacy = true;
                    console.log('[GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrdersFromText)');
                    return;
                }

                // 方案C：拦截子层调用器的响应解析（兜底）
                if (caller && typeof caller.parseAPIResponse === 'function' && !caller._fieldStandardizationHooked) {
                    const originalParseAPIResponse = caller.parseAPIResponse.bind(caller);
                    caller.parseAPIResponse = function(data) {
                        const res = originalParseAPIResponse(data);
                        return standardizeResult(res);
                    };
                    caller._fieldStandardizationHooked = true;
                    console.log('[GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse)');
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(interceptGemini, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ Gemini服务拦截器安装超时，已跳过 - 检查服务注册状态');
                }
            };

            interceptGemini();
        }

        /**
         * 拦截API服务的数据输入
         */
        interceptApiService() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒

            const interceptApi = () => {
                attempts++;

                // 仅当真实实例已挂载到全局时才进行拦截安装
                const apiService = (window.OTA && window.OTA.apiService) || window.apiService || null;
                if (apiService && typeof apiService.createOrder === 'function') {
                    const originalCreateOrder = apiService.createOrder.bind(apiService);

                    apiService.createOrder = function(orderData) {
                        // 在发送到API之前标准化字段
                        const standardizedData = self.standardizeToApiFields(orderData, 'api-input');
                        return originalCreateOrder(standardizedData);
                    };

                    console.log('[GlobalFieldStandardization] ✅ API服务拦截器已安装');
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(interceptApi, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ API服务拦截器安装超时，跳过');
                }
            };

            interceptApi();
        }

        /**
         * 拦截多订单管理器
         */
        interceptMultiOrderManager() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒
            
            const interceptMultiOrder = () => {
                attempts++;
                
                if (window.OTA && window.OTA.multiOrderManager) {
                    const manager = window.OTA.multiOrderManager;
                    
                    // 拦截数据处理方法
                    if (manager.processOrderData) {
                        const originalProcess = manager.processOrderData.bind(manager);
                        manager.processOrderData = function(orderData) {
                            const standardizedData = self.standardizeToApiFields(orderData, 'multi-order');
                            return originalProcess(standardizedData);
                        };
                    }

                    // 拦截显示函数的数据输入，确保字段标准化后传入
                    if (manager.getVehicleTypeDisplay) {
                        const originalGetVehicleType = manager.getVehicleTypeDisplay.bind(manager);
                        manager.getVehicleTypeDisplay = function(order) {
                            // 标准化订单数据，确保包含正确的字段名
                            const standardizedOrder = self.standardizeToApiFields(order, 'vehicle-display');
                            return originalGetVehicleType(standardizedOrder);
                        };
                    }

                    if (manager.getDrivingRegionDisplay) {
                        const originalGetDrivingRegion = manager.getDrivingRegionDisplay.bind(manager);
                        manager.getDrivingRegionDisplay = function(order) {
                            // 标准化订单数据，确保包含正确的字段名
                            const standardizedOrder = self.standardizeToApiFields(order, 'region-display');
                            return originalGetDrivingRegion(standardizedOrder);
                        };
                    }
                    
                    console.log('[GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装');
                    return;
                }
                
                if (attempts < maxAttempts) {
                    setTimeout(interceptMultiOrder, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ 多订单管理器拦截器安装超时，跳过');
                }
            };
            
            interceptMultiOrder();
        }

        /**
         * 拦截表单管理器
         */
        interceptFormManager() {
            // 简化：先尝试依赖容器，如果不存在则跳过
            try {
                const container = window.OTA?.container;
                if (container && container.has('formManager')) {
                    const formManager = container.get('formManager');
                    if (formManager && formManager.getFormData && !formManager.isIntercepted) {
                        const originalGetFormData = formManager.getFormData.bind(formManager);
                        
                        formManager.getFormData = function() {
                            const formData = originalGetFormData();
                            return globalFieldStandardizationLayer.standardizeToApiFields(formData, 'form-data');
                        };
                        
                        formManager.isIntercepted = true;
                        console.log('[GlobalFieldStandardization] ✅ 表单管理器拦截器已安装');
                        return;
                    }
                }
                
                console.log('[GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截');
            } catch (error) {
                console.log('[GlobalFieldStandardization] ⚠️ 表单管理器拦截器安装失败:', error.message);
            }
        }

        /**
         * 🔧 减法修复：移除实时分析管理器拦截器
         *
         * 原因分析：
         * 1. 实时分析管理器已经通过Gemini服务使用标准化字段
         * 2. 拦截器查找路径与实际注册路径不匹配
         * 3. 不必要的复杂性导致初始化错误
         *
         * 解决方案：
         * - 移除拦截器机制
         * - 依赖Gemini服务的统一标准化处理
         * - 简化架构，减少故障点
         */
        interceptRealtimeAnalysisManager() {
            // 🔧 减法修复：不再拦截实时分析管理器
            // 实时分析管理器通过Gemini服务自动获得字段标准化支持
            console.log('[GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复）');
        }

        /**
         * 设置延迟重试机制
         * 🔧 减法修复：简化重试逻辑，只重试必要的拦截器
         */
        setupDelayedRetry() {
            // 30秒后再次尝试安装失败的拦截器
            setTimeout(() => {
                console.log('[GlobalFieldStandardization] 🔄 执行延迟重试拦截器安装...');

                // 重试Gemini服务拦截器
                if (!window.OTA?.geminiService?.isIntercepted) {
                    this.interceptGeminiService();
                }

                // 重试表单管理器拦截器（如果需要）
                const formManager = window.getFormManager?.() ||
                                  window.OTA?.formManager ||
                                  window.formManager;
                if (formManager && !formManager.isIntercepted) {
                    this.interceptFormManager();
                }

                // 🔧 减法修复：不再重试实时分析管理器拦截器
                // 实时分析管理器通过Gemini服务自动获得字段标准化支持
                console.log('[GlobalFieldStandardization] ✅ 延迟重试完成（已简化）');
            }, 30000);
        }

        /**
         * 将对象字段标准化为API格式
         * @param {Object} data - 原始数据对象
         * @param {String} context - 调用上下文（用于日志和缓存）
         * @returns {Object} 标准化后的数据对象
         */
        standardizeToApiFields(data, context = 'unknown') {
            if (!data || typeof data !== 'object') {
                return data;
            }

            // 生成缓存键
            const cacheKey = `${context}_${JSON.stringify(data)}`;
            if (this.cache.has(cacheKey)) {
                this.statistics.cacheHits++;
                return this.cache.get(cacheKey);
            }

            try {
                const standardized = {};
                let transformCount = 0;

                Object.keys(data).forEach(originalField => {
                    // 跳过可忽略的字段
                    if (this.IGNORABLE_FIELDS.has(originalField)) {
                        return;
                    }

                    let standardField = originalField;
                    
                    // 检查是否需要字段映射
                    if (this.UNIFIED_FIELD_MAPPING[originalField]) {
                        standardField = this.UNIFIED_FIELD_MAPPING[originalField];
                        transformCount++;
                        
                        // 记录字段转换（仅在调试模式）
                        if (window.location?.hostname === 'localhost' && originalField !== standardField) {
                            console.log(`[FieldStandardization][${context}] ${originalField} → ${standardField}`);
                        }
                    }

                    // 复制值到标准化字段
                    standardized[standardField] = data[originalField];
                });

                // 更新统计信息
                this.statistics.transformations += transformCount;

                // 缓存结果（限制缓存大小）
                if (this.cache.size < 1000) {
                    this.cache.set(cacheKey, standardized);
                }

                return standardized;

            } catch (error) {
                this.statistics.errors++;
                console.error(`[GlobalFieldStandardization] 字段标准化失败 [${context}]:`, error);
                return data; // 返回原始数据作为后备
            }
        }

        /**
         * 将对象字段标准化为前端格式
         * @param {Object} data - API格式数据对象
         * @param {String} context - 调用上下文
         * @returns {Object} 前端格式的数据对象
         */
        standardizeToFrontendFields(data, context = 'unknown') {
            if (!data || typeof data !== 'object') {
                return data;
            }

            try {
                const standardized = {};

                Object.keys(data).forEach(apiField => {
                    // 跳过可忽略的字段
                    if (this.IGNORABLE_FIELDS.has(apiField)) {
                        return;
                    }

                    let frontendField = apiField;
                    
                    // 检查API字段到前端字段的映射
                    if (this.API_TO_FRONTEND_MAPPING[apiField]) {
                        frontendField = this.API_TO_FRONTEND_MAPPING[apiField];
                    }

                    standardized[frontendField] = data[apiField];
                });

                return standardized;

            } catch (error) {
                this.statistics.errors++;
                console.error(`[GlobalFieldStandardization] 前端字段标准化失败 [${context}]:`, error);
                return data;
            }
        }

        /**
         * 验证字段是否符合API标准
         * @param {String} fieldName - 字段名
         * @returns {Object} 验证结果
         */
        validateFieldStandard(fieldName) {
            const isApiStandard = this.API_STANDARD_FIELDS.hasOwnProperty(fieldName);
            const hasMapping = this.UNIFIED_FIELD_MAPPING.hasOwnProperty(fieldName);
            const suggestedStandard = hasMapping ? this.UNIFIED_FIELD_MAPPING[fieldName] : fieldName;

            return {
                isApiStandard,
                hasMapping,
                fieldName,
                suggestedStandard,
                needsTransformation: fieldName !== suggestedStandard
            };
        }

        /**
         * 获取字段映射统计信息
         * @returns {Object} 统计信息
         */
        getStatistics() {
            return {
                ...this.statistics,
                cacheSize: this.cache.size,
                totalMappings: Object.keys(this.UNIFIED_FIELD_MAPPING).length,
                apiStandardFields: Object.keys(this.API_STANDARD_FIELDS).length
            };
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.cache.clear();
            console.log('[GlobalFieldStandardization] 缓存已清理');
        }

        /**
         * 生成字段映射报告
         * @returns {Object} 字段映射报告
         */
        generateMappingReport() {
            const report = {
                summary: {
                    totalTransformations: this.statistics.transformations,
                    cacheHits: this.statistics.cacheHits,
                    errors: this.statistics.errors,
                    cacheSize: this.cache.size
                },
                fieldMappings: {
                    total: Object.keys(this.UNIFIED_FIELD_MAPPING).length,
                    apiStandard: Object.keys(this.API_STANDARD_FIELDS).length,
                    ignorable: this.IGNORABLE_FIELDS.size
                },
                activeMappings: Object.entries(this.UNIFIED_FIELD_MAPPING)
                    .filter(([from, to]) => from !== to)
                    .reduce((acc, [from, to]) => {
                        acc[from] = to;
                        return acc;
                    }, {}),
                generatedAt: new Date().toISOString()
            };

            return report;
        }
    }

    // 创建全局实例并自动初始化
    const globalFieldStandardizationLayer = new GlobalFieldStandardizationLayer();

    // 注册到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.GlobalFieldStandardizationLayer = GlobalFieldStandardizationLayer;
    window.OTA.globalFieldStandardizationLayer = globalFieldStandardizationLayer;

    // 全局访问接口
    window.getGlobalFieldStandardizationLayer = () => globalFieldStandardizationLayer;

    // 提供便捷的全局方法
    window.standardizeFieldsToApi = (data, context) => 
        globalFieldStandardizationLayer.standardizeToApiFields(data, context);
    
    window.standardizeFieldsToFrontend = (data, context) => 
        globalFieldStandardizationLayer.standardizeToFrontendFields(data, context);

    // 受控启用/禁用
    window.enableGlobalFieldStandardization = () => globalFieldStandardizationLayer.initialize();
    window.disableGlobalFieldStandardization = () => {
        if (window.OTA?.featureToggle) {
            window.OTA.featureToggle.disable('enableGlobalFieldStandardization');
        }
        globalFieldStandardizationLayer.initialized = false;
        console.log('[GlobalFieldStandardization] 已禁用');
    };

    // 自动检查并初始化（如果特性开关已启用）
    const checkAndAutoInitialize = () => {
        const enabled = window.OTA?.featureToggle?.isEnabled('enableGlobalFieldStandardization');
        if (enabled && !globalFieldStandardizationLayer.initialized) {
            console.log('[GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化...');
            globalFieldStandardizationLayer.initialize();
        }
    };

    // 立即检查一次
    setTimeout(checkAndAutoInitialize, 100);

    // 减法修复：减少定期检查的频率和持续时间，避免过度重试
    const autoInitInterval = setInterval(() => {
        if (globalFieldStandardizationLayer.initialized) {
            clearInterval(autoInitInterval);
            return;
        }
        checkAndAutoInitialize();
    }, 1000); // 从500ms增加到1000ms

    // 减少检查时间，从5秒降到3秒
    setTimeout(() => {
        if (autoInitInterval) {
            clearInterval(autoInitInterval);
            console.log('[GlobalFieldStandardization] ℹ️ 自动初始化检查已停止');
        }
    }, 3000);

    console.log('[GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化');

})();