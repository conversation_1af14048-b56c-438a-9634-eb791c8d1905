<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA价格字段映射测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>OTA价格字段映射测试</h1>
    <p>验证从Gemini返回的ota_price字段到最终API的完整映射流程</p>

    <button onclick="runMappingTest()">运行映射测试</button>
    <div id="results"></div>

    <script>
        function runMappingTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>映射测试结果</h2>';

            // 模拟Gemini返回的数据（使用ota_price字段名）
            const geminiData = {
                customer_name: "张三",
                pickup: "机场",
                dropoff: "酒店", 
                ota_price: 150.50,
                currency: "MYR"
            };

            // 测试步骤1：数据规范化（将ota_price映射为otaPrice）
            console.log('🔍 步骤1：Gemini原始数据', geminiData);
            
            const normalizedData = normalizeGeminiData(geminiData);
            console.log('🔧 步骤2：规范化后数据', normalizedData);

            // 测试步骤2：表单字段映射（将otaPrice映射为ota_price）
            const formMapping = {
                'otaPrice': 'ota_price',
                'currency': 'currency'
            };

            const apiData = {};
            for (const [internalField, apiField] of Object.entries(formMapping)) {
                if (normalizedData[internalField] !== undefined) {
                    apiData[apiField] = normalizedData[internalField];
                }
            }
            console.log('📤 步骤3：最终API数据', apiData);

            // 验证映射结果
            const testResults = [];
            
            // 测试1：ota_price字段存在且值正确
            if (apiData.ota_price === 150.50) {
                testResults.push({
                    name: "ota_price字段映射",
                    status: "success",
                    message: `✅ 映射成功: ${geminiData.ota_price} -> ${normalizedData.otaPrice} -> ${apiData.ota_price}`
                });
            } else {
                testResults.push({
                    name: "ota_price字段映射", 
                    status: "error",
                    message: `❌ 映射失败: 期望 150.50, 实际 ${apiData.ota_price}`
                });
            }

            // 测试2：数据类型正确
            if (typeof apiData.ota_price === 'number') {
                testResults.push({
                    name: "价格数据类型",
                    status: "success", 
                    message: `✅ 数据类型正确: ${typeof apiData.ota_price}`
                });
            } else {
                testResults.push({
                    name: "价格数据类型",
                    status: "error",
                    message: `❌ 数据类型错误: 期望 number, 实际 ${typeof apiData.ota_price}`
                });
            }

            // 显示结果
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-case ${result.status}`;
                div.innerHTML = `<h3>${result.name}</h3><p>${result.message}</p>`;
                resultsDiv.appendChild(div);
            });

            // 显示完整的数据流
            const dataFlowDiv = document.createElement('div'); 
            dataFlowDiv.className = 'test-case';
            dataFlowDiv.innerHTML = `
                <h3>完整数据流</h3>
                <pre><strong>1. Gemini返回:</strong>
${JSON.stringify(geminiData, null, 2)}

<strong>2. 规范化处理:</strong>
${JSON.stringify(normalizedData, null, 2)}

<strong>3. API数据:</strong>
${JSON.stringify(apiData, null, 2)}</pre>
            `;
            resultsDiv.appendChild(dataFlowDiv);
        }

        // 模拟数据规范化函数
        function normalizeGeminiData(data) {
            const normalized = { ...data };
            
            // 字段名映射：ota_price -> otaPrice
            if (normalized.ota_price !== undefined) {
                normalized.otaPrice = parseFloat(normalized.ota_price) || null;
                delete normalized.ota_price;
            }
            
            return normalized;
        }
    </script>
</body>
</html>