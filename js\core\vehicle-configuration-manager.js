/**
 * 车辆配置管理器 - 根源级别配置管理
 * 🏷️ 标签: @OTA_VEHICLE_CONFIG_MANAGER
 * 📝 说明: 统一管理所有车型相关配置，消除硬编码，确保配置一致性
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 车辆配置管理器类 - 配置系统的根源
     */
    class VehicleConfigurationManager {
        constructor() {
            this.initialized = false;
            this.logger = null;
            this.subscribers = new Map(); // 配置变更订阅者
            
            // 🎯 根源配置 - 所有车型相关的核心配置
            this.config = {
                // 默认车型配置 - 系统的根源配置
                defaults: {
                    carTypeId: 5,           // 统一默认车型：5座标准车
                    carTypeName: '5 Seater',
                    passengerLimit: 3,
                    luggageLimit: 3,
                    description: '5 Seater (3 passenger, 3 x L size luggage)'
                },
                
                // 车型智能推荐规则 - 基于乘客数量的智能推荐
                recommendationRules: [
                    { minPassengers: 1, maxPassengers: 3, carTypeId: 5, name: '5 Seater' },
                    { minPassengers: 4, maxPassengers: 4, carTypeId: 37, name: 'Extended 5' },
                    { minPassengers: 5, maxPassengers: 5, carTypeId: 15, name: '7 Seater MPV' },
                    { minPassengers: 6, maxPassengers: 6, carTypeId: 32, name: 'Velfire/Alphard' },
                    { minPassengers: 7, maxPassengers: 7, carTypeId: 20, name: '10 Seater MPV' },
                    { minPassengers: 8, maxPassengers: 10, carTypeId: 23, name: '14 Seater Van' },
                    { minPassengers: 11, maxPassengers: 12, carTypeId: 24, name: '18 Seater Van' },
                    { minPassengers: 13, maxPassengers: 29, carTypeId: 25, name: '30 Seat Mini Bus' }
                ],
                
                // 特殊车型配置
                specialVehicles: {
                    ticket: { carTypeId: 34, name: 'Ticket' },
                    ticketNonMalaysian: { carTypeId: 39, name: 'Ticket (Non-Malaysian)' },
                    hatchback: { carTypeId: 38, name: '4 Seater Hatchback' }
                },
                
                // OTA渠道特定的车型偏好
                channelPreferences: {
                    'Ctrip': { 
                        preferLarger: true, 
                        minRecommendedId: 15, // 倾向7座MPV以上
                        description: 'Ctrip偏好较大车型' 
                    },
                    'Chong Dealer': { 
                        preferEconomical: true, 
                        maxRecommendedId: 5, // 倾向经济型
                        description: 'Chong Dealer偏好经济型车型' 
                    }
                }
            };

            this.initializeManager();
        }

        /**
         * 初始化管理器
         */
        initializeManager() {
            try {
                this.logger = this.getLogger();
                this.validateConfiguration();
                this.setupConfigurationChangeNotification();
                this.registerToContainer();
                
                this.initialized = true;
                this.log('车辆配置管理器已初始化', 'info', {
                    defaultCarTypeId: this.config.defaults.carTypeId,
                    recommendationRules: this.config.recommendationRules.length
                });
                
            } catch (error) {
                console.error('车辆配置管理器初始化失败:', error);
                throw error;
            }
        }

        /**
         * 获取默认车型ID - 系统的根源配置
         * @returns {number} 默认车型ID
         */
        getDefaultCarTypeId() {
            return this.config.defaults.carTypeId;
        }

        /**
         * 获取默认车型完整信息
         * @returns {Object} 默认车型信息
         */
        getDefaultCarType() {
            return { ...this.config.defaults };
        }

        /**
         * 智能推荐车型 - 基于乘客数量和渠道偏好
         * @param {number} passengerCount - 乘客数量
         * @param {string} otaChannel - OTA渠道 (可选)
         * @returns {Object} 推荐的车型信息
         */
        recommendCarType(passengerCount, otaChannel = null) {
            const passengers = parseInt(passengerCount) || 0;
            
            // 如果没有乘客数量信息，返回默认车型
            if (passengers <= 0) {
                this.log('无乘客数量信息，使用默认车型', 'debug', {
                    defaultCarTypeId: this.config.defaults.carTypeId
                });
                return this.getDefaultCarType();
            }

            // 查找基础推荐
            let recommendation = this.config.recommendationRules.find(rule => 
                passengers >= rule.minPassengers && passengers <= rule.maxPassengers
            );

            // 如果没找到匹配规则，使用最大车型
            if (!recommendation) {
                const lastRule = this.config.recommendationRules[this.config.recommendationRules.length - 1];
                recommendation = lastRule;
                this.log('乘客数量超出规则范围，使用最大车型', 'info', {
                    passengerCount: passengers,
                    recommendedCarTypeId: lastRule.carTypeId
                });
            }

            // 应用渠道偏好调整
            if (otaChannel && this.config.channelPreferences[otaChannel]) {
                recommendation = this.applyChannelPreference(recommendation, otaChannel, passengers);
            }

            this.log('车型推荐完成', 'debug', {
                passengerCount: passengers,
                otaChannel: otaChannel,
                recommendedCarTypeId: recommendation.carTypeId,
                recommendedName: recommendation.name
            });

            return {
                carTypeId: recommendation.carTypeId,
                carTypeName: recommendation.name,
                passengerLimit: recommendation.maxPassengers,
                description: `推荐给${passengers}位乘客的${recommendation.name}`
            };
        }

        /**
         * 应用渠道偏好调整
         * @param {Object} baseRecommendation - 基础推荐
         * @param {string} channel - 渠道名称
         * @param {number} passengerCount - 乘客数量
         * @returns {Object} 调整后的推荐
         */
        applyChannelPreference(baseRecommendation, channel, passengerCount) {
            const preference = this.config.channelPreferences[channel];
            let adjustedRecommendation = baseRecommendation;

            if (preference.preferLarger && passengerCount >= 3) {
                // 寻找更大的车型
                const largerOption = this.config.recommendationRules.find(rule => 
                    rule.carTypeId >= preference.minRecommendedId && 
                    rule.minPassengers <= passengerCount
                );
                if (largerOption) {
                    adjustedRecommendation = largerOption;
                }
            }

            if (preference.preferEconomical && passengerCount <= 4) {
                // 确保不超过经济型上限
                if (baseRecommendation.carTypeId > preference.maxRecommendedId) {
                    const economicalOption = this.config.recommendationRules.find(rule => 
                        rule.carTypeId <= preference.maxRecommendedId
                    );
                    if (economicalOption) {
                        adjustedRecommendation = economicalOption;
                    }
                }
            }

            if (adjustedRecommendation !== baseRecommendation) {
                this.log('应用渠道偏好调整', 'debug', {
                    channel: channel,
                    originalCarTypeId: baseRecommendation.carTypeId,
                    adjustedCarTypeId: adjustedRecommendation.carTypeId,
                    reason: preference.description
                });
            }

            return adjustedRecommendation;
        }

        /**
         * 获取特殊车型配置
         * @param {string} specialType - 特殊类型 (ticket, hatchback等)
         * @returns {Object|null} 特殊车型配置
         */
        getSpecialVehicle(specialType) {
            return this.config.specialVehicles[specialType] || null;
        }

        /**
         * 验证车型ID是否有效
         * @param {number} carTypeId - 车型ID
         * @returns {boolean} 是否有效
         */
        isValidCarTypeId(carTypeId) {
            const id = parseInt(carTypeId);
            if (!id || id <= 0) return false;

            // 检查是否在推荐规则中
            const inRecommendationRules = this.config.recommendationRules.some(rule => 
                rule.carTypeId === id
            );
            
            // 检查是否在特殊车型中
            const inSpecialVehicles = Object.values(this.config.specialVehicles).some(special => 
                special.carTypeId === id
            );

            return inRecommendationRules || inSpecialVehicles;
        }

        /**
         * 订阅配置变更通知
         * @param {Function} callback - 回调函数
         * @returns {string} 订阅ID
         */
        subscribe(callback) {
            const subscriptionId = `vehicle_config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            this.subscribers.set(subscriptionId, callback);
            return subscriptionId;
        }

        /**
         * 取消订阅
         * @param {string} subscriptionId - 订阅ID
         */
        unsubscribe(subscriptionId) {
            this.subscribers.delete(subscriptionId);
        }

        /**
         * 更新配置 (支持热更新)
         * @param {Object} newConfig - 新配置
         */
        updateConfiguration(newConfig) {
            const oldConfig = JSON.parse(JSON.stringify(this.config));
            this.config = { ...this.config, ...newConfig };
            
            this.validateConfiguration();
            this.notifyConfigurationChange(oldConfig, this.config);
            
            this.log('配置已更新', 'info', {
                changes: this.getConfigurationDiff(oldConfig, this.config)
            });
        }

        /**
         * 通知配置变更
         * @param {Object} oldConfig - 旧配置
         * @param {Object} newConfig - 新配置
         */
        notifyConfigurationChange(oldConfig, newConfig) {
            const changes = this.getConfigurationDiff(oldConfig, newConfig);
            
            this.subscribers.forEach((callback, subscriptionId) => {
                try {
                    callback(changes, newConfig);
                } catch (error) {
                    this.log(`配置变更通知失败 ${subscriptionId}: ${error.message}`, 'error');
                }
            });
        }

        /**
         * 获取配置差异
         * @param {Object} oldConfig - 旧配置
         * @param {Object} newConfig - 新配置
         * @returns {Array} 差异列表
         */
        getConfigurationDiff(oldConfig, newConfig) {
            const changes = [];
            
            if (oldConfig.defaults.carTypeId !== newConfig.defaults.carTypeId) {
                changes.push({
                    type: 'defaultCarType',
                    oldValue: oldConfig.defaults.carTypeId,
                    newValue: newConfig.defaults.carTypeId
                });
            }
            
            return changes;
        }

        /**
         * 验证配置完整性
         */
        validateConfiguration() {
            if (!this.config.defaults.carTypeId || this.config.defaults.carTypeId <= 0) {
                throw new Error('无效的默认车型ID');
            }
            
            if (!Array.isArray(this.config.recommendationRules) || this.config.recommendationRules.length === 0) {
                throw new Error('推荐规则不能为空');
            }
            
            // 验证推荐规则的完整性
            this.config.recommendationRules.forEach((rule, index) => {
                if (!rule.carTypeId || !rule.minPassengers || !rule.maxPassengers || !rule.name) {
                    throw new Error(`推荐规则 ${index} 不完整`);
                }
            });
        }

        /**
         * 设置配置变更通知
         */
        setupConfigurationChangeNotification() {
            // 监听全局配置变更事件
            if (window.OTA && window.OTA.globalEventCoordinator) {
                try {
                    window.OTA.globalEventCoordinator.registerComponent('vehicleConfigManager', this, {
                        type: 'configuration',
                        notifications: ['configurationChange']
                    });
                } catch (error) {
                    this.log('注册到全局事件协调器失败', 'warning', { error: error.message });
                }
            }
        }

        /**
         * 注册到依赖容器
         */
        registerToContainer() {
            if (window.OTA && window.OTA.container) {
                try {
                    window.OTA.container.register('vehicleConfigManager', () => this, {
                        singleton: true
                    });
                    this.log('已注册到依赖容器', 'debug');
                } catch (error) {
                    this.log('注册到依赖容器失败', 'warning', { error: error.message });
                }
            }
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                return window.getLogger ? window.getLogger() : console;
            } catch (e) {
                return console;
            }
        }

        /**
         * 日志输出
         * @param {string} message - 消息
         * @param {string} level - 级别
         * @param {Object} data - 数据
         */
        log(message, level = 'info', data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(`[VehicleConfigManager] ${message}`, level, data);
            } else {
                console.log(`[VehicleConfigManager] ${message}`, data);
            }
        }

        /**
         * 获取配置统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                initialized: this.initialized,
                defaultCarTypeId: this.config.defaults.carTypeId,
                recommendationRules: this.config.recommendationRules.length,
                specialVehicles: Object.keys(this.config.specialVehicles).length,
                channelPreferences: Object.keys(this.config.channelPreferences).length,
                subscribers: this.subscribers.size
            };
        }
    }

    // 创建全局单例实例
    const vehicleConfigManager = new VehicleConfigurationManager();

    // 导出到OTA命名空间
    window.OTA.VehicleConfigurationManager = VehicleConfigurationManager;
    window.OTA.vehicleConfigManager = vehicleConfigManager;

    // 向后兼容
    window.VehicleConfigurationManager = VehicleConfigurationManager;
    window.vehicleConfigManager = vehicleConfigManager;

    // 提供便捷的全局访问函数
    window.getVehicleConfigManager = () => vehicleConfigManager;

    console.log('✅ 车辆配置管理器已加载');

})();