# 文档整合说明（2025-08-09）

## 目标

- 精简文件数量，维持顶层（一级）目录简洁。
- 仅保留 memory-bank 作为文档与知识库的单一入口。

## 已执行的调整

- 原顶层目录 docs/ 已迁移并合并至：memory-bank/legacy-docs/
  - 原 docs/api-reference/ → memory-bank/legacy-docs/api-reference/
- 迁移完成后，顶层 docs/ 文件夹已删除。
- **新增整合**: 合并了多个重复的状态和进度文件到两个主要文件：
  - `project-overview.md` - 项目概览、当前状态、技术架构
  - `development-log.md` - 开发计划、修复总结、实施方案

## 新的文档结构

### 核心文档（主要入口）

- `project-overview.md` - 项目状态、技术架构、数据流全景
- `development-log.md` - 开发计划、修复记录、技术方案

### 详细文档

- `implementation-plans/` - 详细的实施计划和开发文档
- `legacy-docs/` - 原docs目录的完整历史文档

## 影响与兼容性

- 若有脚本、文档或链接仍引用 docs/... 路径，请更新为 memory-bank/legacy-docs/...
  - 例如：docs/xxx.md → memory-bank/legacy-docs/xxx.md
- 部分脚本可能包含对 docs 的可选复制逻辑（如部署脚本的条件复制）。在未更新前不会报错，但也不会再复制文档。如需继续发布文档，请将路径改为 memory-bank/legacy-docs。

## 快速检查（可选）

- 在项目根目录搜索引用：
  - 关键词："docs/"，检查并替换为 "memory-bank/legacy-docs/"（根据实际需要决定是否替换）。

## 当前文档入口

- 主要知识库与存档：memory-bank/
- 历史/迁移后的项目文档：memory-bank/legacy-docs/

## 备注

- 本次调整仅为路径整合，不改变文档内容。 
