<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单管理器修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .editable-field {
            display: inline-block;
            padding: 5px 10px;
            margin: 2px;
            background: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .editable-field:hover {
            background: #dee2e6;
            border-color: #007bff;
        }
        .editable-field.editing {
            background: #fff;
            border-color: #007bff;
        }
        .grid-value {
            display: inline-block;
        }
        .field-editor {
            width: 100%;
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 4px 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>多订单管理器修复测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">🔧 测试1：editField 方法功能测试</h2>
        <div id="editFieldTest">
            <p>测试 editField 方法是否正确实现...</p>
            <button onclick="testEditFieldMethod()">测试 editField 方法</button>
            <div id="editFieldResult"></div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔄 测试2：下拉菜单选项同步测试</h2>
        <div id="dropdownSyncTest">
            <p>测试下拉菜单选项是否与主界面同步...</p>
            <button onclick="testDropdownSync()">测试下拉菜单同步</button>
            <div id="dropdownSyncResult"></div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 测试3：模拟订单编辑测试</h2>
        <div id="orderEditTest">
            <p>创建模拟订单卡片，测试字段编辑功能...</p>
            <button onclick="createMockOrderCard()">创建模拟订单</button>
            <button onclick="testFieldEditing()">测试字段编辑</button>
            <div id="mockOrderContainer"></div>
            <div id="orderEditResult"></div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 测试日志</h2>
        <div id="testLog" class="log-area"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/multi-order-manager-v2.js"></script>

    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // 测试1：editField 方法功能测试
        function testEditFieldMethod() {
            log('开始测试 editField 方法...');
            
            try {
                // 检查 MultiOrderManagerV2 是否存在
                if (!window.OTA || !window.OTA.multiOrderManager) {
                    throw new Error('MultiOrderManagerV2 未加载');
                }

                const manager = window.OTA.multiOrderManager;
                
                // 检查 editField 方法是否存在
                if (typeof manager.editField !== 'function') {
                    throw new Error('editField 方法不存在');
                }

                log('✅ editField 方法存在');
                showResult('editFieldResult', '✅ editField 方法测试通过 - 方法已正确实现', 'success');
                
            } catch (error) {
                log(`❌ editField 方法测试失败: ${error.message}`, 'error');
                showResult('editFieldResult', `❌ editField 方法测试失败: ${error.message}`, 'error');
            }
        }

        // 测试2：下拉菜单选项同步测试
        function testDropdownSync() {
            log('开始测试下拉菜单选项同步...');
            
            try {
                const manager = window.OTA.multiOrderManager;
                if (!manager) {
                    throw new Error('多订单管理器未加载');
                }

                // 创建测试订单数据
                const testOrder = {
                    _otaChannel: 'agoda',
                    vehicle_type: '5 Seater',
                    driving_region: '市区'
                };

                // 测试显示方法
                const otaDisplay = manager.getOtaChannelDisplay(testOrder);
                const vehicleDisplay = manager.getVehicleTypeDisplay(testOrder);
                const regionDisplay = manager.getDrivingRegionDisplay(testOrder);

                log(`OTA显示: ${otaDisplay}`);
                log(`车型显示: ${vehicleDisplay}`);
                log(`区域显示: ${regionDisplay}`);

                let results = [];
                results.push(`OTA渠道显示: ${otaDisplay}`);
                results.push(`车型显示: ${vehicleDisplay}`);
                results.push(`驾驶区域显示: ${regionDisplay}`);

                showResult('dropdownSyncResult', 
                    '✅ 下拉菜单同步测试完成<br>' + results.join('<br>'), 
                    'success');
                
            } catch (error) {
                log(`❌ 下拉菜单同步测试失败: ${error.message}`, 'error');
                showResult('dropdownSyncResult', `❌ 下拉菜单同步测试失败: ${error.message}`, 'error');
            }
        }

        // 测试3：创建模拟订单卡片
        function createMockOrderCard() {
            log('创建模拟订单卡片...');
            
            const container = document.getElementById('mockOrderContainer');
            container.innerHTML = `
                <div class="order-card" data-index="0">
                    <h4>测试订单 1</h4>
                    <div class="editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(0, 'customerName')">
                        <span class="grid-value">张三</span>
                    </div>
                    <div class="editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(0, 'customerContact')">
                        <span class="grid-value">12345678901</span>
                    </div>
                    <div class="editable-field" data-field="otaChannel" onclick="window.OTA.multiOrderManager.editField(0, 'otaChannel')">
                        <span class="grid-value">Agoda</span>
                    </div>
                    <div class="editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(0, 'vehicleType')">
                        <span class="grid-value">5 Seater</span>
                    </div>
                    <div class="editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(0, 'price')">
                        <span class="grid-value">150</span>
                    </div>
                </div>
            `;

            // 模拟订单数据
            if (window.OTA && window.OTA.multiOrderManager) {
                window.OTA.multiOrderManager.state = window.OTA.multiOrderManager.state || {};
                window.OTA.multiOrderManager.state.parsedOrders = [{
                    customer_name: '张三',
                    customer_contact: '12345678901',
                    _otaChannel: 'agoda',
                    vehicle_type: '5 Seater',
                    ota_price: 150
                }];
            }

            log('✅ 模拟订单卡片已创建');
            showResult('orderEditResult', '✅ 模拟订单卡片已创建，点击字段进行编辑测试', 'success');
        }

        // 测试字段编辑功能
        function testFieldEditing() {
            log('测试字段编辑功能...');
            
            try {
                const editableFields = document.querySelectorAll('.editable-field');
                if (editableFields.length === 0) {
                    throw new Error('没有找到可编辑字段，请先创建模拟订单');
                }

                log(`找到 ${editableFields.length} 个可编辑字段`);
                
                // 模拟点击第一个字段
                const firstField = editableFields[0];
                const fieldName = firstField.getAttribute('data-field');
                
                log(`模拟点击字段: ${fieldName}`);
                
                // 触发点击事件
                if (window.OTA && window.OTA.multiOrderManager && typeof window.OTA.multiOrderManager.editField === 'function') {
                    window.OTA.multiOrderManager.editField(0, fieldName);
                    log('✅ editField 方法调用成功');
                    showResult('orderEditResult', '✅ 字段编辑功能测试通过 - 可以点击字段进行编辑', 'success');
                } else {
                    throw new Error('editField 方法不可用');
                }
                
            } catch (error) {
                log(`❌ 字段编辑测试失败: ${error.message}`, 'error');
                showResult('orderEditResult', `❌ 字段编辑测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('测试页面加载完成');
            log('开始初始化测试环境...');
            
            // 初始化必要的全局对象
            if (!window.OTA) {
                window.OTA = {};
            }
            
            // 等待脚本加载
            setTimeout(() => {
                if (window.OTA.multiOrderManager) {
                    log('✅ 多订单管理器已加载');
                } else {
                    log('⚠️ 多订单管理器未加载，某些测试可能失败', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
