# 方案三：微服务化架构

## 🎯 核心理念

将每个OTA渠道作为独立的微服务模块，通过消息总线进行通信，实现物理级别的完全隔离。每个渠道运行在独立的JavaScript Worker或iframe中，确保零污染。

## 📁 文件结构

```
js/ota-microservices/
├── core/
│   ├── ota-service-manager.js       # 服务管理器
│   ├── ota-message-bus.js           # 消息总线
│   ├── ota-service-registry.js      # 服务注册中心
│   └── ota-health-monitor.js        # 健康监控器
├── gateway/
│   ├── ota-api-gateway.js           # API网关
│   ├── ota-load-balancer.js         # 负载均衡器
│   └── ota-request-router.js        # 请求路由器
├── services/
│   ├── fliggy-service/
│   │   ├── fliggy-worker.js         # 飞猪服务Worker
│   │   ├── fliggy-config.json       # 飞猪配置文件
│   │   ├── fliggy-detector.js       # 飞猪检测器
│   │   └── fliggy-processor.js      # 飞猪处理器
│   ├── ctrip-service/
│   │   ├── ctrip-worker.js          # 携程服务Worker
│   │   ├── ctrip-config.json        # 携程配置文件
│   │   └── ctrip-processor.js       # 携程处理器
│   └── klook-service/
│       ├── klook-worker.js          # Klook服务Worker
│       └── klook-processor.js       # Klook处理器
└── shared/
    ├── common-types.js              # 通用类型定义
    ├── message-protocols.js         # 消息协议
    └── error-handlers.js            # 错误处理器
```

## 🏗️ 架构设计

### 服务管理器
```javascript
// js/ota-microservices/core/ota-service-manager.js
class OTAServiceManager {
    constructor() {
        this.services = new Map();
        this.messageBus = new OTAMessageBus();
        this.registry = new OTAServiceRegistry();
        this.healthMonitor = new OTAHealthMonitor();
        this.isolationLevel = 'PHYSICAL'; // 物理隔离
    }
    
    // 启动OTA微服务
    async startService(channelName, config = {}) {
        if (this.services.has(channelName)) {
            throw new Error(`服务 ${channelName} 已经在运行中`);
        }
        
        // 创建独立的Worker环境
        const serviceWorker = new Worker(`js/ota-microservices/services/${channelName.toLowerCase()}-service/${channelName.toLowerCase()}-worker.js`);
        
        // 配置服务
        const serviceConfig = {
            channelName,
            isolationId: this._generateIsolationId(),
            startTime: Date.now(),
            ...config
        };
        
        // 建立消息通信
        const serviceInstance = {
            worker: serviceWorker,
            config: serviceConfig,
            status: 'STARTING',
            lastHeartbeat: Date.now(),
            requestQueue: [],
            responseHandlers: new Map()
        };
        
        // 设置消息处理
        serviceWorker.onmessage = (event) => {
            this._handleServiceMessage(channelName, event.data);
        };
        
        serviceWorker.onerror = (error) => {
            this._handleServiceError(channelName, error);
        };
        
        // 注册服务
        this.services.set(channelName, serviceInstance);
        await this.registry.registerService(channelName, serviceConfig);
        
        // 发送初始化消息
        await this._sendServiceMessage(channelName, {
            type: 'INIT',
            config: serviceConfig
        });
        
        // 等待服务就绪
        await this._waitForServiceReady(channelName);
        
        console.log(`🚀 微服务已启动: ${channelName}`);
        return serviceInstance;
    }
    
    // 停止服务
    async stopService(channelName) {
        const service = this.services.get(channelName);
        if (!service) {
            throw new Error(`服务 ${channelName} 不存在`);
        }
        
        // 发送停止信号
        await this._sendServiceMessage(channelName, { type: 'SHUTDOWN' });
        
        // 终止Worker
        service.worker.terminate();
        
        // 清理资源
        this.services.delete(channelName);
        await this.registry.unregisterService(channelName);
        
        console.log(`🔽 微服务已停止: ${channelName}`);
    }
    
    // 处理OTA请求（路由到对应服务）
    async processOTARequest(channelName, requestData) {
        const service = this.services.get(channelName);
        if (!service) {
            throw new Error(`OTA服务 ${channelName} 未启动`);
        }
        
        if (service.status !== 'READY') {
            throw new Error(`OTA服务 ${channelName} 状态异常: ${service.status}`);
        }
        
        // 生成请求ID
        const requestId = this._generateRequestId();
        
        // 发送处理请求
        const response = await this._sendServiceRequest(channelName, {
            type: 'PROCESS_ORDER',
            requestId,
            data: requestData,
            timestamp: Date.now()
        });
        
        return response;
    }
    
    // 检测OTA渠道（并行检测所有服务）
    async detectOTAChannel(orderData) {
        const activeServices = Array.from(this.services.keys()).filter(
            channelName => this.services.get(channelName).status === 'READY'
        );
        
        if (activeServices.length === 0) {
            throw new Error('没有可用的OTA检测服务');
        }
        
        // 并行向所有服务发送检测请求
        const detectionPromises = activeServices.map(async (channelName) => {
            try {
                const response = await this._sendServiceRequest(channelName, {
                    type: 'DETECT_CHANNEL',
                    data: orderData,
                    timestamp: Date.now()
                });
                
                return {
                    channel: channelName,
                    ...response
                };
            } catch (error) {
                console.warn(`${channelName} 检测失败:`, error);
                return {
                    channel: channelName,
                    confidence: 0,
                    matched: false,
                    error: error.message
                };
            }
        });
        
        const detectionResults = await Promise.all(detectionPromises);
        
        // 找到最佳匹配
        const bestMatch = detectionResults
            .filter(result => result.matched && !result.error)
            .sort((a, b) => b.confidence - a.confidence)[0];
        
        return {
            detected: !!bestMatch,
            bestMatch: bestMatch || null,
            allResults: detectionResults
        };
    }
    
    // 获取服务状态
    getServiceStatus() {
        const status = {};
        for (const [channelName, service] of this.services) {
            status[channelName] = {
                status: service.status,
                uptime: Date.now() - service.config.startTime,
                lastHeartbeat: service.lastHeartbeat,
                queueSize: service.requestQueue.length,
                isolationId: service.config.isolationId
            };
        }
        return status;
    }
    
    // 私有方法
    _generateIsolationId() {
        return `isolation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    _generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    async _sendServiceMessage(channelName, message) {
        const service = this.services.get(channelName);
        if (!service) {
            throw new Error(`服务 ${channelName} 不存在`);
        }
        
        service.worker.postMessage(message);
    }
    
    async _sendServiceRequest(channelName, request) {
        return new Promise((resolve, reject) => {
            const service = this.services.get(channelName);
            if (!service) {
                reject(new Error(`服务 ${channelName} 不存在`));
                return;
            }
            
            // 设置响应处理器
            service.responseHandlers.set(request.requestId, { resolve, reject });
            
            // 发送请求
            service.worker.postMessage(request);
            
            // 设置超时
            setTimeout(() => {
                if (service.responseHandlers.has(request.requestId)) {
                    service.responseHandlers.delete(request.requestId);
                    reject(new Error(`请求超时: ${channelName} - ${request.requestId}`));
                }
            }, 30000); // 30秒超时
        });
    }
    
    _handleServiceMessage(channelName, message) {
        const service = this.services.get(channelName);
        if (!service) return;
        
        switch (message.type) {
            case 'READY':
                service.status = 'READY';
                console.log(`✅ 服务就绪: ${channelName}`);
                break;
                
            case 'HEARTBEAT':
                service.lastHeartbeat = Date.now();
                break;
                
            case 'RESPONSE':
                this._handleServiceResponse(channelName, message);
                break;
                
            case 'ERROR':
                this._handleServiceError(channelName, message.error);
                break;
                
            default:
                console.warn(`未知消息类型: ${message.type} from ${channelName}`);
        }
    }
    
    _handleServiceResponse(channelName, message) {
        const service = this.services.get(channelName);
        if (!service) return;
        
        const handler = service.responseHandlers.get(message.requestId);
        if (handler) {
            service.responseHandlers.delete(message.requestId);
            
            if (message.success) {
                handler.resolve(message.data);
            } else {
                handler.reject(new Error(message.error || '服务处理失败'));
            }
        }
    }
    
    _handleServiceError(channelName, error) {
        console.error(`服务错误 ${channelName}:`, error);
        
        const service = this.services.get(channelName);
        if (service) {
            service.status = 'ERROR';
            
            // 拒绝所有等待的请求
            for (const handler of service.responseHandlers.values()) {
                handler.reject(new Error(`服务异常: ${error.message || error}`));
            }
            service.responseHandlers.clear();
        }
    }
    
    async _waitForServiceReady(channelName, timeout = 10000) {
        const service = this.services.get(channelName);
        if (!service) {
            throw new Error(`服务 ${channelName} 不存在`);
        }
        
        const startTime = Date.now();
        while (service.status !== 'READY' && (Date.now() - startTime) < timeout) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        if (service.status !== 'READY') {
            throw new Error(`服务 ${channelName} 启动超时`);
        }
    }
}
```

### 飞猪微服务Worker
```javascript
// js/ota-microservices/services/fliggy-service/fliggy-worker.js
// 🔒 这是一个完全隔离的飞猪处理环境

// 飞猪专属全局环境
const FLIGGY_NAMESPACE = {
    channelName: 'Fliggy',
    isolationId: null,
    config: null,
    processor: null,
    detector: null
};

// 飞猪专属检测器
class FliggyDetector {
    constructor() {
        this.patterns = [
            {
                type: 'orderNumber',
                pattern: /订单编号\d{19}/,
                confidence: 0.95,
                exclusive: true
            },
            {
                type: 'keyword',
                keywords: ['fliggy', '飞猪', '阿里旅行', '淘宝旅行'],
                confidence: 0.9,
                exclusive: true
            },
            {
                type: 'emailDomain',
                pattern: /@(fliggy|alibaba|taobao)\.com/i,
                confidence: 0.85,
                exclusive: true
            }
        ];
    }
    
    async detect(orderData) {
        let totalConfidence = 0;
        const matchedPatterns = [];
        
        for (const pattern of this.patterns) {
            const match = await this._testPattern(pattern, orderData);
            if (match.matched) {
                totalConfidence += pattern.confidence * match.strength;
                matchedPatterns.push(match);
            }
        }
        
        return {
            channel: 'Fliggy',
            confidence: Math.min(totalConfidence, 1.0),
            matched: totalConfidence > 0.7,
            patterns: matchedPatterns,
            exclusive: true,
            isolationId: FLIGGY_NAMESPACE.isolationId
        };
    }
    
    async _testPattern(pattern, orderData) {
        const rawText = orderData.raw_text || '';
        
        switch (pattern.type) {
            case 'orderNumber':
                const orderMatch = pattern.pattern.test(rawText);
                return { 
                    matched: orderMatch, 
                    strength: orderMatch ? 1.0 : 0.0, 
                    pattern: pattern.type 
                };
                
            case 'keyword':
                const text = rawText.toLowerCase();
                const keywordMatch = pattern.keywords.some(keyword => text.includes(keyword));
                return { 
                    matched: keywordMatch, 
                    strength: keywordMatch ? 1.0 : 0.0, 
                    pattern: pattern.type 
                };
                
            case 'emailDomain':
                const email = orderData.customer_email || '';
                const emailMatch = pattern.pattern.test(email);
                return { 
                    matched: emailMatch, 
                    strength: emailMatch ? 1.0 : 0.0, 
                    pattern: pattern.type 
                };
                
            default:
                return { matched: false, strength: 0.0, pattern: 'unknown' };
        }
    }
}

// 飞猪专属处理器
class FliggyProcessor {
    constructor() {
        this.exclusiveFeatures = new Set([
            'chineseAddressTranslation',
            'alipayIntegration', 
            'chineseCustomerService'
        ]);
    }
    
    async process(orderData) {
        const processedData = { ...orderData };
        
        // 🔒 飞猪专属：中文地址翻译
        if (processedData.pickup_address) {
            processedData.pickup_address = await this._translateChineseAddress(
                processedData.pickup_address
            );
        }
        
        // 🔒 飞猪专属：客户姓名标准化
        if (processedData.customer_name) {
            processedData.customer_name = this._standardizeChineseName(
                processedData.customer_name
            );
        }
        
        // 🔒 飞猪专属：价格计算
        if (processedData.base_price) {
            processedData.pricing = await this._calculateFliggyPrice(
                processedData.base_price,
                processedData
            );
        }
        
        // 添加处理标记
        processedData._processedBy = 'FliggyMicroservice';
        processedData._isolationId = FLIGGY_NAMESPACE.isolationId;
        processedData._timestamp = Date.now();
        
        return processedData;
    }
    
    // 🔒 飞猪独有功能
    async _translateChineseAddress(address) {
        // 检查是否包含中文
        if (!/[\u4e00-\u9fa5]/.test(address)) {
            return address;
        }
        
        // 飞猪专属地址翻译逻辑
        const translations = {
            '新山万丽酒店': 'Renaissance Johor Bahru Hotel',
            '吉隆坡国际机场': 'Kuala Lumpur International Airport (KLIA)',
            '云顶高原': 'Genting Highlands',
            '马六甲': 'Malacca',
            '槟城': 'Penang'
        };
        
        let translatedAddress = address;
        for (const [chinese, english] of Object.entries(translations)) {
            if (address.includes(chinese)) {
                translatedAddress = translatedAddress.replace(chinese, english);
            }
        }
        
        return translatedAddress;
    }
    
    _standardizeChineseName(name) {
        // 中文姓名标准化
        return name.trim().replace(/\s+/g, '');
    }
    
    async _calculateFliggyPrice(basePrice, orderData) {
        let finalPrice = basePrice;
        const adjustments = [];
        
        // 飞猪VIP客户折扣
        if (orderData.customer_level === 'VIP') {
            finalPrice *= 0.95;
            adjustments.push({ type: 'vip_discount', rate: 0.05 });
        }
        
        // 支付宝支付优惠
        if (orderData.payment_method === 'alipay') {
            finalPrice *= 0.98;
            adjustments.push({ type: 'alipay_discount', rate: 0.02 });
        }
        
        return {
            originalPrice: basePrice,
            finalPrice: Math.round(finalPrice * 100) / 100,
            currency: 'CNY',
            adjustments
        };
    }
}

// Worker消息处理
self.onmessage = async function(event) {
    const message = event.data;
    
    try {
        switch (message.type) {
            case 'INIT':
                await handleInit(message.config);
                break;
                
            case 'DETECT_CHANNEL':
                await handleDetection(message);
                break;
                
            case 'PROCESS_ORDER':
                await handleProcessing(message);
                break;
                
            case 'SHUTDOWN':
                await handleShutdown();
                break;
                
            default:
                throw new Error(`未知消息类型: ${message.type}`);
        }
    } catch (error) {
        self.postMessage({
            type: 'ERROR',
            requestId: message.requestId,
            error: error.message
        });
    }
};

async function handleInit(config) {
    FLIGGY_NAMESPACE.config = config;
    FLIGGY_NAMESPACE.isolationId = config.isolationId;
    FLIGGY_NAMESPACE.detector = new FliggyDetector();
    FLIGGY_NAMESPACE.processor = new FliggyProcessor();
    
    // 发送就绪信号
    self.postMessage({ type: 'READY' });
    
    // 启动心跳
    setInterval(() => {
        self.postMessage({ type: 'HEARTBEAT' });
    }, 10000);
}

async function handleDetection(message) {
    const result = await FLIGGY_NAMESPACE.detector.detect(message.data);
    
    self.postMessage({
        type: 'RESPONSE',
        requestId: message.requestId,
        success: true,
        data: result
    });
}

async function handleProcessing(message) {
    const result = await FLIGGY_NAMESPACE.processor.process(message.data);
    
    self.postMessage({
        type: 'RESPONSE',
        requestId: message.requestId,
        success: true,
        data: result
    });
}

async function handleShutdown() {
    // 清理资源
    FLIGGY_NAMESPACE.config = null;
    FLIGGY_NAMESPACE.detector = null;
    FLIGGY_NAMESPACE.processor = null;
    
    self.postMessage({ type: 'SHUTDOWN_COMPLETE' });
    self.close();
}
```

### API网关
```javascript
// js/ota-microservices/gateway/ota-api-gateway.js
class OTAAPIGateway {
    constructor() {
        this.serviceManager = new OTAServiceManager();
        this.requestRouter = new OTARequestRouter();
        this.loadBalancer = new OTALoadBalancer();
    }
    
    // 统一API入口
    async processOTAOrder(orderData, options = {}) {
        try {
            // 1. 检测OTA渠道
            const detection = await this.serviceManager.detectOTAChannel(orderData);
            
            if (!detection.detected) {
                throw new Error('无法识别OTA渠道');
            }
            
            const targetChannel = detection.bestMatch.channel;
            console.log(`🎯 路由到: ${targetChannel}`);
            
            // 2. 路由到对应微服务
            const result = await this.serviceManager.processOTARequest(
                targetChannel, 
                orderData
            );
            
            return {
                success: true,
                channel: targetChannel,
                detection: detection.bestMatch,
                result: result,
                processedAt: Date.now()
            };
            
        } catch (error) {
            console.error('OTA处理失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
    
    // 启动所有OTA服务
    async startAllServices() {
        const channels = ['Fliggy', 'Ctrip', 'Klook', 'ChongDealer', 'Kkday'];
        
        for (const channel of channels) {
            try {
                await this.serviceManager.startService(channel);
                console.log(`✅ ${channel} 微服务已启动`);
            } catch (error) {
                console.error(`❌ ${channel} 微服务启动失败:`, error);
            }
        }
    }
    
    // 停止所有服务
    async stopAllServices() {
        const activeServices = Object.keys(this.serviceManager.getServiceStatus());
        
        for (const channel of activeServices) {
            try {
                await this.serviceManager.stopService(channel);
                console.log(`🔽 ${channel} 微服务已停止`);
            } catch (error) {
                console.error(`❌ ${channel} 微服务停止失败:`, error);
            }
        }
    }
    
    // 获取系统状态
    getSystemStatus() {
        return {
            services: this.serviceManager.getServiceStatus(),
            timestamp: Date.now(),
            isolationLevel: 'PHYSICAL'
        };
    }
}
```

## ⚖️ 优势

- ✅ **物理隔离**: Worker环境确保完全的内存隔离
- ✅ **零污染**: 各服务运行在独立环境，无法相互影响
- ✅ **高可用**: 单个服务崩溃不影响其他服务
- ✅ **可伸缩**: 可以独立扩容高负载的服务
- ✅ **安全性**: 最高级别的隔离安全

## ⚠️ 劣势

- ❌ **复杂度极高**: 需要深入理解Worker和消息传递
- ❌ **性能开销**: Worker创建和消息传递有性能成本
- ❌ **调试困难**: 分布式环境增加调试复杂度
- ❌ **内存占用**: 每个服务独立运行占用更多内存

## 🚀 实施步骤

1. **构建基础框架**: 服务管理器、消息总线、API网关
2. **实现飞猪微服务**: 完全独立的飞猪Worker服务
3. **添加服务监控**: 健康检查、性能监控、错误处理
4. **迁移其他渠道**: 逐步将其他渠道改造为微服务
5. **替换主应用**: 更新主应用使用API网关
6. **性能优化**: 优化消息传递和资源利用

## 🔒 隔离保证

- **进程隔离**: 每个服务运行在独立的Worker进程中
- **内存隔离**: 无法访问其他服务的内存空间
- **消息隔离**: 只能通过定义的消息协议通信
- **错误隔离**: 单个服务的错误不会传播到其他服务

这种方案提供最高级别的隔离保证，适合对安全性和稳定性要求极高的场景。
