<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini AI解析功能优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Gemini AI解析功能优化测试</h1>
    
    <div class="test-container">
        <h2>测试控制</h2>
        <button onclick="testCurrencyRecognition()">测试货币识别增强</button>
        <button onclick="testTimeValidation()">测试时间验证功能</button>
        <button onclick="testPriceExtraction()">测试价格货币统一提取</button>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="testResults">点击按钮开始测试...</div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/hotel-data-inline.js"></script>
    <script src="js/gemini-service.js"></script>

    <script>
        let testResults = document.getElementById('testResults');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            testResults.appendChild(div);
            testResults.scrollTop = testResults.scrollHeight;
        }

        function clearResults() {
            testResults.innerHTML = '测试结果已清空';
        }

        // 等待GeminiService加载
        function waitForGeminiService() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50;
                
                const checkService = () => {
                    attempts++;
                    const geminiService = window.OTA?.geminiService || window.geminiService;
                    
                    if (geminiService) {
                        resolve(geminiService);
                    } else if (attempts >= maxAttempts) {
                        reject(new Error('GeminiService加载超时'));
                    } else {
                        setTimeout(checkService, 100);
                    }
                };
                
                checkService();
            });
        }

        async function testCurrencyRecognition() {
            log('💰 开始测试货币识别增强...', 'info');
            
            try {
                const geminiService = await waitForGeminiService();
                
                const testTexts = [
                    'RM150马币',
                    'S$200新币', 
                    '$100美金',
                    '¥300人民币',
                    '￥400元',
                    'RMB500',
                    '支付时间: 2025-01-24 14:30, 费用: RM180'
                ];
                
                testTexts.forEach(text => {
                    try {
                        const currency = geminiService.extractCurrency(text);
                        log(`"${text}" → 货币: ${currency}`, 'success');
                    } catch (error) {
                        log(`货币识别失败: ${text} - ${error.message}`, 'error');
                    }
                });
                
                log('货币识别测试完成', 'success');
                
            } catch (error) {
                log(`货币识别测试失败: ${error.message}`, 'error');
            }
        }

        async function testTimeValidation() {
            log('⏰ 开始测试时间验证功能...', 'info');
            
            try {
                const geminiService = await waitForGeminiService();
                
                const timeTests = [
                    { context: '接送时间: 14:30', time: '14:30' },
                    { context: '支付时间: 14:30', time: '14:30' },
                    { context: '用车时间: 09:00', time: '09:00' },
                    { context: 'payment time: 16:45', time: '16:45' },
                    { context: 'pickup time: 08:30', time: '08:30' }
                ];
                
                timeTests.forEach(test => {
                    try {
                        const isValid = geminiService.isValidServiceTime(test.context, test.time);
                        const status = isValid ? 'success' : 'warning';
                        log(`"${test.context}" → 有效用车时间: ${isValid}`, status);
                    } catch (error) {
                        log(`时间验证失败: ${test.context} - ${error.message}`, 'error');
                    }
                });
                
                log('时间验证测试完成', 'success');
                
            } catch (error) {
                log(`时间验证测试失败: ${error.message}`, 'error');
            }
        }

        async function testPriceExtraction() {
            log('💵 开始测试价格货币统一提取...', 'info');
            
            try {
                const geminiService = await waitForGeminiService();
                
                const priceTests = [
                    '费用: RM150',
                    '价格: S$200',
                    '金额: $100美金',
                    '总价: ¥300人民币',
                    '支付: 400 RMB'
                ];
                
                priceTests.forEach(text => {
                    try {
                        const result = geminiService.extractPriceAndCurrency(text);
                        log(`"${text}" → 价格: ${result.price}, 货币: ${result.currency}`, 'success');
                    } catch (error) {
                        log(`价格提取失败: ${text} - ${error.message}`, 'error');
                    }
                });
                
                log('价格货币提取测试完成', 'success');
                
            } catch (error) {
                log(`价格货币提取测试失败: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            log('🚀 开始运行所有测试...', 'info');
            clearResults();
            
            await testCurrencyRecognition();
            await testTimeValidation();
            await testPriceExtraction();
            
            log('✅ 所有测试完成', 'success');
        }

        // 页面加载时显示状态
        window.addEventListener('load', () => {
            log('页面加载完成，等待GeminiService初始化...', 'info');
            
            waitForGeminiService()
                .then(() => {
                    log('✅ GeminiService已就绪，可以开始测试', 'success');
                })
                .catch(error => {
                    log(`❌ GeminiService加载失败: ${error.message}`, 'error');
                });
        });
    </script>
</body>
</html>
