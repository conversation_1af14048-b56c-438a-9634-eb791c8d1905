/**
 * ============================================================================
 * 🎬 动画系统快速测试工具
 * ============================================================================
 *
 * @fileoverview 快速测试动画系统是否正常工作
 * @description 提供简单的动画测试和修复验证
 * 
 * @usage 使用方法
 * 在浏览器控制台中运行：quickTestAnimations()
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    /**
     * 快速测试动画系统
     */
    async function quickTestAnimations() {
        console.log('🎬 开始动画系统快速测试...');
        console.log('='.repeat(40));

        const results = [];

        // 1. 检查动画管理器
        console.log('1️⃣ 检查动画管理器...');
        const animationManager = window.OTA?.animationManager || window.animationManager;
        if (animationManager) {
            console.log('✅ 动画管理器存在');
            console.log(`   - 启用状态: ${animationManager.isAnimationEnabled?.() ? '启用' : '禁用'}`);
            results.push('动画管理器: ✅');
        } else {
            console.log('❌ 动画管理器不存在');
            results.push('动画管理器: ❌');
        }

        // 2. 检查管理器集成
        console.log('2️⃣ 检查管理器集成...');
        const managers = [
            { name: '实时分析', path: 'window.OTA?.uiManager?.managers?.realtimeAnalysis' },
            { name: '表单管理', path: 'window.OTA?.uiManager?.managers?.form' },
            { name: 'UI状态', path: 'window.OTA?.uiManager?.managers?.state' }
        ];

        managers.forEach(mgr => {
            try {
                const manager = eval(mgr.path);
                const hasAnimation = manager?.animationManager;
                console.log(`   - ${mgr.name}管理器: ${hasAnimation ? '✅ 已集成' : '⚠️ 未集成'}`);
                results.push(`${mgr.name}管理器集成: ${hasAnimation ? '✅' : '⚠️'}`);
            } catch (error) {
                console.log(`   - ${mgr.name}管理器: ❌ 不存在`);
                results.push(`${mgr.name}管理器集成: ❌`);
            }
        });

        // 3. 测试CSS动画
        console.log('3️⃣ 测试CSS动画...');
        const testElement = document.createElement('div');
        testElement.className = 'field-filling';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);

        const computedStyle = getComputedStyle(testElement);
        const hasTransition = computedStyle.transition !== 'all 0s ease 0s';
        console.log(`   - CSS动画类: ${hasTransition ? '✅ 正常' : '⚠️ 可能有问题'}`);
        results.push(`CSS动画类: ${hasTransition ? '✅' : '⚠️'}`);

        document.body.removeChild(testElement);

        // 4. 测试动画函数
        console.log('4️⃣ 测试动画函数...');
        if (animationManager) {
            try {
                const testInput = document.createElement('input');
                testInput.style.position = 'absolute';
                testInput.style.left = '-9999px';
                document.body.appendChild(testInput);

                await animationManager.animateFieldFill(testInput, '测试', { fieldName: 'test' });
                console.log('   - 字段填充动画: ✅ 正常');
                results.push('字段填充动画: ✅');

                document.body.removeChild(testInput);
            } catch (error) {
                console.log(`   - 字段填充动画: ❌ 错误 - ${error.message}`);
                results.push('字段填充动画: ❌');
            }
        } else {
            console.log('   - 字段填充动画: ⚠️ 跳过（动画管理器不可用）');
            results.push('字段填充动画: ⚠️');
        }

        // 5. 测试实际字段动画
        console.log('5️⃣ 测试实际字段动画...');
        const testFields = ['customerName', 'pickup', 'dropoff'];
        let workingFields = 0;

        testFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                workingFields++;
                // 模拟动画填充
                if (animationManager && animationManager.isAnimationEnabled?.()) {
                    try {
                        animationManager.animateFieldFill(field, `测试${fieldId}`, { fieldName: fieldId });
                        console.log(`   - ${fieldId}: ✅ 动画触发`);
                    } catch (error) {
                        console.log(`   - ${fieldId}: ❌ 动画失败`);
                    }
                } else {
                    field.value = `测试${fieldId}`;
                    console.log(`   - ${fieldId}: ⚠️ 降级填充`);
                }
            } else {
                console.log(`   - ${fieldId}: ❌ 字段不存在`);
            }
        });

        results.push(`实际字段测试: ${workingFields}/${testFields.length} 个字段可用`);

        // 输出总结
        console.log('\n' + '='.repeat(40));
        console.log('🎬 快速测试结果总结:');
        results.forEach(result => console.log(`   ${result}`));

        // 提供修复建议
        console.log('\n💡 修复建议:');
        if (!animationManager) {
            console.log('   - 动画管理器未加载，检查脚本加载顺序');
        }
        
        const hasIntegrationIssues = results.some(r => r.includes('未集成') || r.includes('❌'));
        if (hasIntegrationIssues) {
            console.log('   - 管理器集成有问题，等待1秒后重新检查');
            setTimeout(() => {
                console.log('🔄 延迟检查管理器集成状态...');
                managers.forEach(mgr => {
                    try {
                        const manager = eval(mgr.path);
                        const hasAnimation = manager?.animationManager;
                        console.log(`   - ${mgr.name}管理器: ${hasAnimation ? '✅ 已集成（延迟）' : '⚠️ 仍未集成'}`);
                    } catch (error) {
                        console.log(`   - ${mgr.name}管理器: ❌ 仍不存在`);
                    }
                });
            }, 1000);
        }

        console.log('   - 如果问题持续，运行 diagnoseAnimationSystem() 进行详细诊断');
        
        return results;
    }

    /**
     * 强制启用动画
     */
    function forceEnableAnimations() {
        console.log('🔧 强制启用动画...');
        
        // 启用用户偏好
        const appState = window.getAppState?.();
        if (appState) {
            appState.setAnimationsEnabled(true);
            console.log('✅ 用户动画偏好已启用');
        }

        // 强制启用动画管理器
        const animationManager = window.OTA?.animationManager || window.animationManager;
        if (animationManager) {
            animationManager.isEnabled = true;
            animationManager.setCSSVariables?.();
            console.log('✅ 动画管理器已强制启用');
        }

        console.log('🎬 动画强制启用完成，请重新测试');
    }

    /**
     * 测试特定字段的动画
     */
    function testFieldAnimation(fieldId, value = '测试值') {
        console.log(`🎬 测试字段 ${fieldId} 的动画...`);
        
        const field = document.getElementById(fieldId);
        if (!field) {
            console.log(`❌ 字段 ${fieldId} 不存在`);
            return;
        }

        const animationManager = window.OTA?.animationManager || window.animationManager;
        if (!animationManager) {
            console.log('❌ 动画管理器不可用');
            return;
        }

        if (!animationManager.isAnimationEnabled?.()) {
            console.log('⚠️ 动画已禁用');
            return;
        }

        try {
            animationManager.animateFieldFill(field, value, { fieldName: fieldId });
            console.log(`✅ 字段 ${fieldId} 动画测试成功`);
        } catch (error) {
            console.log(`❌ 字段 ${fieldId} 动画测试失败:`, error.message);
        }
    }

    // 导出全局函数
    window.quickTestAnimations = quickTestAnimations;
    window.forceEnableAnimations = forceEnableAnimations;
    window.testFieldAnimation = testFieldAnimation;

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.quickTestAnimations = quickTestAnimations;
    window.OTA.forceEnableAnimations = forceEnableAnimations;
    window.OTA.testFieldAnimation = testFieldAnimation;

    console.log('🎬 动画快速测试工具已加载');
    console.log('💡 使用方法:');
    console.log('   - quickTestAnimations() - 运行快速测试');
    console.log('   - forceEnableAnimations() - 强制启用动画');
    console.log('   - testFieldAnimation("fieldId", "value") - 测试特定字段');

})();
