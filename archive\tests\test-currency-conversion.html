<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货币转换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🧪 货币转换功能测试</h1>
    
    <div class="test-container">
        <h2>功能1：货币切换时的智能汇率转换</h2>
        <div class="form-group">
            <label for="testPrice">价格:</label>
            <input type="number" id="testPrice" value="100.00" step="0.01">
        </div>
        <div class="form-group">
            <label for="testCurrency">货币:</label>
            <select id="testCurrency">
                <option value="MYR">马来西亚令吉 (MYR)</option>
                <option value="USD" selected>美元 (USD)</option>
                <option value="SGD">新加坡元 (SGD)</option>
                <option value="CNY">人民币 (CNY)</option>
            </select>
        </div>
        <button onclick="testCurrencyConversion()">测试货币转换</button>
        <button onclick="resetTest()">重置</button>
        <div id="conversionResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h2>功能2：地址翻译功能</h2>
        <div class="form-group">
            <label for="testAddress">中文地址:</label>
            <input type="text" id="testAddress" value="吉隆坡国际机场" placeholder="输入中文地址...">
        </div>
        <button onclick="testAddressTranslation()">测试地址翻译</button>
        <div id="translationResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 模拟CurrencyConverter类
        class MockCurrencyConverter {
            constructor() {
                this.exchangeRates = {
                    'CNY': 0.615,  // 人民币转MYR：乘以0.615
                    'USD': 4.3,    // 美元转MYR：乘以4.3
                    'SGD': 3.4,    // 新币转MYR：乘以3.4
                    'MYR': 1.0     // MYR保持不变
                };
            }

            convertToMYR(amount, fromCurrency) {
                const rate = this.exchangeRates[fromCurrency.toUpperCase()];
                if (!rate) {
                    return {
                        originalAmount: amount,
                        originalCurrency: fromCurrency,
                        convertedAmount: amount,
                        convertedCurrency: 'MYR',
                        exchangeRate: 1,
                        needsConversion: false,
                        error: `不支持的货币类型: ${fromCurrency}`
                    };
                }

                const convertedAmount = Math.round(amount * rate * 100) / 100;
                const needsConversion = fromCurrency.toUpperCase() !== 'MYR';

                return {
                    originalAmount: amount,
                    originalCurrency: fromCurrency.toUpperCase(),
                    convertedAmount: convertedAmount,
                    convertedCurrency: 'MYR',
                    exchangeRate: rate,
                    needsConversion: needsConversion
                };
            }

            getExchangeRates() {
                return { ...this.exchangeRates };
            }
        }

        // 创建模拟实例
        const mockConverter = new MockCurrencyConverter();

        // 模拟货币转换功能
        function convertPriceOnCurrencyChange(newCurrency, oldCurrency) {
            const priceInput = document.getElementById('testPrice');
            const currentAmount = parseFloat(priceInput.value);
            
            if (isNaN(currentAmount) || currentAmount <= 0) {
                return;
            }

            try {
                // 先转换为MYR基准货币
                const toMYRResult = mockConverter.convertToMYR(currentAmount, oldCurrency);
                
                // 再从MYR转换为目标货币
                const exchangeRates = mockConverter.getExchangeRates();
                const targetRate = exchangeRates[newCurrency.toUpperCase()];
                
                if (!targetRate) {
                    throw new Error(`不支持的目标货币: ${newCurrency}`);
                }

                // 计算转换后的金额：MYR金额 / 目标货币汇率
                const convertedAmount = Math.round((toMYRResult.convertedAmount / targetRate) * 100) / 100;
                
                // 更新价格输入框
                priceInput.value = convertedAmount.toFixed(2);
                
                return {
                    success: true,
                    from: `${currentAmount} ${oldCurrency}`,
                    to: `${convertedAmount} ${newCurrency}`,
                    viaBaseCurrency: `${toMYRResult.convertedAmount} MYR`
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 测试货币转换
        function testCurrencyConversion() {
            const currencySelect = document.getElementById('testCurrency');
            const resultDiv = document.getElementById('conversionResult');
            
            const testSequence = [
                { from: 'USD', to: 'SGD' },
                { from: 'SGD', to: 'CNY' },
                { from: 'CNY', to: 'MYR' },
                { from: 'MYR', to: 'USD' }
            ];
            
            let results = [];
            let currentIndex = 0;
            
            function runNextTest() {
                if (currentIndex >= testSequence.length) {
                    // 显示所有结果
                    resultDiv.innerHTML = `
                        <h4>✅ 货币转换测试完成</h4>
                        ${results.map(r => `<p><strong>${r.step}:</strong> ${r.result}</p>`).join('')}
                    `;
                    resultDiv.className = 'result success';
                    resultDiv.style.display = 'block';
                    return;
                }
                
                const test = testSequence[currentIndex];
                currencySelect.value = test.from;
                
                setTimeout(() => {
                    const result = convertPriceOnCurrencyChange(test.to, test.from);
                    currencySelect.value = test.to;
                    
                    if (result.success) {
                        results.push({
                            step: `${test.from} → ${test.to}`,
                            result: `${result.from} → ${result.to} (via ${result.viaBaseCurrency})`
                        });
                    } else {
                        results.push({
                            step: `${test.from} → ${test.to}`,
                            result: `❌ ${result.error}`
                        });
                    }
                    
                    currentIndex++;
                    setTimeout(runNextTest, 500);
                }, 100);
            }
            
            resultDiv.innerHTML = '<p>🔄 正在执行货币转换测试...</p>';
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            runNextTest();
        }

        // 测试地址翻译（模拟）
        function testAddressTranslation() {
            const addressInput = document.getElementById('testAddress');
            const resultDiv = document.getElementById('translationResult');
            const address = addressInput.value.trim();
            
            if (!address) {
                resultDiv.innerHTML = '<p>❌ 请输入地址</p>';
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                return;
            }
            
            resultDiv.innerHTML = '<p>🔄 正在翻译地址...</p>';
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            // 模拟翻译结果
            setTimeout(() => {
                const mockTranslation = {
                    original: address,
                    malay: getMalayTranslation(address),
                    english: getEnglishTranslation(address)
                };
                
                resultDiv.innerHTML = `
                    <h4>✅ 地址翻译结果</h4>
                    <p><strong>原始地址:</strong> ${mockTranslation.original}</p>
                    <p><strong>马来文:</strong> ${mockTranslation.malay}</p>
                    <p><strong>英文:</strong> ${mockTranslation.english}</p>
                `;
                resultDiv.className = 'result success';
            }, 1500);
        }
        
        // 模拟翻译函数
        function getMalayTranslation(address) {
            const translations = {
                '吉隆坡国际机场': 'Lapangan Terbang Antarabangsa Kuala Lumpur',
                '双子塔': 'Menara Berkembar Petronas',
                '中央车站': 'Stesen Sentral',
                '茨厂街': 'Petaling Street'
            };
            return translations[address] || `${address} (Terjemahan Melayu)`;
        }
        
        function getEnglishTranslation(address) {
            const translations = {
                '吉隆坡国际机场': 'Kuala Lumpur International Airport',
                '双子塔': 'Petronas Twin Towers',
                '中央车站': 'KL Sentral',
                '茨厂街': 'Petaling Street'
            };
            return translations[address] || `${address} (English Translation)`;
        }

        // 重置测试
        function resetTest() {
            document.getElementById('testPrice').value = '100.00';
            document.getElementById('testCurrency').value = 'USD';
            document.getElementById('conversionResult').style.display = 'none';
        }
    </script>
</body>
</html>
