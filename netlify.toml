[build]
  publish = "."
  # No build command needed - serve static files directly
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"

# Environment variables for production
# Set these in Netlify dashboard: Site settings > Environment variables
# FLIGHTAWARE_API_KEY = "your-flightaware-api-key-here"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://generativelanguage.googleapis.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; connect-src 'self' https://gomyhire.com.my https://generativelanguage.googleapis.com https://aeroapi.flightaware.com; worker-src 'self' blob:;"

# Cache static assets
[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/image/*"
  [headers.values]
    Cache-Control = "public, max-age=2592000"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

# Remove problematic Content-Encoding headers
# Netlify handles compression automatically

# Redirect rules (if needed)
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Error pages
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 404
