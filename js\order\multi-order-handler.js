/**
 * ============================================================================
 * 🚀 核心业务流程 - 多订单处理器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 多订单处理器 - 子层实现
 * @description 负责多订单模式的具体处理逻辑，从multi-order-manager-v2.js拆分而来
 * 
 * @businessFlow 多订单处理
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * B2. 多订单分支 → 【当前文件职责】触发多订单模式 - 本地处理
 *     ↓
 * 映射到多订单表单 → 批量发送API → 保存历史
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：多订单处理的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供多订单处理服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/order-management-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 多订单模式激活和管理
 * - 🟢 订单选择和状态管理
 * - 🟢 批量处理进度跟踪
 * - 🟢 订单验证和数据完整性检查
 * - 🟢 多订单面板状态管理
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地处理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有多订单处理逻辑
 * - 兼容现有的状态管理
 * - 保持向后兼容的接口
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程API（严格本地处理）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持多订单处理准确性
 * - ✅ 保持现有的状态管理逻辑
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 多订单处理器 - 子层实现
     */
    class MultiOrderHandler {
        constructor() {
            this.logger = this.getLogger();
            
            // 多订单处理配置
            this.config = {
                maxOrdersPerBatch: 5,
                minOrderCount: 2,
                autoSelectAll: true,
                showProgressBar: true,
                enableBatchValidation: true
            };

            // 多订单状态
            this.state = {
                isActive: false,
                orders: [],
                selectedOrders: new Set(),
                processingStatus: new Map(),
                currentBatch: null
            };
            
            this.logger.log('多订单处理器已初始化', 'info');
        }

        /**
         * 激活多订单模式
         * @param {array} orders - 订单数组
         * @param {object} options - 激活选项
         * @returns {Promise<object>} 激活结果
         */
        async activateMultiOrderMode(orders, options = {}) {
            try {
                this.logger.log('激活多订单模式', 'info', { 
                    orderCount: orders.length 
                });

                if (!Array.isArray(orders) || orders.length < this.config.minOrderCount) {
                    throw new Error(`订单数量不足，至少需要 ${this.config.minOrderCount} 个订单`);
                }

                // 验证订单数据
                const validatedOrders = this.validateOrders(orders);
                if (validatedOrders.length === 0) {
                    throw new Error('没有有效的订单数据');
                }

                // 更新状态
                this.state.isActive = true;
                this.state.orders = validatedOrders;
                this.state.selectedOrders.clear();
                this.state.processingStatus.clear();

                // 默认选择所有订单
                if (this.config.autoSelectAll) {
                    validatedOrders.forEach((_, index) => {
                        this.state.selectedOrders.add(index);
                    });
                }

                // 初始化处理状态
                validatedOrders.forEach((_, index) => {
                    this.state.processingStatus.set(index, {
                        status: 'ready',
                        progress: 0,
                        error: null,
                        result: null
                    });
                });

                this.logger.log('多订单模式激活成功', 'success', { 
                    validOrderCount: validatedOrders.length,
                    selectedCount: this.state.selectedOrders.size 
                });

                return {
                    success: true,
                    orderCount: validatedOrders.length,
                    selectedCount: this.state.selectedOrders.size,
                    orders: validatedOrders
                };

            } catch (error) {
                this.logger.log('多订单模式激活失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 验证订单数组
         * @param {array} orders - 订单数组
         * @returns {array} 验证后的有效订单数组
         */
        validateOrders(orders) {
            const validOrders = [];

            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                
                if (this.isValidOrder(order)) {
                    // 添加订单索引
                    validOrders.push({
                        ...order,
                        originalIndex: i,
                        validatedAt: new Date().toISOString()
                    });
                } else {
                    this.logger.log(`订单 ${i + 1} 验证失败`, 'warning', { order });
                }
            }

            this.logger.log('订单验证完成', 'info', { 
                totalOrders: orders.length,
                validOrders: validOrders.length 
            });

            return validOrders;
        }

        /**
         * 检查是否为有效订单
         * @param {object} order - 订单数据
         * @returns {boolean} 是否有效
         */
        isValidOrder(order) {
            if (!order || typeof order !== 'object') {
                return false;
            }

            // 必须有客户姓名
            if (!order.customer_name || order.customer_name.trim().length < 2) {
                return false;
            }

            // 必须有接送地点之一
            if (!order.pickup_location && !order.dropoff_location) {
                return false;
            }

            return true;
        }

        /**
         * 选择/取消选择订单
         * @param {number} orderIndex - 订单索引
         * @param {boolean} selected - 是否选择
         */
        toggleOrderSelection(orderIndex, selected = null) {
            try {
                if (selected === null) {
                    // 切换选择状态
                    if (this.state.selectedOrders.has(orderIndex)) {
                        this.state.selectedOrders.delete(orderIndex);
                    } else {
                        this.state.selectedOrders.add(orderIndex);
                    }
                } else {
                    // 设置选择状态
                    if (selected) {
                        this.state.selectedOrders.add(orderIndex);
                    } else {
                        this.state.selectedOrders.delete(orderIndex);
                    }
                }

                this.logger.log('订单选择状态更新', 'info', { 
                    orderIndex,
                    selected: this.state.selectedOrders.has(orderIndex),
                    totalSelected: this.state.selectedOrders.size 
                });

            } catch (error) {
                this.logger.log('订单选择状态更新失败', 'error', { error: error.message });
            }
        }

        /**
         * 获取选中的订单
         * @returns {array} 选中的订单数组
         */
        getSelectedOrders() {
            const selectedOrders = [];
            
            for (const index of this.state.selectedOrders) {
                if (this.state.orders[index]) {
                    selectedOrders.push({
                        ...this.state.orders[index],
                        selectionIndex: index
                    });
                }
            }

            return selectedOrders;
        }

        /**
         * 更新订单处理状态
         * @param {number} orderIndex - 订单索引
         * @param {string} status - 状态
         * @param {object} data - 附加数据
         */
        updateOrderStatus(orderIndex, status, data = {}) {
            try {
                const currentStatus = this.state.processingStatus.get(orderIndex) || {};
                
                this.state.processingStatus.set(orderIndex, {
                    ...currentStatus,
                    status: status,
                    updatedAt: new Date().toISOString(),
                    ...data
                });

                this.logger.log('订单状态更新', 'info', { 
                    orderIndex,
                    status,
                    data 
                });

            } catch (error) {
                this.logger.log('订单状态更新失败', 'error', { error: error.message });
            }
        }

        /**
         * 获取多订单处理状态
         * @returns {object} 处理状态
         */
        getMultiOrderStatus() {
            return {
                isActive: this.state.isActive,
                totalOrders: this.state.orders.length,
                selectedOrders: this.state.selectedOrders.size,
                processingStatus: Object.fromEntries(this.state.processingStatus),
                config: this.config
            };
        }

        /**
         * 重置多订单状态
         */
        resetMultiOrderState() {
            this.state.isActive = false;
            this.state.orders = [];
            this.state.selectedOrders.clear();
            this.state.processingStatus.clear();
            this.state.currentBatch = null;
            
            this.logger.log('多订单状态已重置', 'info');
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const multiOrderHandler = new MultiOrderHandler();

    // 导出到全局作用域
    window.MultiOrderHandler = MultiOrderHandler;
    window.OTA.MultiOrderHandler = MultiOrderHandler;
    window.OTA.multiOrderHandler = multiOrderHandler;

    console.log('✅ MultiOrderHandler (子层实现) 已加载');

})();
