/**
 * ============================================================================
 * 🎬 动画系统测试脚本
 * ============================================================================
 *
 * @fileoverview 动画系统集成测试
 * @description 验证动画管理器、字段填充动画、状态反馈动画和按钮交互动画
 * 
 * @features 测试功能
 * - 动画管理器初始化测试
 * - 字段填充动画测试
 * - 状态反馈动画测试
 * - 按钮交互动画测试
 * - 用户偏好设置测试
 * - 响应式适配测试
 * 
 * @usage 使用方法
 * 在浏览器控制台中运行：testAnimationSystem()
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    /**
     * 动画系统测试套件
     */
    class AnimationSystemTest {
        constructor() {
            this.testResults = [];
            this.animationManager = null;
        }

        /**
         * 运行完整的动画系统测试
         */
        async runAllTests() {
            console.log('🎬 开始动画系统集成测试...');
            console.log('='.repeat(50));

            try {
                // 1. 基础组件测试
                await this.testAnimationManagerInitialization();
                await this.testUserPreferences();
                
                // 2. 功能测试
                await this.testFieldFillAnimations();
                await this.testStatusFeedbackAnimations();
                await this.testButtonInteractionAnimations();
                
                // 3. 集成测试
                await this.testRealtimeAnalysisIntegration();
                await this.testFormManagerIntegration();
                
                // 4. 响应式和可访问性测试
                await this.testResponsiveAnimations();
                await this.testAccessibilitySupport();
                
                // 输出测试结果
                this.outputTestResults();
                
            } catch (error) {
                console.error('❌ 动画系统测试失败:', error);
                this.addTestResult('系统测试', false, `测试执行失败: ${error.message}`);
            }
        }

        /**
         * 测试动画管理器初始化
         */
        async testAnimationManagerInitialization() {
            console.log('🔧 测试动画管理器初始化...');
            
            try {
                // 检查动画管理器是否存在
                this.animationManager = window.OTA?.animationManager || window.animationManager;
                
                if (!this.animationManager) {
                    this.addTestResult('动画管理器初始化', false, '动画管理器实例不存在');
                    return;
                }
                
                // 检查基本方法
                const requiredMethods = [
                    'isAnimationEnabled', 'animateFieldFill', 'animateButtonClick',
                    'animateStatusFeedback', 'animateProgress'
                ];
                
                const missingMethods = requiredMethods.filter(method => 
                    typeof this.animationManager[method] !== 'function'
                );
                
                if (missingMethods.length > 0) {
                    this.addTestResult('动画管理器初始化', false, 
                        `缺少方法: ${missingMethods.join(', ')}`);
                    return;
                }
                
                // 检查配置
                const config = this.animationManager.config;
                if (!config || !config.durations || !config.easings) {
                    this.addTestResult('动画管理器初始化', false, '动画配置不完整');
                    return;
                }
                
                this.addTestResult('动画管理器初始化', true, '所有基本功能正常');
                
            } catch (error) {
                this.addTestResult('动画管理器初始化', false, error.message);
            }
        }

        /**
         * 测试用户偏好设置
         */
        async testUserPreferences() {
            console.log('⚙️ 测试用户偏好设置...');
            
            try {
                const appState = window.getAppState?.();
                if (!appState) {
                    this.addTestResult('用户偏好设置', false, '应用状态管理器不可用');
                    return;
                }
                
                // 测试动画偏好设置
                const originalSetting = appState.getAnimationsEnabled();
                
                // 测试设置为false
                appState.setAnimationsEnabled(false);
                const disabledSetting = appState.getAnimationsEnabled();
                
                // 测试设置为true
                appState.setAnimationsEnabled(true);
                const enabledSetting = appState.getAnimationsEnabled();
                
                // 恢复原始设置
                appState.setAnimationsEnabled(originalSetting);
                
                if (disabledSetting === false && enabledSetting === true) {
                    this.addTestResult('用户偏好设置', true, '动画偏好设置功能正常');
                } else {
                    this.addTestResult('用户偏好设置', false, '动画偏好设置功能异常');
                }
                
            } catch (error) {
                this.addTestResult('用户偏好设置', false, error.message);
            }
        }

        /**
         * 测试字段填充动画
         */
        async testFieldFillAnimations() {
            console.log('📝 测试字段填充动画...');
            
            try {
                if (!this.animationManager) {
                    this.addTestResult('字段填充动画', false, '动画管理器不可用');
                    return;
                }
                
                // 创建测试输入框
                const testInput = document.createElement('input');
                testInput.type = 'text';
                testInput.id = 'test-input';
                document.body.appendChild(testInput);
                
                // 测试动画填充
                await this.animationManager.animateFieldFill(testInput, '测试值', { fieldName: 'test' });
                
                // 检查值是否正确设置
                if (testInput.value === '测试值') {
                    this.addTestResult('字段填充动画', true, '字段填充动画功能正常');
                } else {
                    this.addTestResult('字段填充动画', false, '字段值设置失败');
                }
                
                // 清理测试元素
                document.body.removeChild(testInput);
                
            } catch (error) {
                this.addTestResult('字段填充动画', false, error.message);
            }
        }

        /**
         * 测试状态反馈动画
         */
        async testStatusFeedbackAnimations() {
            console.log('📊 测试状态反馈动画...');
            
            try {
                if (!this.animationManager) {
                    this.addTestResult('状态反馈动画', false, '动画管理器不可用');
                    return;
                }
                
                // 创建测试元素
                const testElement = document.createElement('div');
                testElement.id = 'test-status';
                testElement.textContent = '测试状态';
                document.body.appendChild(testElement);
                
                // 测试不同状态的动画
                const statuses = ['success', 'error', 'warning', 'info'];
                let successCount = 0;
                
                for (const status of statuses) {
                    try {
                        await this.animationManager.animateStatusFeedback(testElement, status);
                        successCount++;
                    } catch (error) {
                        console.warn(`状态 ${status} 动画测试失败:`, error);
                    }
                }
                
                // 清理测试元素
                document.body.removeChild(testElement);
                
                if (successCount === statuses.length) {
                    this.addTestResult('状态反馈动画', true, '所有状态反馈动画正常');
                } else {
                    this.addTestResult('状态反馈动画', false, 
                        `${statuses.length - successCount} 个状态动画失败`);
                }
                
            } catch (error) {
                this.addTestResult('状态反馈动画', false, error.message);
            }
        }

        /**
         * 测试按钮交互动画
         */
        async testButtonInteractionAnimations() {
            console.log('🔘 测试按钮交互动画...');
            
            try {
                if (!this.animationManager) {
                    this.addTestResult('按钮交互动画', false, '动画管理器不可用');
                    return;
                }
                
                // 创建测试按钮
                const testButton = document.createElement('button');
                testButton.className = 'btn btn-primary';
                testButton.textContent = '测试按钮';
                document.body.appendChild(testButton);
                
                // 测试按钮点击动画
                await this.animationManager.animateButtonClick(testButton);
                
                // 清理测试元素
                document.body.removeChild(testButton);
                
                this.addTestResult('按钮交互动画', true, '按钮交互动画功能正常');
                
            } catch (error) {
                this.addTestResult('按钮交互动画', false, error.message);
            }
        }

        /**
         * 测试实时分析集成
         */
        async testRealtimeAnalysisIntegration() {
            console.log('🔄 测试实时分析集成...');
            
            try {
                const realtimeManager = window.OTA?.managers?.RealtimeAnalysisManager;
                
                if (!realtimeManager) {
                    this.addTestResult('实时分析集成', false, '实时分析管理器不可用');
                    return;
                }
                
                // 检查动画管理器是否已集成
                if (realtimeManager.animationManager) {
                    this.addTestResult('实时分析集成', true, '实时分析管理器已集成动画支持');
                } else {
                    this.addTestResult('实时分析集成', false, '实时分析管理器未集成动画支持');
                }
                
            } catch (error) {
                this.addTestResult('实时分析集成', false, error.message);
            }
        }

        /**
         * 测试表单管理器集成
         */
        async testFormManagerIntegration() {
            console.log('📋 测试表单管理器集成...');
            
            try {
                const uiManager = window.OTA?.uiManager;
                const formManager = uiManager?.getManager('form');
                
                if (!formManager) {
                    this.addTestResult('表单管理器集成', false, '表单管理器不可用');
                    return;
                }
                
                // 检查动画管理器是否已集成
                if (formManager.animationManager) {
                    this.addTestResult('表单管理器集成', true, '表单管理器已集成动画支持');
                } else {
                    this.addTestResult('表单管理器集成', false, '表单管理器未集成动画支持');
                }
                
            } catch (error) {
                this.addTestResult('表单管理器集成', false, error.message);
            }
        }

        /**
         * 测试响应式动画
         */
        async testResponsiveAnimations() {
            console.log('📱 测试响应式动画...');
            
            try {
                // 检查CSS变量是否正确设置
                const root = document.documentElement;
                const animationDuration = getComputedStyle(root).getPropertyValue('--animation-duration-normal');
                
                if (animationDuration) {
                    this.addTestResult('响应式动画', true, 'CSS动画变量已正确设置');
                } else {
                    this.addTestResult('响应式动画', false, 'CSS动画变量未设置');
                }
                
            } catch (error) {
                this.addTestResult('响应式动画', false, error.message);
            }
        }

        /**
         * 测试可访问性支持
         */
        async testAccessibilitySupport() {
            console.log('♿ 测试可访问性支持...');
            
            try {
                // 检查是否支持prefers-reduced-motion
                const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
                
                if (prefersReducedMotion) {
                    this.addTestResult('可访问性支持', true, '支持减少动画偏好检测');
                } else {
                    this.addTestResult('可访问性支持', false, '不支持减少动画偏好检测');
                }
                
            } catch (error) {
                this.addTestResult('可访问性支持', false, error.message);
            }
        }

        /**
         * 添加测试结果
         * @param {string} testName - 测试名称
         * @param {boolean} passed - 是否通过
         * @param {string} message - 详细信息
         */
        addTestResult(testName, passed, message) {
            this.testResults.push({
                name: testName,
                passed,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 输出测试结果
         */
        outputTestResults() {
            console.log('\n' + '='.repeat(50));
            console.log('🎬 动画系统测试结果汇总');
            console.log('='.repeat(50));
            
            const passedTests = this.testResults.filter(result => result.passed);
            const failedTests = this.testResults.filter(result => !result.passed);
            
            console.log(`✅ 通过: ${passedTests.length} 项`);
            console.log(`❌ 失败: ${failedTests.length} 项`);
            console.log(`📊 总计: ${this.testResults.length} 项`);
            
            if (failedTests.length > 0) {
                console.log('\n❌ 失败的测试:');
                failedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            if (passedTests.length > 0) {
                console.log('\n✅ 通过的测试:');
                passedTests.forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
            }
            
            const successRate = Math.round((passedTests.length / this.testResults.length) * 100);
            console.log(`\n🎯 成功率: ${successRate}%`);
            
            if (successRate >= 80) {
                console.log('🎉 动画系统测试基本通过！');
            } else {
                console.log('⚠️ 动画系统需要进一步优化');
            }
        }
    }

    // 创建全局测试函数
    window.testAnimationSystem = async function() {
        const tester = new AnimationSystemTest();
        await tester.runAllTests();
        return tester.testResults;
    };

    // 导出到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.AnimationSystemTest = AnimationSystemTest;

    console.log('🎬 动画系统测试脚本已加载');
    console.log('💡 使用 testAnimationSystem() 运行完整测试');

})();
