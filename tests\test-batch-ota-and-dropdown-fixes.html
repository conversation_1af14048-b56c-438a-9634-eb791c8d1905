<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量OTA设置和下拉菜单修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .editable-field {
            display: inline-block;
            padding: 8px 12px;
            margin: 5px;
            background: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            min-width: 120px;
            transition: all 0.2s ease;
        }
        .editable-field:hover {
            background: #dee2e6;
            border-color: #007bff;
        }
        .editable-field.editing {
            background: #fff;
            border-color: #007bff;
        }
        .readonly-field {
            background: #f8f9fa !important;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6 !important;
        }
        .readonly-field:hover {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        .grid-value {
            display: inline-block;
            font-weight: 500;
        }
        .field-editor {
            width: 100%;
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            background: white;
        }
        .field-editor:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            margin-right: 8px;
        }
        .field-row {
            margin: 10px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .batch-controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .batch-controls select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <h1>批量OTA设置和下拉菜单修复测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">🔧 修复说明</h2>
        <p><strong>问题1修复</strong>：批量OTA渠道设置功能失效</p>
        <ul>
            <li>简化 getOtaChannelDisplay 方法，直接使用订单数据</li>
            <li>移除复杂的映射查找逻辑，确保批量设置后能正确显示</li>
        </ul>
        <p><strong>问题2修复</strong>：下拉菜单闪现问题</p>
        <ul>
            <li>修改全局事件协调器，排除字段编辑器</li>
            <li>简化字段编辑器的事件处理逻辑</li>
            <li>移除复杂的延迟和事件阻止机制</li>
        </ul>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 批量OTA设置测试</h2>
        <div class="batch-controls">
            <label>批量设置OTA渠道：</label>
            <select id="batchOtaSelect">
                <option value="">请选择OTA渠道</option>
                <option value="Agoda">Agoda</option>
                <option value="Klook">Klook</option>
                <option value="Ctrip">Ctrip</option>
                <option value="Traveloka">Traveloka</option>
                <option value="Direct Booking">Direct Booking</option>
            </select>
            <button onclick="testBatchOtaSetting()">测试批量设置</button>
        </div>
        <div id="batchOtaResult"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 测试订单卡片</h2>
        <div class="order-card" data-index="0">
            <h4>测试订单 1</h4>
            
            <div class="field-row">
                <span class="field-label">客户姓名:</span>
                <div class="editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(0, 'customerName')">
                    <span class="grid-value">张三</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">OTA渠道:</span>
                <div class="editable-field readonly-field" data-field="otaChannel" title="OTA渠道通过批量操作设置">
                    <span class="grid-value">Agoda</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">车型:</span>
                <div class="editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(0, 'vehicleType')">
                    <span class="grid-value">5 Seater</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">驾驶区域:</span>
                <div class="editable-field" data-field="drivingRegion" onclick="window.OTA.multiOrderManager.editField(0, 'drivingRegion')">
                    <span class="grid-value">市区</span>
                </div>
            </div>
        </div>

        <div class="order-card" data-index="1">
            <h4>测试订单 2</h4>
            
            <div class="field-row">
                <span class="field-label">客户姓名:</span>
                <div class="editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(1, 'customerName')">
                    <span class="grid-value">李四</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">OTA渠道:</span>
                <div class="editable-field readonly-field" data-field="otaChannel" title="OTA渠道通过批量操作设置">
                    <span class="grid-value">Klook</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">车型:</span>
                <div class="editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(1, 'vehicleType')">
                    <span class="grid-value">7 Seater MPV</span>
                </div>
            </div>
            
            <div class="field-row">
                <span class="field-label">驾驶区域:</span>
                <div class="editable-field" data-field="drivingRegion" onclick="window.OTA.multiOrderManager.editField(1, 'drivingRegion')">
                    <span class="grid-value">郊区</span>
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🧪 测试操作</h2>
        <button onclick="testDropdownEditing()">测试下拉字段编辑</button>
        <button onclick="testBatchOtaSync()">测试批量OTA同步</button>
        <button onclick="resetTestData()">重置测试数据</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="testResult"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 测试日志</h2>
        <div id="testLog" class="log-area"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/api-service.js"></script>
    <script src="../js/core/global-event-coordinator.js"></script>
    <script src="../js/multi-order-manager-v2.js"></script>

    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function showResult(message, type = 'info') {
            const container = document.getElementById('testResult');
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function showBatchResult(message, type = 'info') {
            const container = document.getElementById('batchOtaResult');
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // 测试批量OTA设置
        function testBatchOtaSetting() {
            const select = document.getElementById('batchOtaSelect');
            const otaChannel = select.value;
            
            if (!otaChannel) {
                showBatchResult('请先选择OTA渠道', 'error');
                return;
            }

            log(`开始测试批量OTA设置: ${otaChannel}`);
            
            try {
                if (window.OTA && window.OTA.multiOrderManager) {
                    // 模拟批量设置
                    window.OTA.multiOrderManager.applyBatchOtaToSelected(otaChannel);
                    
                    // 检查UI是否更新
                    setTimeout(() => {
                        const otaFields = document.querySelectorAll('[data-field="otaChannel"] .grid-value');
                        let updatedCount = 0;
                        
                        otaFields.forEach(field => {
                            if (field.textContent === otaChannel) {
                                updatedCount++;
                            }
                        });
                        
                        if (updatedCount === otaFields.length) {
                            showBatchResult(`✅ 批量OTA设置成功，${updatedCount}个订单已更新为: ${otaChannel}`, 'success');
                            log(`批量OTA设置成功: ${otaChannel}`);
                        } else {
                            showBatchResult(`⚠️ 部分订单更新失败，${updatedCount}/${otaFields.length}个订单已更新`, 'error');
                            log(`批量OTA设置部分失败: ${updatedCount}/${otaFields.length}`);
                        }
                    }, 500);
                    
                } else {
                    showBatchResult('❌ 多订单管理器未加载', 'error');
                }
                
            } catch (error) {
                showBatchResult(`❌ 批量OTA设置失败: ${error.message}`, 'error');
                log(`批量OTA设置失败: ${error.message}`, 'error');
            }
        }

        // 测试下拉字段编辑
        function testDropdownEditing() {
            log('开始测试下拉字段编辑...');
            
            const dropdownFields = document.querySelectorAll('[data-field="vehicleType"], [data-field="drivingRegion"]');
            let testResults = [];
            
            dropdownFields.forEach((field, index) => {
                const fieldName = field.getAttribute('data-field');
                const orderIndex = field.closest('.order-card').getAttribute('data-index');
                
                try {
                    log(`测试字段: ${fieldName} (订单${orderIndex})`);
                    
                    // 模拟点击编辑
                    if (window.OTA && window.OTA.multiOrderManager) {
                        window.OTA.multiOrderManager.editField(parseInt(orderIndex), fieldName);
                        testResults.push(`✅ ${fieldName}: 编辑器创建成功`);
                    } else {
                        testResults.push(`❌ ${fieldName}: 多订单管理器未加载`);
                    }
                } catch (error) {
                    testResults.push(`❌ ${fieldName}: ${error.message}`);
                    log(`字段 ${fieldName} 测试失败: ${error.message}`, 'error');
                }
            });
            
            showResult('下拉字段编辑测试结果:<br>' + testResults.join('<br>'), 'info');
            log('下拉字段编辑测试完成');
        }

        // 测试批量OTA同步
        function testBatchOtaSync() {
            log('开始测试批量OTA同步...');
            
            try {
                if (window.OTA && window.OTA.multiOrderManager) {
                    const orders = window.OTA.multiOrderManager.state?.parsedOrders;
                    if (orders && orders.length > 0) {
                        let syncResults = [];
                        
                        orders.forEach((order, index) => {
                            const otaValue = order._otaChannel || order.ota || '未设置';
                            const displayElement = document.querySelector(`[data-index="${index}"] [data-field="otaChannel"] .grid-value`);
                            const displayValue = displayElement ? displayElement.textContent : '未找到';
                            
                            if (otaValue === displayValue) {
                                syncResults.push(`✅ 订单${index + 1}: 数据与显示同步 (${otaValue})`);
                            } else {
                                syncResults.push(`❌ 订单${index + 1}: 数据不同步 (数据:${otaValue}, 显示:${displayValue})`);
                            }
                        });
                        
                        showResult('批量OTA同步测试结果:<br>' + syncResults.join('<br>'), 'info');
                        log('批量OTA同步测试完成');
                    } else {
                        showResult('❌ 没有找到订单数据', 'error');
                    }
                } else {
                    showResult('❌ 多订单管理器未加载', 'error');
                }
                
            } catch (error) {
                showResult(`❌ 批量OTA同步测试失败: ${error.message}`, 'error');
                log(`批量OTA同步测试失败: ${error.message}`, 'error');
            }
        }

        // 重置测试数据
        function resetTestData() {
            log('重置测试数据...');
            
            // 模拟订单数据
            if (window.OTA && window.OTA.multiOrderManager) {
                window.OTA.multiOrderManager.state = window.OTA.multiOrderManager.state || {};
                window.OTA.multiOrderManager.state.parsedOrders = [
                    {
                        customer_name: '张三',
                        _otaChannel: 'Agoda',
                        ota: 'Agoda',
                        vehicle_type: '5 Seater',
                        driving_region: '市区'
                    },
                    {
                        customer_name: '李四',
                        _otaChannel: 'Klook',
                        ota: 'Klook',
                        vehicle_type: '7 Seater MPV',
                        driving_region: '郊区'
                    }
                ];
                
                log('✅ 测试数据已重置');
                showResult('✅ 测试数据已重置', 'success');
            } else {
                log('❌ 多订单管理器未加载', 'error');
                showResult('❌ 多订单管理器未加载', 'error');
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            log('测试页面加载完成');
            log('开始初始化测试环境...');
            
            // 初始化必要的全局对象
            if (!window.OTA) {
                window.OTA = {};
            }
            
            // 初始化全局事件协调器
            if (window.OTA.globalEventCoordinator) {
                window.OTA.globalEventCoordinator.init();
                log('✅ 全局事件协调器已初始化');
            }
            
            // 等待脚本加载
            setTimeout(() => {
                if (window.OTA.multiOrderManager) {
                    log('✅ 多订单管理器已加载');
                    resetTestData();
                } else {
                    log('⚠️ 多订单管理器未加载，某些测试可能失败', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
