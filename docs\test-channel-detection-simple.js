/**
 * 简化的渠道检测功能验证脚本
 * 用于验证智能渠道检测与自动分析功能
 */

// 模拟测试环境
function setupTestEnvironment() {
    console.log('🚀 设置测试环境...');
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    
    // 模拟必要的依赖
    if (!window.getLogger) {
        window.getLogger = () => ({
            log: (message, level = 'info', data = null) => {
                console.log(`[${level.toUpperCase()}] ${message}`, data || '');
            }
        });
    }
    
    if (!window.getAppState) {
        window.getAppState = () => ({
            get: (path) => {
                if (path === 'auth.user') {
                    return null; // 模拟通用用户（无专属配置）
                }
                return null;
            }
        });
    }
    
    console.log('✅ 测试环境设置完成');
}

// 测试渠道检测功能
async function testChannelDetection() {
    console.log('\n🎯 开始测试渠道检测功能...');
    
    try {
        // 创建渠道检测器实例
        const ChannelDetectorClass = window.OTA?.ChannelDetector || window.ChannelDetector;
        if (!ChannelDetectorClass) {
            console.error('❌ ChannelDetector类不可用');
            return false;
        }
        
        const detector = new ChannelDetectorClass();
        
        // 测试用例
        const testCases = [
            {
                name: 'Fliggy渠道检测',
                input: '订单编号1234567890123456789 客户：张先生 上车：KLIA',
                expectedChannel: 'Fliggy'
            },
            {
                name: 'JingGe渠道检测',
                input: '商铺订单 客户：李女士 接送：机场到市中心',
                expectedChannel: 'JingGe'
            },
            {
                name: 'Klook关键词检测',
                input: 'Klook订单 客户：王先生 从机场到酒店',
                expectedChannel: 'Klook West Malaysia'
            },
            {
                name: '无特征内容',
                input: '客户：赵先生 从A地到B地',
                expectedChannel: null
            }
        ];
        
        let passedTests = 0;
        
        for (const testCase of testCases) {
            console.log(`\n测试: ${testCase.name}`);
            console.log(`输入: ${testCase.input}`);
            
            const result = await detector.detectChannel(testCase.input);
            console.log(`检测结果:`, result);
            
            const passed = result.channel === testCase.expectedChannel;
            console.log(`预期渠道: ${testCase.expectedChannel}`);
            console.log(`实际渠道: ${result.channel}`);
            console.log(`测试结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
            
            if (passed) passedTests++;
        }
        
        console.log(`\n📊 渠道检测测试结果: ${passedTests}/${testCases.length} 通过`);
        return passedTests === testCases.length;
        
    } catch (error) {
        console.error('❌ 渠道检测测试失败:', error);
        return false;
    }
}

// 测试策略文件兼容性
function testStrategyCompatibility() {
    console.log('\n🔧 开始测试策略文件兼容性...');
    
    try {
        // 测试Fliggy策略
        if (typeof window.FliggyOTAStrategy !== 'undefined') {
            console.log('✅ FliggyOTAStrategy 可用');
            
            const channelName = window.FliggyOTAStrategy.getChannelName();
            console.log(`渠道名称: ${channelName}`);
            
            const snippets = window.FliggyOTAStrategy.getFieldPromptSnippets();
            console.log(`提示词片段数量: ${Object.keys(snippets).length}`);
            console.log(`提示词字段: ${Object.keys(snippets).join(', ')}`);
        } else {
            console.warn('⚠️ FliggyOTAStrategy 不可用');
        }
        
        // 测试JingGe策略
        if (typeof window.JingGeOTAStrategy !== 'undefined') {
            console.log('✅ JingGeOTAStrategy 可用');
            
            const channelName = window.JingGeOTAStrategy.getChannelName();
            console.log(`渠道名称: ${channelName}`);
            
            const snippets = window.JingGeOTAStrategy.getFieldPromptSnippets();
            console.log(`提示词片段数量: ${Object.keys(snippets).length}`);
            console.log(`提示词字段: ${Object.keys(snippets).join(', ')}`);
        } else {
            console.warn('⚠️ JingGeOTAStrategy 不可用');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 策略文件兼容性测试失败:', error);
        return false;
    }
}

// 测试提示词构建器
async function testPromptBuilder() {
    console.log('\n📝 开始测试提示词构建器...');
    
    try {
        const PromptBuilderClass = window.OTA?.PromptBuilder || window.PromptBuilder;
        if (!PromptBuilderClass) {
            console.error('❌ PromptBuilder类不可用');
            return false;
        }
        
        const builder = new PromptBuilderClass();
        
        // 测试Fliggy渠道提示词构建
        const channelResult = {
            channel: 'Fliggy',
            confidence: 0.95,
            method: 'fliggy_pattern'
        };
        
        const input = '订单编号1234567890123456789 客户：张先生';
        const options = { autoTriggered: true, sourceField: 'test', confidence: 0.95 };
        
        const prompt = await builder.buildPrompt(input, channelResult, options);
        
        console.log('✅ 提示词构建成功');
        console.log(`提示词长度: ${prompt.length} 字符`);
        console.log('提示词预览:', prompt.substring(0, 200) + '...');
        
        // 验证提示词包含渠道专属内容
        const containsChannelInfo = prompt.includes('Fliggy') && prompt.includes('自动检测模式');
        console.log(`包含渠道专属信息: ${containsChannelInfo ? '✅ 是' : '❌ 否'}`);
        
        return containsChannelInfo;
        
    } catch (error) {
        console.error('❌ 提示词构建器测试失败:', error);
        return false;
    }
}

// 测试语言检测器扩展功能
function testLanguageDetectorExtension() {
    console.log('\n🌐 开始测试语言检测器扩展功能...');
    
    try {
        const languageDetector = window.OTA?.unifiedLanguageDetector;
        if (!languageDetector) {
            console.warn('⚠️ UnifiedLanguageDetector 不可用');
            return false;
        }
        
        // 测试渠道检测配置
        const mockChannels = [
            { value: 'Fliggy', text: 'Fliggy' },
            { value: 'Klook West Malaysia', text: 'Klook West Malaysia' },
            { value: 'Kkday', text: 'Kkday' }
        ];
        
        languageDetector.initChannelDetection(mockChannels);
        console.log('✅ 渠道检测已初始化');
        
        languageDetector.enableAutoAnalysis(true);
        console.log('✅ 自动分析已启用');
        
        // 检查配置状态
        const config = languageDetector.channelDetectionConfig;
        console.log(`渠道检测启用状态: ${config.enabled ? '✅ 是' : '❌ 否'}`);
        console.log(`自动分析启用状态: ${config.autoAnalysisEnabled ? '✅ 是' : '❌ 否'}`);
        console.log(`可用渠道数量: ${config.availableChannels.length}`);
        
        return config.enabled && config.autoAnalysisEnabled;
        
    } catch (error) {
        console.error('❌ 语言检测器扩展功能测试失败:', error);
        return false;
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行智能渠道检测功能测试套件...\n');
    
    // 设置测试环境
    setupTestEnvironment();
    
    const results = {
        channelDetection: false,
        strategyCompatibility: false,
        promptBuilder: false,
        languageDetectorExtension: false
    };
    
    // 运行各项测试
    results.channelDetection = await testChannelDetection();
    results.strategyCompatibility = testStrategyCompatibility();
    results.promptBuilder = await testPromptBuilder();
    results.languageDetectorExtension = testLanguageDetectorExtension();
    
    // 汇总结果
    console.log('\n📊 测试结果汇总:');
    console.log('==================');
    
    const testNames = {
        channelDetection: '渠道检测功能',
        strategyCompatibility: '策略文件兼容性',
        promptBuilder: '提示词构建器',
        languageDetectorExtension: '语言检测器扩展'
    };
    
    let passedCount = 0;
    const totalCount = Object.keys(results).length;
    
    for (const [key, passed] of Object.entries(results)) {
        const status = passed ? '✅ 通过' : '❌ 失败';
        console.log(`${testNames[key]}: ${status}`);
        if (passed) passedCount++;
    }
    
    console.log('==================');
    console.log(`总体结果: ${passedCount}/${totalCount} 项测试通过`);
    
    if (passedCount === totalCount) {
        console.log('🎉 所有测试通过！智能渠道检测功能实施成功！');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步调试');
    }
    
    return passedCount === totalCount;
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runAllTests };
} else {
    // 浏览器环境下自动运行测试
    window.runChannelDetectionTests = runAllTests;
    
    // 页面加载完成后自动运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 2000); // 延迟2秒确保所有脚本加载完成
        });
    } else {
        setTimeout(runAllTests, 2000);
    }
}
