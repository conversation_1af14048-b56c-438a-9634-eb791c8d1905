/* 多订单模块移动端样式 */
@media (max-width: 992px) {
    .order-grid-layout {
        gap: var(--spacing-1);
        padding: var(--spacing-2);
    }

    .grid-item {
        height: 20px;
        padding: 0 2px;
        font-size: 10px;
        border-width: 1px;
        width: fit-content;
        display: inline-flex;
        white-space: nowrap;
        flex: none;
        box-sizing: border-box;
        min-width: 0;
    }

    .compact-inline-layout {
        gap: 1px;
        padding: 1px;
        justify-content: flex-start;
        width: 100%;
        box-sizing: border-box;
        min-height: 20px;
    }

    /* 确保字段在小屏幕上正确换行 */
    .order-card-body {
        overflow: hidden;
    }

    /* 移动端文本溢出处理 */
    .grid-value {
        max-width: 100px;
        font-size: inherit;
    }

    .grid-item-route .grid-value {
        max-width: 80px;
    }
}

/* =================================
   平板端优化 (768px及以下)
   ================================= */
@media (max-width: 768px) {
    /* 响应式布局优化 - 保持水平排列但优化间距 */
    .dropdown-arrow {
        font-size: 12px;
        transition: transform var(--transition-fast) ease;
    }

    .btn-create-all {
        background: var(--color-success-gradient);
        color: var(--color-white);
        grid-column: 1 / -1; /* 跨两列 */
    }
    .btn-select-all {
        background: var(--color-info-gradient);
        color: var(--color-white);
    }
    .btn-clear-all {
        background: var(--color-error-gradient);
        color: var(--color-white);
    }

    /* 移动端网格布局调整 - 优化高度和间距以确保4张卡片完整显示 */
    .multi-order-list {
        grid-template-columns: 1fr 1fr;
        gap: 1px; /* 压缩间距节省空间 */
        padding: 1px; /* 压缩内边距节省空间 */
        max-height: calc(100vh - 200px); /* 增加减去值，为4张卡片预留更多空间 */
    }

    /* Footer响应式设计 */
    .footer-actions-row {
        justify-content: center;
        gap: 2px;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 4px;
        font-size: 0.8rem; /* 进一步减小 */
    }
    
    .footer-count {
        font-size: 0.8rem;
        order: -1; /* 将计数移到前面 */
        flex: 1;
        text-align: center;
    }

    /* 移动端按钮优化 - 已移除 */

    .btn-icon {
        font-size: 18px;
    }

    /* 移动端专用增强样式 */
    /* 移动端增强样式可在此添加 */

    /* 移动端关闭按钮优化 */
    .multi-order-close-btn {
        top: 12px;
        right: 12px;
        width: 44px;
        height: 44px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.6);
        -webkit-backdrop-filter: var(--blur-glass);
        backdrop-filter: var(--blur-glass);
        border: 2px solid var(--button-overlay-medium);
    }

    .multi-order-close-btn:active {
        transform: scale(0.95);
        background: var(--shadow-darkest);
    }

    /* 订单流式布局优化 - 已删除重复定义，使用第21-28行的统一定义 */

    .order-grid-left,
    .order-grid-right {
        gap: 1px; /* 最小间距 */
    }

    /* .grid-item 样式已删除重复定义，使用第8-19行的统一定义 */

    .grid-label {
        font-size: 14px; /* 减小图标字体 */
        min-width: 20px; /* 减小最小宽度 */
    }

    .grid-value {
        font-size: 12px; /* 减小值字体 */
        font-weight: 500;
        line-height: 1.2; /* 紧凑行高 */
    }

    .route-display {
        gap: var(--spacing-2);
    }

    .pickup-address,
    .dropoff-address {
        font-size: var(--font-sm);
        gap: var(--spacing-2);
    }

    .address-label {
        min-width: 36px;
        font-size: var(--font-sm);
    }

    /* 卡片内容紧凑化 */
    .order-card {
        min-height: 0;
    }

    .order-card-header {
        padding: 2px 4px;
        min-height: 24px;
    }

    .order-card-body {
        padding: 2px 4px;
    }

    .status-badge,
    .paging-badge {
        padding: 0px 2px;
        font-size: 8px;
        line-height: 1.0;
        height: 16px;
    }

    .order-checkbox {
        width: 10px;
        height: 10px;
    }

    .order-number {
        font-size: 12px;
        font-weight: 500;
    }
}

/* =================================
   平板端特定优化 (768px-481px)
   ================================= */
@media (max-width: 768px) and (min-width: 481px) {
    .multi-order-panel {
        padding: 12px;
        justify-content: flex-start;
        padding-top: 40px;
    }

    /* 移动端底部操作栏优化 */
    .footer-actions-row {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-2);
    }

    .footer-actions-left,
    .footer-actions-right {
        width: 100%;
        justify-content: center;
    }

    .footer-actions-center {
        order: -1; /* 将计数器移到顶部 */
    }
    
    .multi-order-content {
        width: 95vw;
        margin: 0 auto;
    }
}

/* =================================
   横屏模式优化
   ================================= */
@media (max-width: 768px) and (orientation: landscape) {
    .multi-order-panel {
        padding: 8px 16px;
    }
}

/* =================================
   大屏手机端优化 (480px及以下)
   ================================= */
@media (max-width: 480px) {
    /* 网格布局调整 - 大屏手机端优化 */
    .multi-order-list {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
        max-height: calc(100vh - 160px); /* 增加减去值，确保4张卡片显示空间 */
    }

    /* Footer响应式设计 */
    .multi-order-footer {
        padding: 2px 4px;
        min-height: 20px;
    }
    
    .footer-actions-row {
        gap: 1px;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 3px;
        font-size: 0.75rem;
        min-width: auto;
    }
    
    .footer-count {
        font-size: 0.75rem;
        width: 100%;
        text-align: center;
        margin-bottom: 2px;
    }

    /* 移动端按钮优化 - 已移除 */

    /* 订单网格布局优化 - 大屏手机端紧凑设计 */
    .order-grid-layout {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
    }

    /* .grid-item 样式已删除重复定义，使用第8-19行的统一定义 */

    .grid-label {
        font-size: 15px; /* 减小字体 */
        min-width: 24px; /* 减小最小宽度 */
    }

    .grid-value {
        font-size: 12px; /* 减小字体 */
        line-height: 1.3; /* 紧凑行高 */
    }

    .edit-indicator {
        font-size: 12px; /* 减小字体 */
    }
}

/* =================================
   大屏手机端特定优化 (480px-376px)
   ================================= */
@media (max-width: 480px) and (min-width: 376px) {
    .multi-order-panel {
        padding: 8px;
        justify-content: flex-start;
        padding-top: 30px;
    }

    .multi-order-content {
        width: 96vw;
        margin: 0 auto;
    }
}

/* =================================
   小屏手机端优化 (375px及以下)
   ================================= */
@media (max-width: 375px) {
    /* 网格布局调整 - 小屏手机端极致优化 */
    .multi-order-list {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
        max-height: calc(100vh - 140px); /* 增加减去值，确保4张卡片显示 */
    }

    /* 移动端按钮优化 - 已移除 */

    /* 小屏手机端面板优化 */
    .multi-order-panel {
        padding: 6px;
        justify-content: flex-start;
        padding-top: 20px;
    }

    .multi-order-content {
        width: 98vw;
        margin: 0 auto;
    }

    /* 移动端关闭按钮优化 */
    .multi-order-close-btn {
        top: 8px;
        right: 8px;
        width: 48px;
        height: 48px;
        font-size: 22px;
    }

    /* 订单网格布局优化 - 小屏手机端紧凑设计 */
    .order-grid-layout {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
        border-radius: 4px; /* 减小圆角 */
    }

    /* .grid-item 样式已删除重复定义，使用第8-19行的统一定义 */

    .grid-label {
        font-size: 16px; /* 减小字体 */
        min-width: 28px; /* 减小最小宽度 */
    }

    .grid-value {
        font-size: 13px; /* 减小字体 */
        font-weight: 500; /* 减轻字重 */
        line-height: 1.2; /* 更紧凑的行高 */
    }

    .route-display {
        gap: 1px; /* 最小间距节省空间 */
    }

    .pickup-address,
    .dropoff-address {
        font-size: 11px; /* 减小字体 */
        min-height: 16px; /* 减小最小高度 */
        align-items: flex-start;
        padding: 1px 0; /* 减小内边距 */
    }

    .address-label {
        min-width: 32px; /* 减小最小宽度 */
        font-size: 11px; /* 减小字体 */
        font-weight: 600; /* 减轻字重 */
    }

    .address-text {
        font-size: 11px; /* 减小字体 */
        line-height: 1.1; /* 更紧凑的行高 */
    }
}

/* =================================
   极小屏幕优化 (320px及以下) - 最大化空间利用
   ================================= */
@media (max-width: 320px) {
    /* 网格布局调整 - 极小屏幕最大化优化 */
    .multi-order-list {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
        max-height: calc(100vh - 120px); /* 为4张卡片预留最大空间 */
    }

    /* 面板优化 */
    .multi-order-panel {
        padding: 2px; /* 进一步减小内边距 */
    }

    /* 按钮优化 - 已移除 */

    /* 订单网格布局优化 - 极致紧凑 */
    .order-grid-layout {
        padding: 1px; /* 最小内边距 */
        gap: 1px; /* 最小间距 */
    }

    /* .grid-item 样式已删除重复定义，使用第8-19行的统一定义 */

    .grid-label {
        font-size: 17px; /* 减小字体 */
        min-width: 30px; /* 减小最小宽度 */
    }

    .grid-value {
        font-size: 14px; /* 减小字体 */
        font-weight: 600; /* 适中字重 */
        line-height: 1.1; /* 最紧凑行高 */
    }

    /* 地址显示优化 */
    .pickup-address,
    .dropoff-address {
        font-size: 10px; /* 最小可读字体 */
        min-height: 14px; /* 最小高度 */
        padding: 0; /* 无内边距 */
    }

    .address-label {
        min-width: 28px; /* 最小宽度 */
        font-size: 10px; /* 最小字体 */
        font-weight: 600;
    }

    .address-text {
        font-size: 10px; /* 最小字体 */
        line-height: 1.0; /* 最紧凑行高 */
    }
}

/* =================================
   高对比度模式支持
   ================================= */
@media (prefers-contrast: high) {
    /* 高对比度样式可在此添加 */
    .order-card {
        border: 2px solid var(--text-primary);
        background: var(--color-white);
    }

    .order-card:hover {
        border-color: var(--color-primary);
        background: var(--color-primary-bg);
    }

    .multi-order-close-btn {
        border: 3px solid var(--text-primary);
        background: var(--color-white);
        color: var(--text-primary);
    }
}

/* =================================
   地址显示优化 - 分行独立显示和内联编辑支持
   ================================= */

/* 地址显示基础样式 - 所有断点通用 */
.route-display {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%;
}

.pickup-address,
.dropoff-address {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    padding: 2px 0;
    border-radius: 3px;
    transition: all var(--transition-fast);
    cursor: pointer;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
}

/* 简化的地址编辑样式 */
.pickup-address.editable,
.dropoff-address.editable {
    background: var(--brand-overlay-minimal);
    border: 1px solid var(--border-color);
    padding: 2px 4px;
}

.pickup-address.editable:hover,
.dropoff-address.editable:hover {
    background: var(--brand-overlay-subtle);
    border-color: var(--color-primary);
}

.address-label {
    font-weight: 600;
    color: var(--color-primary);
    flex-shrink: 0;
    white-space: nowrap;
}

.address-text {
    flex: 1;
    color: var(--text-secondary);
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
}

/* 删除过度的编辑指示器样式 */

/* 地址编辑输入框样式 */
.address-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: inherit;
    font-family: inherit;
    outline: none;
    padding: 0;
    margin: 0;
    word-wrap: break-word;
    word-break: break-word;
}

/* =================================
   通用移动端优化 - 确保4张卡片完整显示
   ================================= */

/* 重复的768px媒体查询已合并到上面 */

/* =================================
   底部操作栏重新设计 - 严格单行显示
   ================================= */

/* 简化的底部操作栏样式 */
.multi-order-footer {
    background: var(--color-primary);
    border-top: 1px solid var(--button-overlay-medium);
    padding: 8px;
}

.footer-actions-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
    overflow-x: auto;
}

.btn-footer,
.btn-footer-primary {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 4px;
    white-space: nowrap;
    min-height: 44px;
    min-width: 80px;
    cursor: pointer;
}

.btn-footer-primary {
    background: var(--button-overlay-strong);
    color: var(--color-primary);
    font-weight: 600;
    min-width: 100px;
}

.footer-count {
    color: var(--color-white);
    font-size: 13px;
    padding: 8px 12px;
    background: var(--button-overlay-light);
    border-radius: 4px;
    min-height: 44px;
    margin-left: auto;
}

/* 删除重复的768px媒体查询，已在上面合并 */

/* 删除重复的480px媒体查询，已在上面合并 */

/* 删除重复的375px媒体查询，已在上面合并 */

/* =================================
   批量操作控件样式
   ================================= */

/* 简化的批量操作样式 */
.batch-controls {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.batch-controls-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-accent);
}

.batch-info-text {
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
    margin-left: 8px;
}

.batch-dropdown-btn,
.batch-action-btn {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    min-width: 80px;
}

.batch-dropdown-btn:hover,
.batch-action-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

/* 删除重复的768px媒体查询，已在上面合并 */

/* 删除重复的480px媒体查询，已在上面合并 */

/* =================================
   扩展订单信息字段样式
   ================================= */

/* 扩展字段容器 - 支持更多字段显示 */
.order-grid-layout.extended {
    grid-template-columns: 1fr 1fr 1fr; /* 三列布局容纳更多字段 */
    gap: 1px;
}

/* 删除过度装饰的渐变背景，使用简单的背景色 */

/* 删除过度的字段值颜色装饰，使用统一样式 */

/* 删除重复的768px媒体查询，已在上面合并 */

/* 删除重复的480px媒体查询，已在上面合并 */

/* 删除重复的375px媒体查询，已在上面合并 */

/* =================================
   订单卡片字段样式 - 减少字体大小15%
   ================================= */

/* 订单卡片基础样式 */
.order-card {
    font-size: calc(var(--font-sm) * 0.85); /* 减少15%字体大小 */
    line-height: 1.4;
}

/* 订单头部 */
.order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.order-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: calc(var(--font-sm) * 0.85);
}

/* 订单详情区域 */
.order-details {
    padding: 10px 12px;
    display: flex;
    flex-direction: row; /* 横向流式 */
    flex-wrap: wrap;
    gap: 1px;
}

/* 客户信息行 */
.order-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.order-customer {
    font-weight: 600;
    color: var(--text-primary);
    font-size: calc(var(--font-sm) * 0.85);
}

.order-contact {
    color: var(--text-secondary);
    font-size: calc(var(--font-xs) * 0.85);
}

/* 路线信息行 */
.order-route {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
}

.order-pickup,
.order-dropoff {
    color: var(--text-primary);
    font-size: calc(var(--font-xs) * 0.85);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.order-arrow {
    color: var(--text-accent);
    font-weight: bold;
    margin: 0 2px;
}

/* 时间和价格信息行 */
.order-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.order-date,
.order-time {
    color: var(--text-secondary);
    font-size: calc(var(--font-xs) * 0.85);
}

.order-price {
    color: var(--color-success);
    font-weight: 600;
    font-size: calc(var(--font-xs) * 0.85);
}

/* 附加信息行 (OTA, 车型, Region) */
.order-additional {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 4px;
    padding-top: 6px;
    border-top: 1px solid var(--border-light);
}

.order-ota,
.order-vehicle,
.order-region {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: calc(var(--font-xs) * 0.85);
    font-weight: 500;
    white-space: nowrap;
}

.order-ota {
    background: var(--brand-glass);
    color: var(--color-primary);
    border: 1px solid var(--color-primary-light);
}

.order-vehicle {
    background: var(--bg-info-subtle);
    color: var(--color-info);
    border: 1px solid var(--color-info-light);
}

.order-region {
    background: var(--bg-warning-subtle);
    color: var(--color-warning);
    border: 1px solid var(--color-warning-light);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .order-details {
        padding: 8px 10px;
        flex-direction: row; /* 保持横向 */
        flex-wrap: wrap;
        gap: 1px;
    }
    
    .order-pickup,
    .order-dropoff {
        max-width: 100px;
    }
    
    .order-additional {
        gap: 4px;
    }
    
    .order-ota,
    .order-vehicle,
    .order-region {
        font-size: calc(var(--font-xs) * 0.8); /* 移动端进一步减小 */
        padding: 1px 4px;
    }
}

@media (max-width: 480px) {
    .order-card {
        font-size: calc(var(--font-xs) * 0.85);
    }
    
    .order-additional {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .order-ota,
    .order-vehicle,
    .order-region {
        font-size: calc(var(--font-xs) * 0.75);
    }
}
