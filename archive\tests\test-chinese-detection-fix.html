<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文检测机制修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-input {
            margin: 10px 0;
        }
        
        .test-input input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .language-checkboxes {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .language-checkboxes label {
            display: block;
            margin: 5px 0;
            cursor: pointer;
        }
        
        .language-checkboxes input[type="checkbox"] {
            margin-right: 8px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status-display {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 中文检测机制修复验证</h1>
        
        <div class="test-section">
            <h3>📋 问题描述</h3>
            <p>发现中文勾选机制没有如预期体现，经分析发现统一语言检测器脚本文件未被包含在主页面中。</p>
            
            <div class="test-result info">
                <strong>修复措施：</strong> 已在 index.html 中添加 <code>js/core/language-detector.js</code> 脚本引用
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 功能测试</h3>
            
            <div class="test-input">
                <label for="testCustomerName">客户姓名测试：</label>
                <input type="text" id="testCustomerName" placeholder="输入客户姓名进行测试...">
            </div>
            
            <div class="test-input">
                <label for="testExtraRequirement">额外要求测试：</label>
                <input type="text" id="testExtraRequirement" placeholder="输入额外要求进行测试...">
            </div>
            
            <div class="language-checkboxes">
                <label>
                    <input type="checkbox" id="lang_2" name="languagesIdArray" value="2">
                    English (EN)
                </label>
                <label>
                    <input type="checkbox" id="lang_4" name="languagesIdArray" value="4">
                    Chinese (CN)
                </label>
                <label>
                    <input type="checkbox" id="lang_5" name="languagesIdArray" value="5" style="display: none;">
                    Paging (PG)
                </label>
                <label>
                    <input type="checkbox" id="lang_6" name="languagesIdArray" value="6">
                    Charter (CHARTER)
                </label>
            </div>
            
            <div>
                <button onclick="testChineseText()">测试中文文本</button>
                <button onclick="testEnglishText()">测试英文文本</button>
                <button onclick="testMixedText()">测试中英混合</button>
                <button onclick="clearTest()">清除测试</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 系统状态检查</h3>
            <div id="systemStatus"></div>
        </div>
    </div>

    <!-- 模拟主系统的脚本加载 -->
    <script>
        // 模拟OTA命名空间
        window.OTA = window.OTA || {};
        
        // 模拟基础服务
        window.getLogger = function() {
            return {
                log: function(message, level, data) {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                },
                logError: function(message, error) {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        };

        window.getService = function(serviceName) {
            if (serviceName === 'formManager') {
                return {
                    setLanguageSelection: function(languageIds) {
                        console.log('FormManager.setLanguageSelection called with:', languageIds);
                        setLanguageSelectionDirectly(languageIds);
                        return true;
                    }
                };
            }
            return null;
        };

        // 直接设置语言选择的方法
        function setLanguageSelectionDirectly(languageIds) {
            const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]');
            
            // 清除所有选择
            checkboxes.forEach(checkbox => checkbox.checked = false);
            
            // 设置指定的语言
            languageIds.forEach(id => {
                const checkbox = document.getElementById(`lang_${id}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            updateTestResults(`已设置语言选择: [${languageIds.join(', ')}]`, 'info');
        }

        // 测试函数
        function testChineseText() {
            const testTexts = ['张三', '农玉琴', '需要中文司机', '接机服务'];
            testTexts.forEach((text, index) => {
                setTimeout(() => {
                    if (index === 0) {
                        document.getElementById('testCustomerName').value = text;
                        document.getElementById('testCustomerName').dispatchEvent(new Event('input', { bubbles: true }));
                    } else {
                        document.getElementById('testExtraRequirement').value = text;
                        document.getElementById('testExtraRequirement').dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }, index * 500);
            });
        }

        function testEnglishText() {
            const testTexts = ['John Smith', 'Mary Johnson', 'Airport pickup service', 'English speaking driver'];
            testTexts.forEach((text, index) => {
                setTimeout(() => {
                    if (index === 0) {
                        document.getElementById('testCustomerName').value = text;
                        document.getElementById('testCustomerName').dispatchEvent(new Event('input', { bubbles: true }));
                    } else {
                        document.getElementById('testExtraRequirement').value = text;
                        document.getElementById('testExtraRequirement').dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }, index * 500);
            });
        }

        function testMixedText() {
            const testTexts = ['Mary Wang', 'John 李明', 'Airport 机场', 'English driver 请安排'];
            testTexts.forEach((text, index) => {
                setTimeout(() => {
                    if (index === 0) {
                        document.getElementById('testCustomerName').value = text;
                        document.getElementById('testCustomerName').dispatchEvent(new Event('input', { bubbles: true }));
                    } else {
                        document.getElementById('testExtraRequirement').value = text;
                        document.getElementById('testExtraRequirement').dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }, index * 500);
            });
        }

        function clearTest() {
            document.getElementById('testCustomerName').value = '';
            document.getElementById('testExtraRequirement').value = '';
            document.querySelectorAll('input[name="languagesIdArray"]').forEach(cb => cb.checked = false);
            document.getElementById('testResults').innerHTML = '';
        }

        function updateTestResults(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
        }

        function checkSystemStatus() {
            const status = document.getElementById('systemStatus');
            const checks = [];
            
            // 检查OTA命名空间
            checks.push({
                name: 'OTA命名空间',
                status: window.OTA ? '✅ 已创建' : '❌ 未创建',
                success: !!window.OTA
            });
            
            // 检查统一语言检测器
            checks.push({
                name: '统一语言检测器',
                status: window.OTA?.unifiedLanguageDetector ? '✅ 已初始化' : '❌ 未初始化',
                success: !!window.OTA?.unifiedLanguageDetector
            });
            
            // 检查语言复选框
            const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]');
            checks.push({
                name: '语言复选框',
                status: checkboxes.length > 0 ? `✅ 找到 ${checkboxes.length} 个` : '❌ 未找到',
                success: checkboxes.length > 0
            });
            
            // 检查中文复选框
            const chineseCheckbox = document.getElementById('lang_4');
            checks.push({
                name: '中文复选框 (lang_4)',
                status: chineseCheckbox ? '✅ 存在' : '❌ 不存在',
                success: !!chineseCheckbox
            });
            
            status.innerHTML = checks.map(check => `
                <div class="status-display">
                    <strong>${check.name}:</strong> ${check.status}
                </div>
            `).join('');
            
            // 显示总体状态
            const allSuccess = checks.every(check => check.success);
            updateTestResults(`系统状态检查完成 - ${allSuccess ? '全部正常' : '发现问题'}`, allSuccess ? 'success' : 'warning');
        }

        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', () => {
            checkSystemStatus();
            
            // 定期检查统一语言检测器状态
            const checkDetector = setInterval(() => {
                if (window.OTA?.unifiedLanguageDetector) {
                    updateTestResults('统一语言检测器已加载', 'success');
                    clearInterval(checkDetector);
                    
                    // 模拟一些测试
                    setTimeout(() => {
                        updateTestResults('开始自动测试...', 'info');
                        testChineseText();
                    }, 1000);
                }
            }, 500);
            
            // 10秒后仍未加载则报告问题
            setTimeout(() => {
                if (!window.OTA?.unifiedLanguageDetector) {
                    updateTestResults('统一语言检测器加载超时 - 可能存在问题', 'error');
                    clearInterval(checkDetector);
                }
            }, 10000);
        });
    </script>

    <!-- 引入统一语言检测器 -->
    <script src="js/core/language-detector.js"></script>
</body>
</html>
