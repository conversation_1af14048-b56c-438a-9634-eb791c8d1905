<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单界面显示问题调试测试</title>
    
    <!-- 加载必需的CSS -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <link rel="stylesheet" href="css/multi-order/mobile.css">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🧪 多订单界面显示问题调试测试</h1>
    
    <div class="test-controls">
        <h3>测试控制面板</h3>
        
        <button class="test-button" onclick="runSystemCheck()">1️⃣ 系统检查</button>
        <button class="test-button" onclick="testPanelDisplay()">2️⃣ 面板显示测试</button>
        <button class="test-button" onclick="testModuleLoading()">3️⃣ 模块加载测试</button>
        <button class="test-button" onclick="testCSSVariables()">4️⃣ CSS变量测试</button>
        <button class="test-button" onclick="simulateMultiOrder()">5️⃣ 模拟多订单</button>
        <button class="test-button" onclick="forceShowPanel()">🔧 强制显示面板</button>
        <button class="test-button" onclick="clearDebugInfo()">🗑️ 清除日志</button>
    </div>
    
    <div id="debugInfo" class="debug-info">
        点击上方按钮开始测试...
    </div>
    
    <!-- 多订单面板 - 从原始index.html复制 -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-content">
            <div class="multi-order-header">
                <div class="header-left">
                    <button type="button" id="backToMainBtn" class="btn btn-header-back" title="返回主页">←返回主页</button>
                    <h3 data-i18n="multiOrder.title">📦多订单管理</h3>
                </div>
                <div class="multi-order-controls">
                    <div class="header-actions">
                        <button type="button" id="batchCreateBtn" class="btn btn-header-action" data-i18n="multiOrder.batchCreate">⚙️批量操作</button>
                        <button type="button" id="closeMultiOrderBtn" class="btn btn-icon btn-close" data-i18n-title="common.close" title="关闭">✕</button>
                    </div>
                </div>
            </div>

            <!-- 简化版批量操作控件 -->
            <div class="batch-controls">
                <span class="batch-controls-label">批量设置:</span>
                <select id="batchLanguageSelect" class="batch-dropdown-btn">
                    <option value="">选择语言</option>
                    <option value="2">英文</option>
                    <option value="3">马来文</option>
                    <option value="4">中文</option>
                    <option value="5">举牌</option>
                </select>
                <button type="button" id="applyBatchBtn" class="batch-action-btn">应用设置</button>
            </div>

            <div class="multi-order-list" id="multiOrderList">
                <!-- 多订单项将在这里动态生成 -->
            </div>

            <!-- 底部操作栏 -->
            <div class="multi-order-footer">
                <div class="footer-actions-row">
                    <div class="footer-actions-left">
                        <button type="button" id="selectAllOrdersBtn" class="btn btn-footer btn-sm">全选</button>
                        <button type="button" id="deselectAllOrdersBtn" class="btn btn-footer btn-sm">取消全选</button>
                    </div>
                    <div class="footer-actions-center">
                        <span id="selectedOrderCount" class="footer-count">已选择 0 个订单</span>
                    </div>
                    <div class="footer-actions-right">
                        <button type="button" id="createSelectedOrdersBtn" class="btn btn-footer-primary">创建选中订单</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心JavaScript模块 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    
    <script>
        // 调试信息输出函数
        function addDebugInfo(message, type = 'info') {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            const typeClass = type;
            const newLine = `[${timestamp}] <span class="${typeClass}">[${type.toUpperCase()}]</span> ${message}\n`;
            debugDiv.innerHTML += newLine;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // 清除调试信息
        function clearDebugInfo() {
            document.getElementById('debugInfo').innerHTML = '调试信息已清除...\n';
        }
        
        // 1. 系统检查
        function runSystemCheck() {
            addDebugInfo('开始系统检查...', 'info');
            
            // 检查多订单面板元素
            const panel = document.getElementById('multiOrderPanel');
            addDebugInfo(`面板元素存在: ${!!panel}`, panel ? 'success' : 'error');
            
            if (panel) {
                addDebugInfo(`面板类名: ${panel.className}`, 'info');
                addDebugInfo(`面板样式显示: ${panel.style.display}`, 'info');
                addDebugInfo(`面板计算样式显示: ${window.getComputedStyle(panel).display}`, 'info');
                addDebugInfo(`面板可见性: ${panel.offsetHeight > 0 ? '可见' : '隐藏'}`, panel.offsetHeight > 0 ? 'success' : 'warning');
            }
            
            // 检查OTA命名空间
            addDebugInfo(`window.OTA 存在: ${!!window.OTA}`, window.OTA ? 'success' : 'error');
            if (window.OTA) {
                const otaKeys = Object.keys(window.OTA);
                addDebugInfo(`OTA 模块: [${otaKeys.join(', ')}]`, 'info');
            }
            
            // 检查CSS变量
            const rootStyle = getComputedStyle(document.documentElement);
            const zToast = rootStyle.getPropertyValue('--z-toast');
            const overlayBackdrop = rootStyle.getPropertyValue('--overlay-backdrop');
            
            addDebugInfo(`CSS变量 --z-toast: '${zToast}'`, zToast ? 'success' : 'warning');
            addDebugInfo(`CSS变量 --overlay-backdrop: '${overlayBackdrop}'`, overlayBackdrop ? 'success' : 'warning');
            
            addDebugInfo('系统检查完成', 'success');
        }
        
        // 2. 面板显示测试
        function testPanelDisplay() {
            addDebugInfo('开始面板显示测试...', 'info');
            
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) {
                addDebugInfo('面板元素不存在，无法测试', 'error');
                return;
            }
            
            // 记录初始状态
            addDebugInfo(`初始状态 - 类名: ${panel.className}`, 'info');
            addDebugInfo(`初始状态 - 显示: ${panel.style.display}`, 'info');
            
            // 尝试标准显示方法
            panel.classList.remove('hidden');
            panel.style.display = 'flex';
            
            addDebugInfo(`标准方法后 - 类名: ${panel.className}`, 'info');
            addDebugInfo(`标准方法后 - 显示: ${panel.style.display}`, 'info');
            addDebugInfo(`标准方法后 - 可见: ${panel.offsetHeight > 0}`, panel.offsetHeight > 0 ? 'success' : 'warning');
            
            // 等待一下再检查
            setTimeout(() => {
                addDebugInfo(`1秒后检查 - 可见: ${panel.offsetHeight > 0}`, panel.offsetHeight > 0 ? 'success' : 'warning');
                addDebugInfo(`1秒后检查 - Z-index: ${window.getComputedStyle(panel).zIndex}`, 'info');
            }, 1000);
            
            addDebugInfo('面板显示测试完成', 'success');
        }
        
        // 3. 模块加载测试
        function testModuleLoading() {
            addDebugInfo('开始模块加载测试...', 'info');
            
            const requiredModules = [
                'multiOrderCoordinator',
                'multiOrderStateManager', 
                'batchProcessor',
                'multiOrderDetector',
                'multiOrderProcessor',
                'multiOrderRenderer'
            ];
            
            requiredModules.forEach(moduleName => {
                const exists = window.OTA?.[moduleName] !== undefined;
                addDebugInfo(`模块 ${moduleName}: ${exists ? '已加载' : '未加载'}`, exists ? 'success' : 'error');
            });
            
            // 检查管理器
            addDebugInfo(`multiOrderManager: ${!!window.OTA?.multiOrderManager}`, window.OTA?.multiOrderManager ? 'success' : 'error');
            addDebugInfo(`multiOrderManagerV2: ${!!window.OTA?.multiOrderManagerV2}`, window.OTA?.multiOrderManagerV2 ? 'success' : 'error');
            
            addDebugInfo('模块加载测试完成', 'success');
        }
        
        // 4. CSS变量测试
        function testCSSVariables() {
            addDebugInfo('开始CSS变量测试...', 'info');
            
            const rootStyle = getComputedStyle(document.documentElement);
            const criticalVars = [
                '--z-toast',
                '--overlay-backdrop', 
                '--bg-tertiary',
                '--border-color',
                '--color-primary',
                '--brand-gradient'
            ];
            
            criticalVars.forEach(varName => {
                const value = rootStyle.getPropertyValue(varName);
                addDebugInfo(`${varName}: '${value}'`, value.trim() ? 'success' : 'warning');
            });
            
            addDebugInfo('CSS变量测试完成', 'success');
        }
        
        // 5. 模拟多订单
        function simulateMultiOrder() {
            addDebugInfo('开始模拟多订单测试...', 'info');
            
            const mockOrders = [
                {
                    customerName: '张三',
                    pickup: 'KLCC',
                    dropoff: 'KL Sentral',
                    pickupDate: '2024-01-15',
                    pickupTime: '10:00',
                    customerContact: '+60123456789',
                    passengerCount: 2,
                    luggageCount: 1,
                    otaPrice: 35.50,
                    currency: 'MYR',
                    carTypeId: 5
                },
                {
                    customerName: '李四',
                    pickup: 'KLIA2',
                    dropoff: 'Bukit Bintang',
                    pickupDate: '2024-01-15',
                    pickupTime: '14:30',
                    customerContact: '+60187654321',
                    passengerCount: 1,
                    luggageCount: 2,
                    otaPrice: 45.00,
                    currency: 'MYR',
                    carTypeId: 15
                }
            ];
            
            // 生成订单HTML
            const orderList = document.getElementById('multiOrderList');
            if (orderList) {
                orderList.innerHTML = mockOrders.map((order, index) => `
                    <div class="order-card" data-order-index="${index}">
                        <div class="order-card-header">
                            <div class="order-selector">
                                <input type="checkbox" id="order-${index}" checked class="order-checkbox">
                                <div class="order-title">
                                    <span class="order-number">订单 ${index + 1}</span>
                                </div>
                            </div>
                            <div class="order-status">
                                <span class="status-badge status-parsed">已解析</span>
                            </div>
                        </div>
                        <div class="order-card-body">
                            <div class="order-summary order-grid-layout">
                                <div class="order-grid-left">
                                    <div class="grid-item">
                                        <span class="grid-label">👤</span>
                                        <span class="grid-value">${order.customerName}</span>
                                    </div>
                                    <div class="grid-item grid-item-route">
                                        <span class="grid-label">📍</span>
                                        <div class="grid-value">
                                            <div class="route-display">
                                                <div class="pickup-address">
                                                    <span class="address-label">上车:</span>
                                                    <span class="address-text">${order.pickup}</span>
                                                </div>
                                                <div class="dropoff-address">
                                                    <span class="address-label">下车:</span>
                                                    <span class="address-text">${order.dropoff}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="order-grid-right">
                                    <div class="grid-item">
                                        <span class="grid-label">💰</span>
                                        <span class="grid-value">${order.currency}${order.otaPrice}</span>
                                    </div>
                                    <div class="grid-item">
                                        <span class="grid-label">👥</span>
                                        <span class="grid-value">${order.passengerCount}人</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                addDebugInfo(`已生成 ${mockOrders.length} 个模拟订单`, 'success');
            } else {
                addDebugInfo('订单列表容器不存在', 'error');
            }
            
            addDebugInfo('模拟多订单测试完成', 'success');
        }
        
        // 6. 强制显示面板
        function forceShowPanel() {
            addDebugInfo('强制显示面板...', 'info');
            
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) {
                addDebugInfo('面板元素不存在', 'error');
                return;
            }
            
            // 多重方法强制显示
            panel.classList.remove('hidden');
            panel.style.display = 'flex';
            panel.style.visibility = 'visible';
            panel.style.opacity = '1';
            panel.style.zIndex = '9999';
            panel.style.position = 'fixed';
            panel.style.top = '0';
            panel.style.left = '0';
            panel.style.width = '100vw';
            panel.style.height = '100vh';
            
            addDebugInfo('已应用强制显示样式', 'success');
            
            // 检查是否生效
            setTimeout(() => {
                const visible = panel.offsetHeight > 0;
                addDebugInfo(`强制显示结果: ${visible ? '成功' : '失败'}`, visible ? 'success' : 'error');
                if (visible) {
                    addDebugInfo('面板现在应该可见了！', 'success');
                }
            }, 100);
        }
        
        // 绑定关闭按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    const panel = document.getElementById('multiOrderPanel');
                    panel.classList.add('hidden');
                    panel.style.display = 'none';
                    addDebugInfo('面板已通过关闭按钮隐藏', 'info');
                });
            }
            
            addDebugInfo('测试页面已加载完成', 'success');
        });
    </script>
</body>
</html>