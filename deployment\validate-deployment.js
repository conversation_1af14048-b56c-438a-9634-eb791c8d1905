#!/usr/bin/env node

/**
 * 部署配置验证脚本
 * 用于验证 Netlify 部署配置的正确性
 */

const fs = require('fs');
const path = require('path');

class DeploymentValidator {
    constructor() {
        this.projectRoot = path.dirname(__dirname);
        this.errors = [];
        this.warnings = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    validateNetlifyConfig() {
        this.log('验证 Netlify 配置...');
        
        const netlifyConfigPath = path.join(this.projectRoot, 'netlify.toml');
        
        if (!fs.existsSync(netlifyConfigPath)) {
            this.errors.push('netlify.toml 文件不存在');
            return;
        }

        try {
            const config = fs.readFileSync(netlifyConfigPath, 'utf8');
            
            // 验证必要的配置项
            if (!config.includes('[build]')) {
                this.errors.push('netlify.toml 缺少 [build] 配置');
            }
            
            if (!config.includes('publish = "."')) {
                this.warnings.push('建议设置 publish 目录为当前目录');
            }
            
            // 验证重定向规则
            if (config.includes('conditions = {Role = ["admin"]}')) {
                this.errors.push('SPA 重定向规则有误，不应包含 admin 条件');
            }
            
            // 验证 CSP 策略
            if (!config.includes('Content-Security-Policy')) {
                this.warnings.push('建议配置 Content-Security-Policy');
            }
            
            this.log('Netlify 配置验证完成');
            
        } catch (error) {
            this.errors.push(`读取 netlify.toml 时出错: ${error.message}`);
        }
    }

    validatePackageJson() {
        this.log('验证 package.json...');
        
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        
        if (!fs.existsSync(packageJsonPath)) {
            this.errors.push('package.json 文件不存在');
            return;
        }

        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            if (!packageJson.scripts) {
                this.warnings.push('package.json 缺少 scripts 配置');
            } else {
                if (!packageJson.scripts.build) {
                    this.warnings.push('建议添加 build 脚本');
                }
                
                if (!packageJson.scripts.start) {
                    this.warnings.push('建议添加 start 脚本');
                }
            }
            
            this.log('package.json 验证完成');
            
        } catch (error) {
            this.errors.push(`解析 package.json 时出错: ${error.message}`);
        }
    }

    validateRequiredFiles() {
        this.log('验证必要文件...');
        
        const requiredFiles = [
            'index.html',
            'js/app-state.js',
            'js/gemini-service.js',
            'css/main.css'
        ];
        
        requiredFiles.forEach(file => {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                this.warnings.push(`建议的文件不存在: ${file}`);
            }
        });
        
        this.log('文件验证完成');
    }

    validateDirectoryStructure() {
        this.log('验证目录结构...');
        
        const requiredDirs = [
            'js',
            'css',
            'deployment',
            'docs'
        ];
        
        requiredDirs.forEach(dir => {
            const dirPath = path.join(this.projectRoot, dir);
            if (!fs.existsSync(dirPath)) {
                this.warnings.push(`建议的目录不存在: ${dir}`);
            }
        });
        
        this.log('目录结构验证完成');
    }

    generateReport() {
        this.log('生成验证报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            errors: this.errors,
            warnings: this.warnings,
            status: this.errors.length === 0 ? 'PASS' : 'FAIL'
        };
        
        const reportPath = path.join(this.projectRoot, 'deployment-validation-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        this.log(`验证报告已生成: ${reportPath}`);
        
        return report;
    }

    run() {
        this.log('开始部署配置验证...');
        
        this.validateNetlifyConfig();
        this.validatePackageJson();
        this.validateRequiredFiles();
        this.validateDirectoryStructure();
        
        const report = this.generateReport();
        
        this.log('验证完成');
        this.log(`错误: ${this.errors.length}`);
        this.log(`警告: ${this.warnings.length}`);
        
        if (this.errors.length > 0) {
            this.log('发现错误:', 'error');
            this.errors.forEach(error => this.log(`  - ${error}`, 'error'));
        }
        
        if (this.warnings.length > 0) {
            this.log('发现警告:', 'warning');
            this.warnings.forEach(warning => this.log(`  - ${warning}`, 'warning'));
        }
        
        return report.status === 'PASS';
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const validator = new DeploymentValidator();
    const success = validator.run();
    process.exit(success ? 0 : 1);
}

module.exports = DeploymentValidator;
