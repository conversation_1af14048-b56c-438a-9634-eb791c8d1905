<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>字段标准化层自检</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; margin: 0; padding: 24px; background: #f6f8fa; }
    .card { background: #fff; border: 1px solid #e1e4e8; border-radius: 8px; padding: 16px 20px; margin: 0 auto 16px; max-width: 980px; }
    h1 { margin: 0 0 8px; font-size: 20px; }
    .muted { color: #6a737d; font-size: 12px; }
    .row { display: flex; gap: 12px; flex-wrap: wrap; margin-top: 8px; }
    .pill { font-size: 12px; padding: 4px 10px; border-radius: 999px; border: 1px solid #e1e4e8; background: #fafbfc; }
    .ok { color: #1b7b1b; border-color: #b6e3b6; background: #eaf7ea; }
    .warn { color: #8a6d3b; border-color: #fae1a1; background: #fff8e1; }
    .err { color: #a94442; border-color: #f5c6cb; background: #f8d7da; }
    pre { background: #0b1020; color: #d5e4f3; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; }
    button { padding: 8px 12px; border: 1px solid #d1d5da; background: #fff; border-radius: 6px; cursor: pointer; }
    button:hover { background: #f3f4f6; }
    .actions { display: flex; gap: 8px; margin-top: 8px; }
  </style>
</head>
<body>
  <div class="card">
    <h1>🔍 字段标准化层自检</h1>
    <div class="muted">加载主站脚本后，自动检查拦截器安装状态，并本地验证字段映射。</div>
  </div>

  <div class="card" id="statusCard">
    <strong>安装状态</strong>
    <div class="row" id="statusRow">
      <span class="pill">等待脚本加载…</span>
    </div>
    <div class="actions">
      <button id="recheckBtn">重新检查</button>
      <button id="runLocalMappingBtn">运行本地映射测试</button>
      <a href="../index.html" target="_blank"><button>打开主页面</button></a>
    </div>
  </div>

  <div class="card">
    <strong>控制台提示</strong>
    <div class="muted">请同时打开浏览器控制台查看日志中是否出现：“✅ Gemini适配器拦截器已安装 (parseOrder)”或“✅ GeminiCaller拦截器已安装 (parseAPIResponse)”。</div>
  </div>

  <div class="card">
    <strong>本地映射测试输出</strong>
    <pre id="mappingOutput">等待运行…</pre>
  </div>

  <!-- 使用脚本加载器，保持与主站相同的加载顺序 -->
  <script src="../js/core/script-manifest.js"></script>
  <script src="../js/core/script-loader.js"></script>

  <script>
    function pill(text, cls = '') { const span = document.createElement('span'); span.className = 'pill ' + cls; span.textContent = text; return span; }

    function updateStatus() {
      const row = document.getElementById('statusRow');
      row.innerHTML = '';

      const layer = window.OTA?.globalFieldStandardizationLayer;
      const adapter = window.OTA?.geminiService;
      const caller = window.OTA?.geminiCaller;

      // 层是否存在
      if (layer) row.appendChild(pill('层实例: 存在', 'ok')); else row.appendChild(pill('层实例: 缺失', 'err'));
      // 初始化状态
      if (layer?.initialized) row.appendChild(pill('已初始化', 'ok')); else row.appendChild(pill('未初始化', 'warn'));

      // 适配器拦截状态
      const adapterHooked = !!(adapter && adapter._fieldStandardizationHooked);
      const adapterLegacyHooked = !!(adapter && adapter._fieldStandardizationHookedLegacy);
      if (adapter) row.appendChild(pill('Gemini适配器: 存在', 'ok')); else row.appendChild(pill('Gemini适配器: 缺失', 'warn'));
      row.appendChild(pill('parseOrder钩子: ' + (adapterHooked ? '已安装' : '未安装'), adapterHooked ? 'ok' : 'warn'));
      if (adapterLegacyHooked) row.appendChild(pill('parseOrdersFromText钩子: 已安装', 'ok'));

      // 调用器拦截状态
      const callerHooked = !!(caller && caller._fieldStandardizationHooked);
      if (caller) row.appendChild(pill('GeminiCaller: 存在', 'ok')); else row.appendChild(pill('GeminiCaller: 缺失', 'warn'));
      row.appendChild(pill('parseAPIResponse钩子: ' + (callerHooked ? '已安装' : '未安装'), callerHooked ? 'ok' : 'warn'));
    }

    async function runLocalMappingTest() {
      const out = document.getElementById('mappingOutput');
      const layer = window.OTA?.globalFieldStandardizationLayer;
      if (!layer) { out.textContent = '标准化层缺失，无法运行测试'; return; }

      // 使用本地样例（无需调用远端AI）
      const sample = {
        customerName: '张三',
        customerContact: '+86 13800138000',
        customerEmail: '<EMAIL>',
        pickup: '斗湖机场MAIN',
        dropoff: '市区酒店',
        pickupDate: '2025-08-11',
        pickupTime: '07:50',
        otaPrice: 120,
        currency: 'MYR',
        drivingRegion: 'KK',
        subCategoryId: 1,
        carTypeId: 2,
        extraRequirement: '需要儿童座椅',
        babyChair: true
      };

      const standardized = layer.standardizeToApiFields(sample, 'local-test');

      // 断言关键点：键存在（允许值为null）
      const mustKeys = [
        'customer_name','customer_contact','customer_email',
        'pickup','destination','date','time','ota_price','currency',
        'driving_region_id','sub_category_id','car_type_id','extra_requirement','baby_chair'
      ];
      const missing = mustKeys.filter(k => !(k in standardized));

      const result = {
        ok: missing.length === 0,
        missingKeys: missing,
        sampleInput: sample,
        standardized
      };

      out.textContent = JSON.stringify(result, null, 2);
      console.log('本地映射测试结果', result);
    }

    // 初次加载后稍等，让脚本完成初始化
    window.addEventListener('load', () => {
      setTimeout(() => { updateStatus(); }, 800);
    });

    document.getElementById('recheckBtn').addEventListener('click', updateStatus);
    document.getElementById('runLocalMappingBtn').addEventListener('click', runLocalMappingTest);
  </script>
</body>
</html>
